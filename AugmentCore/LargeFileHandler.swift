import CommonCrypto
// Import required frameworks
import CryptoKit
import Foundation
import os.log

/// Specialized handler for large file operations with streaming and progress tracking
/// Optimized for files >100MB with target processing time <10s for files up to 1GB
public class LargeFileHandler {

    // MARK: - Configuration

    /// File size threshold to consider a file "large" (100MB)
    public static let largeFileThreshold: Int64 = 100 * 1024 * 1024

    /// Maximum file size to process (5GB)
    public static let maximumFileSize: Int64 = 5 * 1024 * 1024 * 1024

    /// Chunk size for streaming operations (1MB)
    private static let chunkSize = 1024 * 1024

    /// Buffer size for I/O operations (64KB)
    private static let bufferSize = 64 * 1024

    // MARK: - Dependencies

    private let logger: AugmentLogger
    private let performanceMonitor: PerformanceMonitor
    private let fileManager = FileManager.default

    // MARK: - Progress Tracking

    public typealias ProgressHandler = (Double, String) -> Void
    public typealias CompletionHandler = (Result<LargeFileOperationResult, AugmentError>) -> Void

    // MARK: - Initialization

    public init(logger: AugmentLogger, performanceMonitor: PerformanceMonitor) {
        self.logger = logger
        self.performanceMonitor = performanceMonitor
    }

    // MARK: - File Size Analysis

    /// Determines if a file should be handled as a large file
    /// - Parameter url: The file URL to check
    /// - Returns: True if the file is considered large
    public func isLargeFile(_ url: URL) -> Bool {
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            let fileSize = attributes[.size] as? Int64 ?? 0
            return fileSize >= Self.largeFileThreshold
        } catch {
            logger.error(
                "Failed to get file size for \(url.path): \(error)", category: .performance)
            return false
        }
    }

    /// Gets file size efficiently without loading the file
    /// - Parameter url: The file URL
    /// - Returns: File size in bytes, or nil if unable to determine
    public func getFileSize(_ url: URL) -> Int64? {
        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            return attributes[.size] as? Int64
        } catch {
            logger.error(
                "Failed to get file size for \(url.path): \(error)", category: .performance)
            return nil
        }
    }

    // MARK: - Streaming File Operations

    /// Copies a large file with progress tracking and optimization
    /// - Parameters:
    ///   - sourceURL: Source file URL
    ///   - destinationURL: Destination file URL
    ///   - progressHandler: Progress callback (0.0 to 1.0)
    ///   - completionHandler: Completion callback
    public func copyLargeFile(
        from sourceURL: URL,
        to destinationURL: URL,
        progressHandler: @escaping ProgressHandler,
        completionHandler: @escaping CompletionHandler
    ) {
        let operationId = performanceMonitor.startOperation("LargeFileCopy")

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let result = try self.performStreamingCopy(
                    from: sourceURL,
                    to: destinationURL,
                    progressHandler: progressHandler
                )

                self.performanceMonitor.endOperation(operationId)
                DispatchQueue.main.async {
                    completionHandler(.success(result))
                }
            } catch {
                self.performanceMonitor.endOperation(operationId)
                let augmentError =
                    error as? AugmentError
                    ?? .fileOperationFailed(
                        operation: "copy",
                        path: sourceURL.path,
                        underlying: error
                    )
                DispatchQueue.main.async {
                    completionHandler(.failure(augmentError))
                }
            }
        }
    }

    /// Calculates file hash with streaming for large files
    /// - Parameters:
    ///   - url: File URL to hash
    ///   - algorithm: Hash algorithm to use
    ///   - progressHandler: Progress callback
    ///   - completionHandler: Completion callback with hash result
    public func calculateFileHash(
        for url: URL,
        algorithm: HashAlgorithm = .sha256,
        progressHandler: @escaping ProgressHandler,
        completionHandler: @escaping (Result<String, AugmentError>) -> Void
    ) {
        let operationId = performanceMonitor.startOperation("LargeFileHash")

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let hash = try self.performStreamingHash(
                    for: url,
                    algorithm: algorithm,
                    progressHandler: progressHandler
                )

                self.performanceMonitor.endOperation(operationId)
                DispatchQueue.main.async {
                    completionHandler(.success(hash))
                }
            } catch {
                self.performanceMonitor.endOperation(operationId)
                let augmentError =
                    error as? AugmentError
                    ?? .fileOperationFailed(
                        operation: "hash",
                        path: url.path,
                        underlying: error
                    )
                DispatchQueue.main.async {
                    completionHandler(.failure(augmentError))
                }
            }
        }
    }

    /// Reads file content in chunks for large files
    /// - Parameters:
    ///   - url: File URL to read
    ///   - chunkHandler: Called for each chunk of data
    ///   - completionHandler: Called when reading is complete
    public func readLargeFileInChunks(
        from url: URL,
        chunkHandler: @escaping (Data, Int64, Int64) -> Bool,  // data, offset, totalSize -> continue
        completionHandler: @escaping CompletionHandler
    ) {
        let operationId = performanceMonitor.startOperation("LargeFileRead")

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let result = try self.performChunkedRead(
                    from: url,
                    chunkHandler: chunkHandler
                )

                self.performanceMonitor.endOperation(operationId)
                DispatchQueue.main.async {
                    completionHandler(.success(result))
                }
            } catch {
                self.performanceMonitor.endOperation(operationId)
                let augmentError =
                    error as? AugmentError
                    ?? .fileOperationFailed(
                        operation: "read",
                        path: url.path,
                        underlying: error
                    )
                DispatchQueue.main.async {
                    completionHandler(.failure(augmentError))
                }
            }
        }
    }

    // MARK: - Private Implementation

    private func performStreamingCopy(
        from sourceURL: URL,
        to destinationURL: URL,
        progressHandler: @escaping ProgressHandler
    ) throws -> LargeFileOperationResult {

        guard let fileSize = getFileSize(sourceURL) else {
            throw AugmentError.fileOperationFailed(
                operation: "copy",
                path: sourceURL.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 1,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to determine file size"])
            )
        }

        guard fileSize <= Self.maximumFileSize else {
            throw AugmentError.fileTooLarge(
                path: sourceURL.path, size: fileSize, limit: Self.maximumFileSize)
        }

        let startTime = Date()

        // Create destination directory if needed
        try fileManager.createDirectory(
            at: destinationURL.deletingLastPathComponent(),
            withIntermediateDirectories: true
        )

        // Open source file for reading
        guard let sourceFile = FileHandle(forReadingAtPath: sourceURL.path) else {
            throw AugmentError.fileOperationFailed(
                operation: "copy",
                path: sourceURL.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 2,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to open source file"])
            )
        }
        defer { sourceFile.closeFile() }

        // Create destination file
        guard fileManager.createFile(atPath: destinationURL.path, contents: nil) else {
            throw AugmentError.fileOperationFailed(
                operation: "copy",
                path: destinationURL.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 3,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to create destination file"])
            )
        }

        guard let destinationFile = FileHandle(forWritingAtPath: destinationURL.path) else {
            throw AugmentError.fileOperationFailed(
                operation: "copy",
                path: destinationURL.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 4,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to open destination file"])
            )
        }
        defer { destinationFile.closeFile() }

        var totalBytesRead: Int64 = 0
        let buffer = Data(count: Self.bufferSize)

        while totalBytesRead < fileSize {
            autoreleasepool {
                let remainingBytes = fileSize - totalBytesRead
                let bytesToRead = min(Int64(Self.bufferSize), remainingBytes)

                let data = sourceFile.readData(ofLength: Int(bytesToRead))
                if data.isEmpty { return }

                destinationFile.write(data)
                totalBytesRead += Int64(data.count)

                let progress = Double(totalBytesRead) / Double(fileSize)
                let progressMessage =
                    "Copying: \(ByteCountFormatter.string(fromByteCount: totalBytesRead, countStyle: .file)) / \(ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file))"

                DispatchQueue.main.async {
                    progressHandler(progress, progressMessage)
                }
            }
        }

        let duration = Date().timeIntervalSince(startTime)
        let throughput = Double(fileSize) / duration / (1024 * 1024)  // MB/s

        logger.info(
            "Large file copy completed: \(sourceURL.lastPathComponent) (\(ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file))) in \(String(format: "%.2f", duration))s (\(String(format: "%.1f", throughput)) MB/s)",
            category: .performance)

        return LargeFileOperationResult(
            operation: .copy,
            fileSize: fileSize,
            duration: duration,
            throughput: throughput,
            success: true
        )
    }

    private func performStreamingHash(
        for url: URL,
        algorithm: HashAlgorithm,
        progressHandler: @escaping ProgressHandler
    ) throws -> String {

        guard let fileSize = getFileSize(url) else {
            throw AugmentError.fileOperationFailed(
                operation: "hash",
                path: url.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 1,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to determine file size"])
            )
        }

        guard let fileHandle = FileHandle(forReadingAtPath: url.path) else {
            throw AugmentError.fileOperationFailed(
                operation: "hash",
                path: url.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 2,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to open file"])
            )
        }
        defer { fileHandle.closeFile() }

        var hasher = algorithm.createHasher()
        var totalBytesRead: Int64 = 0

        while totalBytesRead < fileSize {
            let shouldContinue = autoreleasepool { () -> Bool in
                let remainingBytes = fileSize - totalBytesRead
                let bytesToRead = min(Int64(Self.chunkSize), remainingBytes)

                let data = fileHandle.readData(ofLength: Int(bytesToRead))
                if data.isEmpty { return false }

                hasher.update(data: data)
                totalBytesRead += Int64(data.count)

                let progress = Double(totalBytesRead) / Double(fileSize)
                let progressMessage = "Hashing: \(Int(progress * 100))%"

                DispatchQueue.main.async {
                    progressHandler(progress, progressMessage)
                }

                return true
            }

            if !shouldContinue { break }
        }

        return hasher.finalize()
    }

    private func performChunkedRead(
        from url: URL,
        chunkHandler: @escaping (Data, Int64, Int64) -> Bool
    ) throws -> LargeFileOperationResult {

        guard let fileSize = getFileSize(url) else {
            throw AugmentError.fileOperationFailed(
                operation: "read",
                path: url.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 1,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to determine file size"])
            )
        }

        guard let fileHandle = FileHandle(forReadingAtPath: url.path) else {
            throw AugmentError.fileOperationFailed(
                operation: "read",
                path: url.path,
                underlying: NSError(
                    domain: "LargeFileHandler", code: 2,
                    userInfo: [NSLocalizedDescriptionKey: "Unable to open file"])
            )
        }
        defer { fileHandle.closeFile() }

        let startTime = Date()
        var totalBytesRead: Int64 = 0

        while totalBytesRead < fileSize {
            let shouldContinue = autoreleasepool { () -> Bool in
                let remainingBytes = fileSize - totalBytesRead
                let bytesToRead = min(Int64(Self.chunkSize), remainingBytes)

                let data = fileHandle.readData(ofLength: Int(bytesToRead))
                if data.isEmpty { return false }

                let handlerResult = chunkHandler(data, totalBytesRead, fileSize)
                if !handlerResult { return false }

                totalBytesRead += Int64(data.count)
                return true
            }

            if !shouldContinue { break }
        }

        let duration = Date().timeIntervalSince(startTime)
        let throughput = Double(totalBytesRead) / duration / (1024 * 1024)  // MB/s

        return LargeFileOperationResult(
            operation: .read,
            fileSize: totalBytesRead,
            duration: duration,
            throughput: throughput,
            success: true
        )
    }
}

// MARK: - Supporting Types

/// Hash algorithms supported for large file operations
public enum HashAlgorithm {
    case sha256

    func createHasher() -> FileHasher {
        switch self {
        case .sha256:
            return SHA256FileHasher()
        }
    }
}

/// Protocol for streaming hash calculation
public protocol FileHasher {
    mutating func update(data: Data)
    func finalize() -> String
}

/// SHA256 hasher implementation
public struct SHA256FileHasher: FileHasher {
    private var hasher = SHA256()

    public mutating func update(data: Data) {
        hasher.update(data: data)
    }

    public func finalize() -> String {
        return hasher.finalize().compactMap { String(format: "%02x", $0) }.joined()
    }
}

/// Result of a large file operation
public struct LargeFileOperationResult {
    public let operation: LargeFileOperation
    public let fileSize: Int64
    public let duration: TimeInterval
    public let throughput: Double  // MB/s
    public let success: Bool

    public var formattedSummary: String {
        return
            "\(operation.rawValue.capitalized): \(ByteCountFormatter.string(fromByteCount: fileSize, countStyle: .file)) in \(String(format: "%.2f", duration))s (\(String(format: "%.1f", throughput)) MB/s)"
    }
}

/// Types of large file operations
public enum LargeFileOperation: String {
    case copy = "copy"
    case hash = "hash"
    case read = "read"
    case write = "write"
}
