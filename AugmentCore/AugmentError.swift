import Foundation

/// Comprehensive error taxonomy for the Augment application
/// Provides a unified error handling approach across all modules
public enum AugmentError: Error, LocalizedError, CustomStringConvertible {

    // MARK: - File System Errors
    case fileSystemMonitoringFailed(underlying: Error)
    case fileAccessDenied(path: String)
    case fileCorrupted(path: String, reason: String? = nil)
    case fileNotFound(path: String)
    case fileTooLarge(path: String, size: Int64, limit: Int64)
    case directoryCreationFailed(path: String, underlying: Error)
    case fileOperationFailed(operation: String, path: String, underlying: Error)

    // MARK: - Version Control Errors
    case versionCreationFailed(filePath: String, reason: String)
    case versionRestoreFailed(filePath: String, version: String, reason: String)
    case metadataCorrupted(path: String, details: String? = nil)
    case versionNotFound(filePath: String, version: String)
    case invalidVersionData(path: String, reason: String)
    case rollbackFailed(filePath: String, reason: String)

    // MARK: - Storage Errors
    case insufficientStorage(required: Int64, available: Int64)
    case storageQuotaExceeded(limit: Int64, current: Int64)
    case storageCleanupFailed(reason: String)
    case diskAccessError(path: String, underlying: Error)

    // MARK: - Search and Indexing Errors
    case indexingFailed(spacePath: String, reason: String)
    case searchTimeout(query: String, timeoutSeconds: TimeInterval)
    case indexCorrupted(spacePath: String)
    case searchEngineUnavailable(reason: String)

    // MARK: - Configuration Errors
    case invalidConfiguration(key: String, value: Any, reason: String)
    case configurationMissing(key: String)
    case configurationValidationFailed(errors: [String])
    case configurationLoadFailed(underlying: Error)
    case configurationSaveFailed(underlying: Error)

    // MARK: - Network and Sync Errors
    case networkUnavailable
    case syncFailed(reason: String, underlying: Error? = nil)
    case authenticationFailed(service: String)
    case serverError(statusCode: Int, message: String)
    case conflictResolutionFailed(filePath: String, reason: String)

    // MARK: - Permission Errors
    case permissionDenied(resource: String, requiredPermission: String)
    case sandboxViolation(attemptedPath: String)
    case securityScopedBookmarkFailed(path: String)

    // MARK: - Performance and Resource Errors
    case memoryPressure(operation: String)
    case operationTimeout(operation: String, timeoutSeconds: TimeInterval)
    case resourceExhausted(resource: String, limit: String)
    case performanceDegradation(
        operation: String, expectedTime: TimeInterval, actualTime: TimeInterval)

    // MARK: - System Integration Errors
    case fsEventsFailure(reason: String)
    case notificationDeliveryFailed(notification: String)
    case backgroundTaskFailed(task: String, reason: String)

    // MARK: - Data Integrity Errors
    case checksumMismatch(filePath: String, expected: String, actual: String)
    case dataCorruption(component: String, details: String)
    case backupVerificationFailed(backupPath: String, reason: String)

    // MARK: - User Interface Errors
    case viewStateInconsistent(view: String, reason: String)
    case userInputValidationFailed(field: String, value: String, reason: String)

    // MARK: - Unknown and Generic Errors
    case unknown(underlying: Error, context: String? = nil)
    case internalError(component: String, reason: String)
    case preconditionFailed(condition: String, context: String)

    // MARK: - LocalizedError Implementation

    public var errorDescription: String? {
        switch self {
        // File System Errors
        case .fileSystemMonitoringFailed(let underlying):
            return "File system monitoring failed: \(underlying.localizedDescription)"
        case .fileAccessDenied(let path):
            return "Access denied to file: \(path)"
        case .fileCorrupted(let path, let reason):
            return "File corrupted at \(path)" + (reason.map { ": \($0)" } ?? "")
        case .fileNotFound(let path):
            return "File not found: \(path)"
        case .fileTooLarge(let path, let size, let limit):
            return
                "File too large: \(path) (\(ByteCountFormatter.string(fromByteCount: size, countStyle: .file)) exceeds limit of \(ByteCountFormatter.string(fromByteCount: limit, countStyle: .file)))"
        case .directoryCreationFailed(let path, let underlying):
            return "Failed to create directory \(path): \(underlying.localizedDescription)"
        case .fileOperationFailed(let operation, let path, let underlying):
            return
                "File operation '\(operation)' failed for \(path): \(underlying.localizedDescription)"

        // Version Control Errors
        case .versionCreationFailed(let filePath, let reason):
            return "Failed to create version for \(filePath): \(reason)"
        case .versionRestoreFailed(let filePath, let version, let reason):
            return "Failed to restore \(filePath) to version \(version): \(reason)"
        case .metadataCorrupted(let path, let details):
            return "Metadata corrupted for \(path)" + (details.map { ": \($0)" } ?? "")
        case .versionNotFound(let filePath, let version):
            return "Version \(version) not found for file \(filePath)"
        case .invalidVersionData(let path, let reason):
            return "Invalid version data for \(path): \(reason)"
        case .rollbackFailed(let filePath, let reason):
            return "Rollback failed for \(filePath): \(reason)"

        // Storage Errors
        case .insufficientStorage(let required, let available):
            return
                "Insufficient storage: need \(ByteCountFormatter.string(fromByteCount: required, countStyle: .file)), have \(ByteCountFormatter.string(fromByteCount: available, countStyle: .file))"
        case .storageQuotaExceeded(let limit, let current):
            return
                "Storage quota exceeded: \(ByteCountFormatter.string(fromByteCount: current, countStyle: .file)) / \(ByteCountFormatter.string(fromByteCount: limit, countStyle: .file))"
        case .storageCleanupFailed(let reason):
            return "Storage cleanup failed: \(reason)"
        case .diskAccessError(let path, let underlying):
            return "Disk access error for \(path): \(underlying.localizedDescription)"

        // Search and Indexing Errors
        case .indexingFailed(let spacePath, let reason):
            return "Indexing failed for \(spacePath): \(reason)"
        case .searchTimeout(let query, let timeoutSeconds):
            return "Search timed out after \(timeoutSeconds)s for query: \(query)"
        case .indexCorrupted(let spacePath):
            return "Search index corrupted for space: \(spacePath)"
        case .searchEngineUnavailable(let reason):
            return "Search engine unavailable: \(reason)"

        // Configuration Errors
        case .invalidConfiguration(let key, let value, let reason):
            return "Invalid configuration for \(key) = \(value): \(reason)"
        case .configurationMissing(let key):
            return "Missing required configuration: \(key)"
        case .configurationValidationFailed(let errors):
            return "Configuration validation failed: \(errors.joined(separator: ", "))"
        case .configurationLoadFailed(let underlying):
            return "Failed to load configuration: \(underlying.localizedDescription)"
        case .configurationSaveFailed(let underlying):
            return "Failed to save configuration: \(underlying.localizedDescription)"

        // Network and Sync Errors
        case .networkUnavailable:
            return "Network connection unavailable"
        case .syncFailed(let reason, let underlying):
            return "Sync failed: \(reason)"
                + (underlying.map { " (\($0.localizedDescription))" } ?? "")
        case .authenticationFailed(let service):
            return "Authentication failed for \(service)"
        case .serverError(let statusCode, let message):
            return "Server error \(statusCode): \(message)"
        case .conflictResolutionFailed(let filePath, let reason):
            return "Conflict resolution failed for \(filePath): \(reason)"

        // Permission Errors
        case .permissionDenied(let resource, let requiredPermission):
            return "Permission denied: need \(requiredPermission) for \(resource)"
        case .sandboxViolation(let attemptedPath):
            return "Sandbox violation: attempted to access \(attemptedPath)"
        case .securityScopedBookmarkFailed(let path):
            return "Failed to create security-scoped bookmark for \(path)"

        // Performance and Resource Errors
        case .memoryPressure(let operation):
            return "Memory pressure during \(operation)"
        case .operationTimeout(let operation, let timeoutSeconds):
            return "Operation \(operation) timed out after \(timeoutSeconds)s"
        case .resourceExhausted(let resource, let limit):
            return "Resource exhausted: \(resource) limit \(limit)"
        case .performanceDegradation(let operation, let expectedTime, let actualTime):
            return
                "Performance degradation in \(operation): expected \(expectedTime)s, took \(actualTime)s"

        // System Integration Errors
        case .fsEventsFailure(let reason):
            return "FSEvents failure: \(reason)"
        case .notificationDeliveryFailed(let notification):
            return "Failed to deliver notification: \(notification)"
        case .backgroundTaskFailed(let task, let reason):
            return "Background task \(task) failed: \(reason)"

        // Data Integrity Errors
        case .checksumMismatch(let filePath, let expected, let actual):
            return "Checksum mismatch for \(filePath): expected \(expected), got \(actual)"
        case .dataCorruption(let component, let details):
            return "Data corruption in \(component): \(details)"
        case .backupVerificationFailed(let backupPath, let reason):
            return "Backup verification failed for \(backupPath): \(reason)"

        // User Interface Errors
        case .viewStateInconsistent(let view, let reason):
            return "View state inconsistent in \(view): \(reason)"
        case .userInputValidationFailed(let field, let value, let reason):
            return "Invalid input for \(field) = '\(value)': \(reason)"

        // Unknown and Generic Errors
        case .unknown(let underlying, let context):
            return "Unknown error" + (context.map { " in \($0)" } ?? "")
                + ": \(underlying.localizedDescription)"
        case .internalError(let component, let reason):
            return "Internal error in \(component): \(reason)"
        case .preconditionFailed(let condition, let context):
            return "Precondition failed: \(condition) in \(context)"
        }
    }

    public var failureReason: String? {
        return errorDescription
    }

    public var recoverySuggestion: String? {
        switch self {
        case .fileAccessDenied:
            return
                "Check file permissions and ensure the file is not locked by another application."
        case .insufficientStorage:
            return "Free up disk space or increase storage limits in preferences."
        case .networkUnavailable:
            return "Check your internet connection and try again."
        case .permissionDenied:
            return "Grant the required permissions in System Preferences > Security & Privacy."
        case .configurationMissing, .invalidConfiguration:
            return "Reset configuration to defaults or check your settings."
        default:
            return "Try restarting the application or contact support if the problem persists."
        }
    }

    // MARK: - CustomStringConvertible Implementation

    public var description: String {
        return errorDescription ?? "Unknown error"
    }

    // MARK: - Error Categorization

    /// Returns the error category for use with ErrorRecoveryManager
    public var category: ErrorCategory {
        switch self {
        case .fileSystemMonitoringFailed, .fileAccessDenied, .fileCorrupted, .fileNotFound,
            .directoryCreationFailed, .fileOperationFailed:
            return .fileSystem
        case .insufficientStorage, .storageQuotaExceeded, .storageCleanupFailed, .diskAccessError:
            return .storage
        case .permissionDenied, .sandboxViolation, .securityScopedBookmarkFailed:
            return .permissions
        case .networkUnavailable, .syncFailed, .authenticationFailed, .serverError:
            return .network
        case .invalidConfiguration, .configurationMissing, .configurationValidationFailed,
            .configurationLoadFailed, .configurationSaveFailed:
            return .configuration
        default:
            return .unknown
        }
    }

    /// Returns whether this error is recoverable
    public var isRecoverable: Bool {
        switch self {
        case .dataCorruption, .checksumMismatch, .preconditionFailed:
            return false  // These indicate serious data integrity issues
        case .internalError:
            return false  // Internal errors usually require code fixes
        default:
            return true  // Most errors can be recovered from
        }
    }

    /// Returns the severity level of this error
    public var severity: ErrorSeverity {
        switch self {
        case .dataCorruption, .checksumMismatch, .metadataCorrupted:
            return .critical
        case .fileSystemMonitoringFailed, .fsEventsFailure, .storageQuotaExceeded:
            return .high
        case .versionCreationFailed, .indexingFailed, .syncFailed:
            return .medium
        case .searchTimeout, .performanceDegradation, .notificationDeliveryFailed:
            return .low
        default:
            return .medium
        }
    }
}

// MARK: - Supporting Types

/// Error severity levels
public enum ErrorSeverity: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case critical = "Critical"

    public var priority: Int {
        switch self {
        case .low: return 1
        case .medium: return 2
        case .high: return 3
        case .critical: return 4
        }
    }
}

// MARK: - Error Creation Helpers

extension AugmentError {
    /// Creates an AugmentError from a generic Error
    public static func from(_ error: Error, context: String? = nil) -> AugmentError {
        if let augmentError = error as? AugmentError {
            return augmentError
        }
        return .unknown(underlying: error, context: context)
    }

    /// Creates a file system error with proper categorization
    public static func fileSystemError(operation: String, path: String, underlying: Error)
        -> AugmentError
    {
        let nsError = underlying as NSError

        // Check for specific error codes
        if nsError.domain == NSCocoaErrorDomain {
            switch nsError.code {
            case NSFileReadNoPermissionError, NSFileWriteNoPermissionError:
                return .fileAccessDenied(path: path)
            case NSFileNoSuchFileError:
                return .fileNotFound(path: path)
            case NSFileWriteOutOfSpaceError:
                return .insufficientStorage(required: 0, available: 0)
            default:
                break
            }
        }

        return .fileOperationFailed(operation: operation, path: path, underlying: underlying)
    }
}
