import Foundation
import os.log

/// Service that provides memory optimization strategies and automatic cleanup
/// Integrates with MemoryManager to provide application-wide memory optimization
public class MemoryOptimizationService {
    
    // MARK: - Dependencies
    
    private let memoryManager: MemoryManager
    private let logger: AugmentLogger
    private let performanceMonitor: PerformanceMonitor
    
    // MARK: - Configuration
    
    /// Memory optimization strategies
    public enum OptimizationStrategy {
        case conservative  // Light cleanup, minimal impact
        case balanced     // Moderate cleanup, good balance
        case aggressive   // Heavy cleanup, maximum memory recovery
    }
    
    /// Memory optimization configuration
    public struct OptimizationConfig {
        public let strategy: OptimizationStrategy
        public let enableAutomaticOptimization: Bool
        public let optimizationInterval: TimeInterval
        public let memoryPressureThreshold: Double
        
        public init(
            strategy: OptimizationStrategy = .balanced,
            enableAutomaticOptimization: Bool = true,
            optimizationInterval: TimeInterval = 600, // 10 minutes
            memoryPressureThreshold: Double = 0.75 // 75%
        ) {
            self.strategy = strategy
            self.enableAutomaticOptimization = enableAutomaticOptimization
            self.optimizationInterval = optimizationInterval
            self.memoryPressureThreshold = memoryPressureThreshold
        }
    }
    
    // MARK: - State Management
    
    private var config: OptimizationConfig
    private var optimizationTimer: Timer?
    private var isOptimizing = false
    private let optimizationQueue = DispatchQueue(label: "com.augment.memory.optimization", qos: .utility)
    
    // MARK: - Optimization Handlers
    
    private var optimizationHandlers: [String: (OptimizationStrategy) -> Void] = [:]
    private let handlersQueue = DispatchQueue(label: "com.augment.memory.handlers", attributes: .concurrent)
    
    // MARK: - Statistics
    
    public struct OptimizationStats {
        public let timestamp: Date
        public let strategy: OptimizationStrategy
        public let memoryBeforeOptimization: UInt64
        public let memoryAfterOptimization: UInt64
        public let duration: TimeInterval
        public let handlersExecuted: Int
        
        public var memoryRecovered: UInt64 {
            return memoryBeforeOptimization > memoryAfterOptimization 
                ? memoryBeforeOptimization - memoryAfterOptimization 
                : 0
        }
        
        public var memoryRecoveredPercentage: Double {
            guard memoryBeforeOptimization > 0 else { return 0 }
            return Double(memoryRecovered) / Double(memoryBeforeOptimization) * 100
        }
    }
    
    private var optimizationHistory: [OptimizationStats] = []
    private let historyQueue = DispatchQueue(label: "com.augment.memory.history", attributes: .concurrent)
    
    // MARK: - Initialization
    
    public init(
        memoryManager: MemoryManager,
        logger: AugmentLogger,
        performanceMonitor: PerformanceMonitor,
        config: OptimizationConfig = OptimizationConfig()
    ) {
        self.memoryManager = memoryManager
        self.logger = logger
        self.performanceMonitor = performanceMonitor
        self.config = config
        
        setupAutomaticOptimization()
        registerDefaultOptimizationHandlers()
    }
    
    deinit {
        stopAutomaticOptimization()
    }
    
    // MARK: - Configuration Management
    
    /// Updates the optimization configuration
    /// - Parameter newConfig: New configuration to apply
    public func updateConfiguration(_ newConfig: OptimizationConfig) {
        optimizationQueue.async(flags: .barrier) {
            self.config = newConfig
            self.setupAutomaticOptimization()
            self.logger.info("Memory optimization configuration updated", category: .performance)
        }
    }
    
    /// Gets the current optimization configuration
    /// - Returns: Current configuration
    public func getCurrentConfiguration() -> OptimizationConfig {
        return optimizationQueue.sync { config }
    }
    
    // MARK: - Optimization Handler Management
    
    /// Registers an optimization handler for a specific component
    /// - Parameters:
    ///   - identifier: Unique identifier for the handler
    ///   - handler: Optimization closure that receives the strategy
    public func registerOptimizationHandler(identifier: String, handler: @escaping (OptimizationStrategy) -> Void) {
        handlersQueue.async(flags: .barrier) {
            self.optimizationHandlers[identifier] = handler
            self.logger.debug("Registered optimization handler: \(identifier)", category: .performance)
        }
    }
    
    /// Unregisters an optimization handler
    /// - Parameter identifier: Identifier of the handler to remove
    public func unregisterOptimizationHandler(identifier: String) {
        handlersQueue.async(flags: .barrier) {
            self.optimizationHandlers.removeValue(forKey: identifier)
            self.logger.debug("Unregistered optimization handler: \(identifier)", category: .performance)
        }
    }
    
    // MARK: - Memory Optimization
    
    /// Performs memory optimization with the specified strategy
    /// - Parameter strategy: Optimization strategy to use (defaults to configured strategy)
    /// - Returns: Optimization statistics
    @discardableResult
    public func performOptimization(strategy: OptimizationStrategy? = nil) -> OptimizationStats {
        let effectiveStrategy = strategy ?? config.strategy
        let operationId = performanceMonitor.startOperation("MemoryOptimization")
        
        return optimizationQueue.sync {
            guard !isOptimizing else {
                logger.warning("Memory optimization already in progress, skipping", category: .performance)
                performanceMonitor.endOperation(operationId)
                return createEmptyStats(strategy: effectiveStrategy)
            }
            
            isOptimizing = true
            defer { isOptimizing = false }
            
            let startTime = Date()
            let memoryBefore = memoryManager.getCurrentMemoryUsage().physicalMemoryUsed
            
            logger.info("Starting memory optimization with \(effectiveStrategy) strategy", category: .performance)
            
            // Execute optimization handlers
            let handlersExecuted = executeOptimizationHandlers(strategy: effectiveStrategy)
            
            // Perform core memory management
            memoryManager.performCleanup()
            
            // Force garbage collection based on strategy
            performGarbageCollection(strategy: effectiveStrategy)
            
            let memoryAfter = memoryManager.getCurrentMemoryUsage().physicalMemoryUsed
            let duration = Date().timeIntervalSince(startTime)
            
            let stats = OptimizationStats(
                timestamp: startTime,
                strategy: effectiveStrategy,
                memoryBeforeOptimization: memoryBefore,
                memoryAfterOptimization: memoryAfter,
                duration: duration,
                handlersExecuted: handlersExecuted
            )
            
            recordOptimizationStats(stats)
            
            logger.info(
                "Memory optimization completed: recovered \(ByteCountFormatter.string(fromByteCount: Int64(stats.memoryRecovered), countStyle: .memory)) (\(String(format: "%.1f", stats.memoryRecoveredPercentage))%) in \(String(format: "%.3f", duration))s",
                category: .performance
            )
            
            performanceMonitor.endOperation(operationId)
            return stats
        }
    }
    
    /// Checks if memory optimization is needed based on current memory pressure
    /// - Returns: True if optimization is recommended
    public func isOptimizationNeeded() -> Bool {
        let currentUsage = memoryManager.getCurrentMemoryUsage()
        let memoryPressureRatio = Double(currentUsage.physicalMemoryUsed) / Double(currentUsage.physicalMemoryTotal)
        return memoryPressureRatio > config.memoryPressureThreshold
    }
    
    /// Gets optimization statistics history
    /// - Returns: Array of optimization statistics
    public func getOptimizationHistory() -> [OptimizationStats] {
        return historyQueue.sync { Array(optimizationHistory) }
    }
    
    // MARK: - Automatic Optimization
    
    /// Starts automatic memory optimization
    public func startAutomaticOptimization() {
        guard config.enableAutomaticOptimization else { return }
        
        optimizationQueue.async(flags: .barrier) {
            self.setupAutomaticOptimization()
        }
    }
    
    /// Stops automatic memory optimization
    public func stopAutomaticOptimization() {
        optimizationQueue.async(flags: .barrier) {
            self.optimizationTimer?.invalidate()
            self.optimizationTimer = nil
            self.logger.info("Automatic memory optimization stopped", category: .performance)
        }
    }
    
    // MARK: - Private Implementation
    
    private func setupAutomaticOptimization() {
        optimizationTimer?.invalidate()
        
        guard config.enableAutomaticOptimization else { return }
        
        optimizationTimer = Timer.scheduledTimer(withTimeInterval: config.optimizationInterval, repeats: true) { [weak self] _ in
            self?.performAutomaticOptimizationIfNeeded()
        }
        
        logger.info("Automatic memory optimization started with \(config.optimizationInterval)s interval", category: .performance)
    }
    
    private func performAutomaticOptimizationIfNeeded() {
        guard isOptimizationNeeded() else { return }
        
        logger.info("Automatic memory optimization triggered due to memory pressure", category: .performance)
        performOptimization()
    }
    
    private func registerDefaultOptimizationHandlers() {
        // Register handler for performance monitor cleanup
        registerOptimizationHandler(identifier: "PerformanceMonitor") { [weak self] strategy in
            self?.performanceMonitor.performMemoryCleanup()
        }
    }
    
    private func executeOptimizationHandlers(strategy: OptimizationStrategy) -> Int {
        return handlersQueue.sync {
            var executedCount = 0
            for (identifier, handler) in optimizationHandlers {
                autoreleasepool {
                    handler(strategy)
                    executedCount += 1
                    logger.debug("Executed optimization handler: \(identifier)", category: .performance)
                }
            }
            return executedCount
        }
    }
    
    private func performGarbageCollection(strategy: OptimizationStrategy) {
        switch strategy {
        case .conservative:
            // Light garbage collection
            memoryManager.forceGarbageCollection()
            
        case .balanced:
            // Moderate garbage collection with delay
            memoryManager.forceGarbageCollection()
            Thread.sleep(forTimeInterval: 0.1)
            memoryManager.forceGarbageCollection()
            
        case .aggressive:
            // Heavy garbage collection with multiple passes
            for _ in 0..<3 {
                memoryManager.forceGarbageCollection()
                Thread.sleep(forTimeInterval: 0.1)
            }
        }
    }
    
    private func recordOptimizationStats(_ stats: OptimizationStats) {
        historyQueue.async(flags: .barrier) {
            self.optimizationHistory.append(stats)
            
            // Keep only last 50 optimization records
            if self.optimizationHistory.count > 50 {
                self.optimizationHistory.removeFirst()
            }
        }
    }
    
    private func createEmptyStats(strategy: OptimizationStrategy) -> OptimizationStats {
        return OptimizationStats(
            timestamp: Date(),
            strategy: strategy,
            memoryBeforeOptimization: 0,
            memoryAfterOptimization: 0,
            duration: 0,
            handlersExecuted: 0
        )
    }
}
