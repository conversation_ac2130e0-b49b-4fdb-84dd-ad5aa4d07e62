import Foundation
import Security

/// Comprehensive security management for Augment application
/// Handles sandboxing, security-scoped bookmarks, file access validation, and audit logging
public class SecurityManager {
    
    // MARK: - Singleton for backward compatibility
    /// Singleton instance - DEPRECATED: Use dependency injection instead
    @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
    public static let shared = SecurityManager()
    
    // MARK: - Dependencies
    private let logger: AugmentLogger
    private let userDefaults = UserDefaults.standard
    private let fileManager = FileManager.default
    
    // MARK: - Security State
    private var securityScopedBookmarks: [String: Data] = [:]
    private var activeSecurityScopedResources: Set<URL> = []
    private let bookmarkQueue = DispatchQueue(label: "com.augment.security.bookmarks", attributes: .concurrent)
    private let auditQueue = DispatchQueue(label: "com.augment.security.audit")
    
    // MARK: - Initialization
    
    /// Private initializer for singleton backward compatibility
    private init() {
        self.logger = AugmentLogger.shared
        loadSecurityScopedBookmarks()
        setupSecurityMonitoring()
    }
    
    /// Public initializer for dependency injection
    public init(logger: AugmentLogger) {
        self.logger = logger
        loadSecurityScopedBookmarks()
        setupSecurityMonitoring()
    }
    
    // MARK: - Security-Scoped Bookmarks
    
    /// Creates and stores a security-scoped bookmark for persistent file access
    /// - Parameter url: The URL to create a bookmark for
    /// - Returns: True if bookmark was created successfully
    public func createSecurityScopedBookmark(for url: URL) -> Bool {
        return bookmarkQueue.sync(flags: .barrier) {
            do {
                // Create security-scoped bookmark
                let bookmarkData = try url.bookmarkData(
                    options: [.withSecurityScope, .securityScopeAllowOnlyReadAccess],
                    includingResourceValuesForKeys: nil,
                    relativeTo: nil
                )
                
                // Store bookmark with URL path as key
                let bookmarkKey = createBookmarkKey(for: url)
                securityScopedBookmarks[bookmarkKey] = bookmarkData
                userDefaults.set(bookmarkData, forKey: bookmarkKey)
                
                logger.info("Created security-scoped bookmark for: \(url.path)", category: .security)
                auditSecurityEvent(.bookmarkCreated(path: url.path))
                
                return true
            } catch {
                logger.error("Failed to create security-scoped bookmark for \(url.path): \(error)", category: .security)
                auditSecurityEvent(.bookmarkCreationFailed(path: url.path, error: error.localizedDescription))
                return false
            }
        }
    }
    
    /// Resolves a security-scoped bookmark and starts accessing the resource
    /// - Parameter url: The URL to resolve bookmark for
    /// - Returns: True if bookmark was resolved and access started
    public func resolveSecurityScopedBookmark(for url: URL) -> Bool {
        return bookmarkQueue.sync {
            let bookmarkKey = createBookmarkKey(for: url)
            
            guard let bookmarkData = securityScopedBookmarks[bookmarkKey] ?? userDefaults.data(forKey: bookmarkKey) else {
                logger.warning("No security-scoped bookmark found for: \(url.path)", category: .security)
                return false
            }
            
            do {
                var isStale = false
                let resolvedURL = try URL(
                    resolvingBookmarkData: bookmarkData,
                    options: .withSecurityScope,
                    relativeTo: nil,
                    bookmarkDataIsStale: &isStale
                )
                
                if isStale {
                    logger.warning("Security-scoped bookmark is stale for: \(url.path)", category: .security)
                    // Try to recreate the bookmark
                    _ = createSecurityScopedBookmark(for: resolvedURL)
                }
                
                // Start accessing the security-scoped resource
                guard resolvedURL.startAccessingSecurityScopedResource() else {
                    logger.error("Failed to start accessing security-scoped resource: \(url.path)", category: .security)
                    auditSecurityEvent(.resourceAccessFailed(path: url.path))
                    return false
                }
                
                activeSecurityScopedResources.insert(resolvedURL)
                logger.info("Started accessing security-scoped resource: \(url.path)", category: .security)
                auditSecurityEvent(.resourceAccessStarted(path: url.path))
                
                return true
            } catch {
                logger.error("Failed to resolve security-scoped bookmark for \(url.path): \(error)", category: .security)
                auditSecurityEvent(.bookmarkResolutionFailed(path: url.path, error: error.localizedDescription))
                return false
            }
        }
    }
    
    /// Stops accessing a security-scoped resource
    /// - Parameter url: The URL to stop accessing
    public func stopAccessingSecurityScopedResource(_ url: URL) {
        bookmarkQueue.async(flags: .barrier) {
            if self.activeSecurityScopedResources.contains(url) {
                url.stopAccessingSecurityScopedResource()
                self.activeSecurityScopedResources.remove(url)
                self.logger.info("Stopped accessing security-scoped resource: \(url.path)", category: .security)
                self.auditSecurityEvent(.resourceAccessStopped(path: url.path))
            }
        }
    }
    
    /// Removes a security-scoped bookmark
    /// - Parameter url: The URL to remove bookmark for
    public func removeSecurityScopedBookmark(for url: URL) {
        bookmarkQueue.async(flags: .barrier) {
            let bookmarkKey = self.createBookmarkKey(for: url)
            self.securityScopedBookmarks.removeValue(forKey: bookmarkKey)
            self.userDefaults.removeObject(forKey: bookmarkKey)
            
            // Stop accessing if currently active
            self.stopAccessingSecurityScopedResource(url)
            
            self.logger.info("Removed security-scoped bookmark for: \(url.path)", category: .security)
            self.auditSecurityEvent(.bookmarkRemoved(path: url.path))
        }
    }
    
    // MARK: - File Access Validation
    
    /// Validates file access permissions before performing operations
    /// - Parameters:
    ///   - url: The URL to validate access for
    ///   - operation: The type of operation to perform
    /// - Returns: True if access is allowed
    public func validateFileAccess(_ url: URL, for operation: FileOperation) -> Bool {
        // Check if file exists
        guard fileManager.fileExists(atPath: url.path) else {
            logger.warning("File access validation failed - file does not exist: \(url.path)", category: .security)
            auditSecurityEvent(.fileAccessDenied(path: url.path, operation: operation.rawValue, reason: "File does not exist"))
            return false
        }
        
        // Check read permissions
        guard fileManager.isReadableFile(atPath: url.path) else {
            logger.warning("File access validation failed - no read permission: \(url.path)", category: .security)
            auditSecurityEvent(.fileAccessDenied(path: url.path, operation: operation.rawValue, reason: "No read permission"))
            return false
        }
        
        // Check write permissions for write operations
        if operation.requiresWrite {
            guard fileManager.isWritableFile(atPath: url.path) else {
                logger.warning("File access validation failed - no write permission: \(url.path)", category: .security)
                auditSecurityEvent(.fileAccessDenied(path: url.path, operation: operation.rawValue, reason: "No write permission"))
                return false
            }
        }
        
        // Validate against security policies
        guard validateSecurityPolicy(for: url, operation: operation) else {
            return false
        }
        
        logger.debug("File access validation passed for: \(url.path), operation: \(operation.rawValue)", category: .security)
        auditSecurityEvent(.fileAccessGranted(path: url.path, operation: operation.rawValue))
        return true
    }
    
    /// Validates security policies for file operations
    /// - Parameters:
    ///   - url: The URL to validate
    ///   - operation: The operation to perform
    /// - Returns: True if operation is allowed by security policy
    private func validateSecurityPolicy(for url: URL, operation: FileOperation) -> Bool {
        // Check for suspicious file patterns
        let suspiciousPatterns = [
            "/System/", "/usr/bin/", "/usr/sbin/", "/bin/", "/sbin/",
            ".app/Contents/MacOS/", ".framework/"
        ]
        
        let path = url.path
        for pattern in suspiciousPatterns {
            if path.contains(pattern) && operation.requiresWrite {
                logger.warning("Security policy violation - attempted write to system path: \(path)", category: .security)
                auditSecurityEvent(.securityPolicyViolation(path: path, operation: operation.rawValue, reason: "System path write attempt"))
                return false
            }
        }
        
        // Check file size limits for certain operations
        if operation == .backup || operation == .version {
            do {
                let attributes = try fileManager.attributesOfItem(atPath: path)
                if let fileSize = attributes[.size] as? Int64, fileSize > 10_000_000_000 { // 10GB limit
                    logger.warning("Security policy violation - file too large: \(path) (\(fileSize) bytes)", category: .security)
                    auditSecurityEvent(.securityPolicyViolation(path: path, operation: operation.rawValue, reason: "File size exceeds limit"))
                    return false
                }
            } catch {
                logger.error("Failed to check file size for security policy: \(error)", category: .security)
            }
        }
        
        return true
    }
    
    // MARK: - Audit Logging
    
    /// Logs a security event for audit purposes
    /// - Parameter event: The security event to log
    private func auditSecurityEvent(_ event: SecurityEvent) {
        auditQueue.async {
            let timestamp = ISO8601DateFormatter().string(from: Date())
            let auditEntry = SecurityAuditEntry(
                timestamp: timestamp,
                event: event,
                userId: NSUserName(),
                processId: ProcessInfo.processInfo.processIdentifier
            )
            
            // Log to system logger
            self.logger.info("SECURITY_AUDIT: \(auditEntry.description)", category: .security)
            
            // Store in persistent audit log
            self.storeAuditEntry(auditEntry)
        }
    }
    
    /// Stores an audit entry in persistent storage
    /// - Parameter entry: The audit entry to store
    private func storeAuditEntry(_ entry: SecurityAuditEntry) {
        // Get existing audit log
        var auditLog = getAuditLog()
        auditLog.append(entry)
        
        // Keep only recent entries (last 1000 or 30 days)
        let thirtyDaysAgo = Date().addingTimeInterval(-30 * 24 * 60 * 60)
        auditLog = auditLog.suffix(1000).filter { entry in
            ISO8601DateFormatter().date(from: entry.timestamp) ?? Date() > thirtyDaysAgo
        }
        
        // Save audit log
        do {
            let data = try JSONEncoder().encode(auditLog)
            userDefaults.set(data, forKey: "security_audit_log")
        } catch {
            logger.error("Failed to save security audit log: \(error)", category: .security)
        }
    }
    
    /// Retrieves the current audit log
    /// - Returns: Array of security audit entries
    public func getAuditLog() -> [SecurityAuditEntry] {
        guard let data = userDefaults.data(forKey: "security_audit_log") else {
            return []
        }
        
        do {
            return try JSONDecoder().decode([SecurityAuditEntry].self, from: data)
        } catch {
            logger.error("Failed to decode security audit log: \(error)", category: .security)
            return []
        }
    }
    
    // MARK: - Private Helpers
    
    private func createBookmarkKey(for url: URL) -> String {
        return "security_bookmark_\(url.path.hash)"
    }
    
    private func loadSecurityScopedBookmarks() {
        // Load existing bookmarks from UserDefaults
        let keys = userDefaults.dictionaryRepresentation().keys.filter { $0.hasPrefix("security_bookmark_") }
        for key in keys {
            if let data = userDefaults.data(forKey: key) {
                securityScopedBookmarks[key] = data
            }
        }
        logger.info("Loaded \(securityScopedBookmarks.count) security-scoped bookmarks", category: .security)
    }
    
    private func setupSecurityMonitoring() {
        // Set up periodic security checks
        Timer.scheduledTimer(withTimeInterval: 3600, repeats: true) { _ in
            self.performSecurityHealthCheck()
        }
    }
    
    private func performSecurityHealthCheck() {
        auditQueue.async {
            self.logger.info("Performing security health check", category: .security)
            
            // Check for stale bookmarks
            let staleBookmarks = self.securityScopedBookmarks.compactMap { (key, data) -> String? in
                do {
                    var isStale = false
                    _ = try URL(resolvingBookmarkData: data, options: .withSecurityScope, relativeTo: nil, bookmarkDataIsStale: &isStale)
                    return isStale ? key : nil
                } catch {
                    return key // Consider failed resolution as stale
                }
            }
            
            if !staleBookmarks.isEmpty {
                self.logger.warning("Found \(staleBookmarks.count) stale security bookmarks", category: .security)
                self.auditSecurityEvent(.securityHealthCheck(staleBookmarks: staleBookmarks.count))
            }
            
            // Check active resources
            self.logger.info("Active security-scoped resources: \(self.activeSecurityScopedResources.count)", category: .security)
        }
    }
}

// MARK: - Supporting Types

/// File operation types for security validation
public enum FileOperation: String, CaseIterable {
    case read = "read"
    case write = "write"
    case delete = "delete"
    case version = "version"
    case backup = "backup"
    case monitor = "monitor"
    
    var requiresWrite: Bool {
        switch self {
        case .read, .monitor:
            return false
        case .write, .delete, .version, .backup:
            return true
        }
    }
}

/// Security events for audit logging
public enum SecurityEvent {
    case bookmarkCreated(path: String)
    case bookmarkCreationFailed(path: String, error: String)
    case bookmarkResolutionFailed(path: String, error: String)
    case bookmarkRemoved(path: String)
    case resourceAccessStarted(path: String)
    case resourceAccessStopped(path: String)
    case resourceAccessFailed(path: String)
    case fileAccessGranted(path: String, operation: String)
    case fileAccessDenied(path: String, operation: String, reason: String)
    case securityPolicyViolation(path: String, operation: String, reason: String)
    case securityHealthCheck(staleBookmarks: Int)
}

/// Security audit entry for persistent logging
public struct SecurityAuditEntry: Codable {
    public let timestamp: String
    public let event: String
    public let userId: String
    public let processId: Int32
    
    init(timestamp: String, event: SecurityEvent, userId: String, processId: Int32) {
        self.timestamp = timestamp
        self.event = SecurityAuditEntry.eventDescription(event)
        self.userId = userId
        self.processId = processId
    }
    
    private static func eventDescription(_ event: SecurityEvent) -> String {
        switch event {
        case .bookmarkCreated(let path):
            return "BOOKMARK_CREATED: \(path)"
        case .bookmarkCreationFailed(let path, let error):
            return "BOOKMARK_CREATION_FAILED: \(path) - \(error)"
        case .bookmarkResolutionFailed(let path, let error):
            return "BOOKMARK_RESOLUTION_FAILED: \(path) - \(error)"
        case .bookmarkRemoved(let path):
            return "BOOKMARK_REMOVED: \(path)"
        case .resourceAccessStarted(let path):
            return "RESOURCE_ACCESS_STARTED: \(path)"
        case .resourceAccessStopped(let path):
            return "RESOURCE_ACCESS_STOPPED: \(path)"
        case .resourceAccessFailed(let path):
            return "RESOURCE_ACCESS_FAILED: \(path)"
        case .fileAccessGranted(let path, let operation):
            return "FILE_ACCESS_GRANTED: \(operation) on \(path)"
        case .fileAccessDenied(let path, let operation, let reason):
            return "FILE_ACCESS_DENIED: \(operation) on \(path) - \(reason)"
        case .securityPolicyViolation(let path, let operation, let reason):
            return "SECURITY_POLICY_VIOLATION: \(operation) on \(path) - \(reason)"
        case .securityHealthCheck(let staleBookmarks):
            return "SECURITY_HEALTH_CHECK: \(staleBookmarks) stale bookmarks found"
        }
    }
    
    var description: String {
        return "[\(timestamp)] [\(userId):\(processId)] \(event)"
    }
}
