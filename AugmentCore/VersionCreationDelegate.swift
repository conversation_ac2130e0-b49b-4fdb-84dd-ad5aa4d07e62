import Foundation

/// Protocol for version creation to avoid circular dependencies
public protocol VersionCreationDelegate: AnyObject {
    func createFileVersion(filePath: URL, comment: String?) -> Bool
}

/// Default implementation of VersionCreationDelegate using VersionControl
public class DefaultVersionCreationDelegate: VersionCreationDelegate {
    public init() {}
    
    public func createFileVersion(filePath: URL, comment: String?) -> Bool {
        // Use VersionControl.shared to create the version
        if let _ = VersionControl.shared.createFileVersion(filePath: filePath, comment: comment) {
            return true
        } else {
            return false
        }
    }
}
