import Cocoa
import Combine
import Foundation

/// Enhanced FileSystemMonitor that detects actual file saves vs other modifications
public class FileSystemMonitor {
    /// Singleton instance - DEPRECATED: Use dependency injection instead
    @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
    public static let shared = FileSystemMonitor()

    // MARK: - Dependencies for Error Handling
    public let logger: AugmentLogger
    public let errorRecoveryManager: ErrorRecoveryManager
    public let memoryManager: MemoryManager

    /// Configuration instance (temporarily commented out until file is added to project)
    // private let configuration = AugmentConfiguration.shared

    /// The list of monitored spaces
    private var monitoredSpaces: [URL] = []

    /// The file system event stream
    private var eventStream: FSEventStreamRef?

    /// The callback for file system events
    private var eventCallback: ((URL, FileSystemEvent) -> Void)?

    /// Throttling dictionary to prevent excessive version creation
    private var lastVersionCreationTimes: [String: Date] = [:]

    /// Track file metadata to detect actual saves
    private var fileMetadataCache: [String: FileMetadata] = [:]

    /// Track keyboard events to detect Command+S
    private var isCommandSPressed = false
    private var commandSTimestamp: Date?

    /// Global keyboard monitor
    private var globalKeyboardMonitor: Any?

    /// Queue for thread-safe access
    private let throttlingQueue = DispatchQueue(
        label: "com.augment.filesystemmonitor.throttling", attributes: .concurrent)

    /// Flag to track if we're in fallback mode
    private var isInFallbackMode = false

    /// Timer for fallback mode scanning
    private var fallbackTimer: Timer?

    /// Configuration for save detection
    public struct SaveDetectionConfig {
        var enableKeyboardMonitoring = true
        var commandSTimeWindow: TimeInterval = 2.0  // 2 seconds window after Cmd+S
        var minimumFileSizeChange: Int64 = 1  // Minimum bytes changed to consider it a save
        var enableContentHashCheck = true  // Check file content hash for changes
        var saveDetectionMethods: [SaveDetectionMethod] = [.keyboardShortcut, .fileMetadata]
    }

    public enum SaveDetectionMethod {
        case keyboardShortcut  // Detect Command+S
        case fileMetadata  // Check modification time + size changes
        case contentHash  // Check if file content actually changed
        case applicationFocus  // Check if foreground app just saved
    }

    public var saveDetectionConfig = SaveDetectionConfig()

    /// File metadata for tracking changes
    private struct FileMetadata {
        let modificationDate: Date
        let fileSize: Int64
        let contentHash: String?

        init(url: URL) {
            let resourceValues = try? url.resourceValues(forKeys: [
                .contentModificationDateKey,
                .fileSizeKey,
            ])

            self.modificationDate = resourceValues?.contentModificationDate ?? Date()
            self.fileSize = Int64(resourceValues?.fileSize ?? 0)

            // Calculate content hash if enabled (using default config for now)
            // TODO: Pass saveDetectionConfig as parameter to avoid singleton dependency
            self.contentHash = nil  // Simplified for now to avoid singleton dependency
        }

        private static func calculateFileHash(url: URL) -> String? {
            guard let data = try? Data(contentsOf: url, options: .mappedIfSafe) else {
                return nil
            }
            // Use a simple hash for performance - you could use SHA256 for better accuracy
            return String(data.hashValue)
        }
    }

    /// Debounce timers for each file to prevent excessive versioning
    private var debounceTimers: [String: Timer] = [:]
    /// Debounce interval (seconds) - configurable
    public var debounceInterval: TimeInterval = 10.0

    /// Set of file paths for which auto-versioning is temporarily suppressed
    private var suppressedAutoVersioning: Set<String> = []
    /// Set of space paths for which auto-versioning is temporarily suppressed
    private var suppressedSpaces: Set<String> = []
    private let suppressionQueue = DispatchQueue(
        label: "com.augment.filesystemmonitor.suppression", attributes: .concurrent)

    /// Add this property to allow protocol-based access
    private let fileSystem: SpaceFileSystemProviding = {
        // Use dynamic lookup to avoid direct import issues
        if let cls = NSClassFromString("AugmentFileSystem.AugmentFileSystem") as? NSObject.Type,
            let instance = cls.perform(NSSelectorFromString("shared"))?.takeUnretainedValue()
                as? SpaceFileSystemProviding
        {
            return instance
        }
        // Fallback: return a dummy implementation that always returns nil
        class Dummy: SpaceFileSystemProviding { func getSpace(path: URL) -> AugmentSpace? { nil } }
        return Dummy()
    }()

    /// Spaces managed by this monitor
    private var spaces: [String: FileSystemMonitor.SpaceConfig] = [:]

    /// Private initializer for singleton backward compatibility
    private init() {
        // Fallback dependencies for singleton usage
        self.logger = AugmentLogger.shared
        self.errorRecoveryManager = ErrorRecoveryManager.shared
        self.memoryManager = MemoryManager(
            logger: AugmentLogger.shared,
            performanceMonitor: PerformanceMonitor(logger: AugmentLogger.shared))
        // Don't set up keyboard monitoring in init - wait until it's actually needed
        print("FileSystemMonitor: Initialized with singleton dependencies")
    }

    /// Public initializer for dependency injection
    public init(
        logger: AugmentLogger, errorRecoveryManager: ErrorRecoveryManager,
        memoryManager: MemoryManager
    ) {
        self.logger = logger
        self.errorRecoveryManager = errorRecoveryManager
        self.memoryManager = memoryManager

        // Register memory cleanup handlers
        setupMemoryManagement()

        // Don't set up keyboard monitoring in init - wait until it's actually needed
        print("FileSystemMonitor: Initialized with dependency injection")
    }

    deinit {
        print("FileSystemMonitor: Deinitializing")
        stopKeyboardMonitoring()
        stopMonitoring()
        memoryManager.unregisterCleanupHandler(identifier: "FileSystemMonitor")
    }

    // MARK: - Memory Management

    /// Sets up memory management for the FileSystemMonitor
    private func setupMemoryManagement() {
        memoryManager.registerCleanupHandler(identifier: "FileSystemMonitor") { [weak self] in
            self?.performMemoryCleanup()
        }

        // Track this object for memory leak detection
        memoryManager.trackObject(self)
    }

    /// Performs memory cleanup for the FileSystemMonitor
    private func performMemoryCleanup() {
        throttlingQueue.async(flags: .barrier) {
            // Clear old throttling entries (older than 1 hour)
            let oneHourAgo = Date().addingTimeInterval(-3600)
            self.lastVersionCreationTimes = self.lastVersionCreationTimes.filter { _, date in
                date > oneHourAgo
            }

            // Clear old metadata cache entries
            let oldCacheKeys = self.fileMetadataCache.keys.filter { key in
                guard let metadata = self.fileMetadataCache[key] else { return true }
                return metadata.modificationDate.addingTimeInterval(3600) < Date()
            }

            for key in oldCacheKeys {
                self.fileMetadataCache.removeValue(forKey: key)
            }

            self.logger.debug(
                "FileSystemMonitor memory cleanup completed: cleared \(oldCacheKeys.count) cache entries"
            )
        }
    }

    /// Setup global keyboard monitoring to detect Command+S
    private func setupKeyboardMonitoring() {
        guard saveDetectionConfig.enableKeyboardMonitoring else { return }

        // Ensure we don't create multiple monitors
        stopKeyboardMonitoring()

        // Create the monitor with proper memory management
        globalKeyboardMonitor = NSEvent.addGlobalMonitorForEvents(matching: .keyDown) {
            [weak self] event in
            guard let self = self else { return }
            self.handleKeyboardEvent(event)
        }

        print("FileSystemMonitor: Global keyboard monitoring started")
    }

    /// Stop keyboard monitoring
    private func stopKeyboardMonitoring() {
        guard let monitor = globalKeyboardMonitor else { return }

        // Remove the monitor
        NSEvent.removeMonitor(monitor)
        globalKeyboardMonitor = nil

        print("FileSystemMonitor: Global keyboard monitoring stopped")
    }

    /// Handle keyboard events to detect Command+S
    private func handleKeyboardEvent(_ event: NSEvent) {
        // Check for Command+S (keyCode 1 is 'S', modifierFlags contains .command)
        if event.keyCode == 1 && event.modifierFlags.contains(.command) {
            print("FileSystemMonitor: Command+S detected")
            isCommandSPressed = true
            commandSTimestamp = Date()

            // Reset the flag after the time window
            DispatchQueue.main.asyncAfter(deadline: .now() + saveDetectionConfig.commandSTimeWindow)
            {
                self.isCommandSPressed = false
                self.commandSTimestamp = nil
            }
        }
    }

    /// Helper: Find the Augment space path for a file
    private func findSpacePath(for filePath: URL) -> URL? {
        var currentPath = filePath.deletingLastPathComponent()
        print("[DEBUG] findSpacePath: Starting at \(filePath.path)")
        while currentPath.path != "/" {
            let augmentDir = currentPath.appendingPathComponent(".augment")
            print("[DEBUG] Checking for .augment at \(augmentDir.path)")
            if FileManager.default.fileExists(atPath: augmentDir.path) {
                print("[DEBUG] Found .augment at \(augmentDir.path)")
                return currentPath
            }
            currentPath = currentPath.deletingLastPathComponent()
        }
        print("[DEBUG] No .augment found for \(filePath.path)")
        return nil
    }

    /// Helper: Check if autoVersioning is enabled for a space
    private func isAutoVersioningEnabled(for spacePath: URL) -> Bool {
        guard let space = fileSystem.getSpace(path: spacePath) else {
            // If the space is not found, default to true
            return true
        }
        return space.settings.versioningMode == .auto
    }

    /// Suppress auto-versioning for a file (call before restoring)
    public func suppressAutoVersioning(for filePath: URL) {
        print("🔒 FileSystemMonitor: SUPPRESSING auto-versioning for: \(filePath.path)")
        suppressionQueue.async(flags: .barrier) {
            self.suppressedAutoVersioning.insert(filePath.path)
            print(
                "🔒 FileSystemMonitor: Suppression set for: \(filePath.path) (total suppressed: \(self.suppressedAutoVersioning.count))"
            )
        }
    }

    /// Remove suppression for a file (call after restoring)
    public func unsuppressAutoVersioning(for filePath: URL) {
        print("🔓 FileSystemMonitor: REMOVING suppression for: \(filePath.path)")
        suppressionQueue.async(flags: .barrier) {
            self.suppressedAutoVersioning.remove(filePath.path)
            print(
                "🔓 FileSystemMonitor: Suppression removed for: \(filePath.path) (total suppressed: \(self.suppressedAutoVersioning.count))"
            )
        }
    }

    /// Check if auto-versioning is suppressed for a file
    public func isAutoVersioningSuppressed(for filePath: URL) -> Bool {
        var suppressed = false
        suppressionQueue.sync {
            // Check if the specific file is suppressed
            suppressed = self.suppressedAutoVersioning.contains(filePath.path)

            // If not suppressed individually, check if the entire space is suppressed
            if !suppressed {
                for spacePath in self.suppressedSpaces {
                    if filePath.path.hasPrefix(spacePath) {
                        suppressed = true
                        break
                    }
                }
            }
        }
        print("🔍 FileSystemMonitor: Checking suppression for: \(filePath.path)")
        print("🔍 FileSystemMonitor: Suppressed files: \(suppressedAutoVersioning)")
        print("🔍 FileSystemMonitor: Suppressed spaces: \(suppressedSpaces)")
        if suppressed {
            print("🔒 FileSystemMonitor: Auto-versioning IS SUPPRESSED for: \(filePath.path)")
        } else {
            print("🔓 FileSystemMonitor: Auto-versioning NOT SUPPRESSED for: \(filePath.path)")
        }
        return suppressed
    }

    /// Suppress auto-versioning for an entire space (call before backup restoration)
    public func suppressAutoVersioningForSpace(spacePath: URL) {
        print(
            "🔒 FileSystemMonitor: SUPPRESSING auto-versioning for entire space: \(spacePath.path)")
        suppressionQueue.async(flags: .barrier) {
            self.suppressedSpaces.insert(spacePath.path)
            print(
                "🔒 FileSystemMonitor: Space suppression set for: \(spacePath.path) (total suppressed spaces: \(self.suppressedSpaces.count))"
            )
        }
    }

    /// Remove suppression for an entire space (call after backup restoration)
    public func unsuppressAutoVersioningForSpace(spacePath: URL) {
        print("🔓 FileSystemMonitor: REMOVING space suppression for: \(spacePath.path)")
        suppressionQueue.async(flags: .barrier) {
            self.suppressedSpaces.remove(spacePath.path)
            print(
                "🔓 FileSystemMonitor: Space suppression removed for: \(spacePath.path) (total suppressed spaces: \(self.suppressedSpaces.count))"
            )
        }
    }

    /// Enhanced method to detect if a file modification was actually a save (Command+S only)
    private func isActualFileSave(url: URL) -> Bool {
        // Suppress auto-versioning if flagged
        if isAutoVersioningSuppressed(for: url) {
            return false
        }
        // Only return true if Command+S was detected recently
        if saveDetectionConfig.saveDetectionMethods.contains(.keyboardShortcut) {
            if let cmdSTime = commandSTimestamp,
                Date().timeIntervalSince(cmdSTime) < saveDetectionConfig.commandSTimeWindow
            {
                print(
                    "FileSystemMonitor: Save detected via Command+S timing for \(url.lastPathComponent)"
                )
                return true
            }
        }
        return false
    }

    /// Starts monitoring a space for file system events
    /// - Parameters:
    ///   - spacePath: The path to the Augment space
    ///   - callback: The callback to invoke when events occur
    /// - Returns: A boolean indicating success or failure
    public func startMonitoring(spacePath: URL, callback: @escaping (URL, FileSystemEvent) -> Void)
        -> Bool
    {
        guard spacePath.isFileURL else { return false }

        // Store the callback
        eventCallback = callback

        // Add the space to the monitored spaces
        if !monitoredSpaces.contains(spacePath) {
            monitoredSpaces.append(spacePath)
        }

        // Stop any existing stream
        stopMonitoring()

        // Create a new event stream
        var context = FSEventStreamContext(
            version: 0,
            info: Unmanaged.passUnretained(self).toOpaque(),
            retain: nil,
            release: nil,
            copyDescription: nil
        )

        let paths = monitoredSpaces.map { $0.path as NSString }

        eventStream = FSEventStreamCreate(
            kCFAllocatorDefault,
            { (streamRef, clientCallBackInfo, numEvents, eventPaths, eventFlags, eventIds) in
                // CRITICAL FIX: Add comprehensive safety checks to prevent crashes

                // Validate all parameters before processing
                guard let clientCallBackInfo = clientCallBackInfo else {
                    print("FileSystemMonitor: No callback info provided")
                    return
                }

                guard numEvents > 0 && numEvents < 10000 else {
                    print("FileSystemMonitor: Invalid numEvents: \(numEvents)")
                    return
                }

                // eventPaths and eventFlags are guaranteed to be non-null by FSEvents
                // but we'll add validation in the processing methods

                // Safely extract the monitor instance
                let monitor = Unmanaged<FileSystemMonitor>.fromOpaque(clientCallBackInfo)
                    .takeUnretainedValue()

                // Process events with comprehensive error handling and crash prevention
                monitor.processEvents(
                    numEvents: numEvents, eventPaths: eventPaths, eventFlags: eventFlags)
            },
            &context,
            paths as CFArray,
            FSEventStreamEventId(kFSEventStreamEventIdSinceNow),
            1.0,  // 1 second latency
            FSEventStreamCreateFlags(kFSEventStreamCreateFlagFileEvents)
        )

        guard let eventStream = eventStream else { return false }

        // Schedule the event stream using modern dispatch queue approach
        let queue = DispatchQueue.global(qos: .utility)
        FSEventStreamSetDispatchQueue(eventStream, queue)

        // Start the event stream
        FSEventStreamStart(eventStream)

        return true
    }

    /// Async version of startMonitoring for modern Swift concurrency
    /// - Parameters:
    ///   - spacePath: The path to the Augment space
    ///   - callback: The async callback to invoke when events occur
    /// - Returns: A boolean indicating success or failure
    @available(macOS 10.15, *)
    public func startMonitoringAsync(
        spacePath: URL, callback: @escaping (URL, FileSystemEvent) async -> Void
    ) -> Bool {
        // Wrap the async callback in a sync callback
        let syncCallback: (URL, FileSystemEvent) -> Void = { url, event in
            Task {
                await callback(url, event)
            }
        }

        return startMonitoring(spacePath: spacePath, callback: syncCallback)
    }

    /// Stops monitoring for file system events
    public func stopMonitoring() {
        guard let eventStream = eventStream else { return }

        FSEventStreamStop(eventStream)
        FSEventStreamInvalidate(eventStream)
        FSEventStreamRelease(eventStream)

        self.eventStream = nil
    }

    /// Processes file system events from FSEvents with maximum safety
    /// - Parameters:
    ///   - numEvents: The number of events
    ///   - eventPaths: The paths of the events
    ///   - eventFlags: The flags of the events
    private func processEvents(
        numEvents: Int, eventPaths: UnsafeMutableRawPointer,
        eventFlags: UnsafePointer<FSEventStreamEventFlags>
    ) {
        // Use improved error handling with proper logging
        do {
            try processEventsWithSafety(
                numEvents: numEvents, eventPaths: eventPaths, eventFlags: eventFlags)
        } catch {
            print("FileSystemMonitor: Critical error in processEvents: \(error)")
            print("FileSystemMonitor: Switching to fallback mode to prevent crashes")
            disableFSEventsAndUseFallback()
        }
    }

    /// Internal method with comprehensive safety checks
    private func processEventsWithSafety(
        numEvents: Int, eventPaths: UnsafeMutableRawPointer,
        eventFlags: UnsafePointer<FSEventStreamEventFlags>
    ) throws {
        // CRITICAL FIX: Use the safest possible FSEvents handling
        guard numEvents > 0 else {
            print("FileSystemMonitor: No events to process")
            return
        }

        // Add comprehensive safety checks
        guard numEvents < 10000 else {
            throw FileSystemMonitorError.eventProcessingError(
                "Suspiciously large number of events (\(numEvents)), aborting for safety")
        }

        // eventPaths and eventFlags are guaranteed to be non-null by FSEvents API
        // Additional validation will be done in the conversion methods

        print("FileSystemMonitor: Processing \(numEvents) events with enhanced safety")

        // Process events with maximum safety using multiple fallback approaches
        processEventsWithMaximumSafety(
            numEvents: numEvents, eventPaths: eventPaths, eventFlags: eventFlags)
    }

    /// Process events with maximum safety using multiple fallback approaches
    private func processEventsWithMaximumSafety(
        numEvents: Int,
        eventPaths: UnsafeMutableRawPointer,
        eventFlags: UnsafePointer<FSEventStreamEventFlags>
    ) {
        print("FileSystemMonitor: Processing \(numEvents) FSEvents with enhanced safety")

        // Validate parameters first
        guard numEvents > 0 && numEvents < 1000 else {
            print(
                "FileSystemMonitor: Invalid number of events (\(numEvents)), switching to fallback"
            )
            disableFSEventsAndUseFallback()
            return
        }

        // Try primary approach first (CFArray conversion)
        do {
            if let pathsArray = try convertToCFArray(eventPaths: eventPaths, numEvents: numEvents) {
                processCFArrayEvents(
                    pathsArray: pathsArray, numEvents: numEvents, eventFlags: eventFlags)
                print("FileSystemMonitor: Successfully processed \(numEvents) events via CFArray")
                return
            }
        } catch {
            print("FileSystemMonitor: CFArray processing failed: \(error)")
        }

        // Try alternative approach (direct processing)
        do {
            try processEventsAlternativeApproach(
                numEvents: numEvents, eventPaths: eventPaths, eventFlags: eventFlags)
            print(
                "FileSystemMonitor: Successfully processed \(numEvents) events via alternative approach"
            )
            return
        } catch {
            print("FileSystemMonitor: Alternative processing failed: \(error)")
        }

        // Only switch to fallback if both approaches fail
        print(
            "FileSystemMonitor: All FSEvents processing approaches failed, switching to fallback mode"
        )
        disableFSEventsAndUseFallback()
    }

    /// Convert eventPaths to CFArray safely with proper memory management
    private func convertToCFArray(eventPaths: UnsafeMutableRawPointer, numEvents: Int) throws
        -> CFArray?
    {
        // Validate the pointer is not null
        guard eventPaths != UnsafeMutableRawPointer(bitPattern: 0) else {
            throw FileSystemMonitorError.memoryAccessError("FSEvents data pointer is null")
        }

        // Validate numEvents is reasonable
        guard numEvents > 0 && numEvents < 10000 else {
            throw FileSystemMonitorError.eventProcessingError(
                "Invalid number of events: \(numEvents)")
        }

        // Cast the pointer to the correct type for FSEvents
        // FSEvents provides eventPaths as a C array of C strings (char**)
        let pathsPointer = eventPaths.assumingMemoryBound(to: UnsafePointer<CChar>?.self)

        // Create an array to hold the path strings
        var pathStrings: [String] = []
        pathStrings.reserveCapacity(numEvents)

        // Safely extract each path string
        for i in 0..<numEvents {
            guard let pathPtr = pathsPointer[i] else {
                print("FileSystemMonitor: Null path pointer at index \(i)")
                continue
            }

            // Convert C string to Swift String
            let pathString = String(cString: pathPtr)

            // Validate the path string
            guard !pathString.isEmpty && pathString.count < 4096 else {
                print("FileSystemMonitor: Invalid path string at index \(i): '\(pathString)'")
                continue
            }

            pathStrings.append(pathString)
        }

        // Convert to CFArray
        let cfArray = pathStrings as CFArray
        return cfArray
    }

    /// Process events from CFArray
    private func processCFArrayEvents(
        pathsArray: CFArray,
        numEvents: Int,
        eventFlags: UnsafePointer<FSEventStreamEventFlags>
    ) {
        for i in 0..<numEvents {
            autoreleasepool {
                // Safely get the CFString from the array
                guard let pathCFString = CFArrayGetValueAtIndex(pathsArray, i) else {
                    print("FileSystemMonitor: Failed to get CFString at index \(i)")
                    return
                }

                // Convert CFString to Swift String with error handling
                let pathString: String
                pathString = String(pathCFString as! CFString)

                // Validate the path string
                guard !pathString.isEmpty && pathString.count < 4096 else {
                    print("FileSystemMonitor: Invalid path string at index \(i)")
                    return
                }

                let eventURL = URL(fileURLWithPath: pathString)
                let flags = eventFlags[i]

                // Process the event with error handling
                do {
                    try processFileSystemEvent(url: eventURL, flags: flags)
                } catch {
                    print("FileSystemMonitor: Error processing event for \(pathString): \(error)")
                }
            }
        }
    }

    /// Alternative approach to process FSEvents when primary method fails
    private func processEventsAlternativeApproach(
        numEvents: Int,
        eventPaths: UnsafeMutableRawPointer,
        eventFlags: UnsafePointer<FSEventStreamEventFlags>
    ) throws {
        print("FileSystemMonitor: Using alternative FSEvents processing approach")

        // Validate parameters
        guard numEvents > 0 && numEvents < 10000 else {
            throw FileSystemMonitorError.eventProcessingError(
                "Invalid number of events: \(numEvents)")
        }

        // Cast the pointer to the correct type for FSEvents
        let pathsPointer = eventPaths.assumingMemoryBound(to: UnsafePointer<CChar>?.self)

        // Process each event directly without creating CFArray
        for i in 0..<numEvents {
            autoreleasepool {
                guard let pathPtr = pathsPointer[i] else {
                    print("FileSystemMonitor: Null path pointer at index \(i)")
                    return
                }

                // Convert C string to Swift String
                let pathString = String(cString: pathPtr)

                // Validate the path string
                guard !pathString.isEmpty && pathString.count < 4096 else {
                    print("FileSystemMonitor: Invalid path string at index \(i): '\(pathString)'")
                    return
                }

                let eventURL = URL(fileURLWithPath: pathString)
                let flags = eventFlags[i]

                // Process the event with error handling
                do {
                    try processFileSystemEvent(url: eventURL, flags: flags)
                } catch {
                    print("FileSystemMonitor: Error processing event for \(pathString): \(error)")
                }
            }
        }
    }

    /// Disable FSEvents and switch to fallback periodic scanning
    private func disableFSEventsAndUseFallback() {
        guard !isInFallbackMode else { return }

        print("FileSystemMonitor: Switching to fallback mode due to FSEvents corruption")

        startFallbackMode()
    }

    /// Start safe periodic scanning that doesn't rely on FSEvents
    private func startSafePeriodicScan() {
        print("FileSystemMonitor: Starting safe periodic file scanning")

        // Schedule the first scan immediately
        DispatchQueue.global(qos: .background).async {
            self.performSafePeriodicScan()
        }
    }

    /// Perform safe periodic scanning without FSEvents
    private func performSafePeriodicScan() {
        // Only continue if we're in fallback mode
        guard isInFallbackMode else {
            print("FileSystemMonitor: Stopping periodic scan - no longer in fallback mode")
            return
        }

        print("FileSystemMonitor: Performing safe periodic file scan")

        // Perform a basic scan of valid auto spaces only
        let autoSpaces = spaces.values.filter { $0.isValid && $0.mode == "auto" }
        for space in autoSpaces {
            self.performDirectoryScan(spacePath: URL(fileURLWithPath: space.path))
        }

        // Schedule the next scan (every 2 seconds for responsiveness)
        DispatchQueue.global(qos: .background).asyncAfter(deadline: .now() + 2.0) {
            self.performSafePeriodicScan()
        }
    }

    /// Legacy method - kept for compatibility but redirects to safe version
    private func schedulePeriodicScan() {
        performSafePeriodicScan()
    }

    /// Perform a manual directory scan as fallback
    private func performDirectoryScan(spacePath: URL) {
        do {
            let contents = try FileManager.default.contentsOfDirectory(
                at: spacePath, includingPropertiesForKeys: [.contentModificationDateKey],
                options: [.skipsHiddenFiles])

            for fileURL in contents {
                // Check if file was recently modified (within last 10 seconds)
                if let modificationDate = try? fileURL.resourceValues(forKeys: [
                    .contentModificationDateKey
                ]).contentModificationDate,
                    Date().timeIntervalSince(modificationDate) < 10.0
                {

                    // Simulate a file modification event
                    self.eventCallback?(fileURL, .modified)

                    // Auto-create version if needed
                    try? self.autoCreateFileVersion(for: fileURL)
                }
            }
        } catch {
            print("FileSystemMonitor: Error during directory scan: \(error)")
        }
    }

    /// Processes a single file system event (version only on save)
    private func processFileSystemEvent(url: URL, flags: FSEventStreamEventFlags) throws {
        guard !url.path.isEmpty else {
            throw FileSystemMonitorError.invalidPath("Empty path provided")
        }
        if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemIsDir) != 0 { return }
        let fileName = url.lastPathComponent
        if fileName.hasPrefix(".") || fileName.hasPrefix("~") || fileName.hasSuffix(".tmp")
            || fileName.hasSuffix(".swp") || fileName.hasSuffix(".autosave")
        {
            return
        }
        var event: FileSystemEvent = .unknown
        if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemCreated) != 0 {
            event = .created
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemModified) != 0 {
            event = .modified
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemRemoved) != 0 {
            event = .deleted
        } else if flags & FSEventStreamEventFlags(kFSEventStreamEventFlagItemRenamed) != 0 {
            event = .renamed
        }
        eventCallback?(url, event)
        // Only create version if this is a save (Command+S)
        if (event == .modified || event == .created) && isActualFileSave(url: url) {
            if let spacePath = findSpacePath(for: url), isAutoVersioningEnabled(for: spacePath) {
                // TODO: Fix VersionControl reference after dependency injection
                print(
                    "FileSystemMonitor: Would create version for \(url.path) in space: \(spacePath.lastPathComponent)"
                )
            }
        }
        print("FileSystemMonitor: Processed \(event) event for \(fileName)")
    }

    /// Automatically creates a file version when a file is saved (not just modified)
    private func autoCreateFileVersion(for filePath: URL) throws {
        // Suppress auto-versioning if flagged
        if isAutoVersioningSuppressed(for: filePath) {
            return
        }
        guard !filePath.path.isEmpty else { return }
        var isDirectory: ObjCBool = false
        guard FileManager.default.fileExists(atPath: filePath.path, isDirectory: &isDirectory),
            !isDirectory.boolValue
        else { return }
        let fileName = filePath.lastPathComponent
        if fileName.hasPrefix(".") || fileName.hasPrefix("~") || fileName.hasSuffix(".tmp")
            || fileName.hasSuffix(".swp") || fileName.hasSuffix(".autosave")
        {
            return
        }

        // Find the space for this file using the local spaces dictionary
        let space = findSpaceForFile(filePath.path)
        guard let space = space else {
            print("[DEBUG] autoCreateFileVersion: No space found for file=\(filePath.path)")
            return
        }

        // Check if auto versioning is enabled for this space
        guard space.mode == "auto" else {
            print(
                "[DEBUG] autoCreateFileVersion: Skipping version creation for manual space: \(space.name)"
            )
            return
        }

        print(
            "[DEBUG] autoCreateFileVersion: file=\(filePath.path), space=\(space.name), mode=\(space.mode)"
        )

        let now = Date()
        let shouldCreateVersion = throttlingQueue.sync(flags: .barrier) { () -> Bool in
            if let lastVersionTime = lastVersionCreationTimes[filePath.path],
                now.timeIntervalSince(lastVersionTime) < debounceInterval
            {
                return false
            }
            lastVersionCreationTimes[filePath.path] = now
            if lastVersionCreationTimes.count % 20 == 0 { cleanupThrottlingEntries() }
            return true
        }
        guard shouldCreateVersion else { return }
        DispatchQueue.global(qos: .utility).async {
            // TODO: Fix DependencyContainer reference after dependency injection
            // let versionControl = DependencyContainer.shared.versionControl()
            let comment =
                "Auto-version on file save at \(DateFormatter.localizedString(from: now, dateStyle: .short, timeStyle: .medium))"
            print(
                "[DEBUG] createFileVersion (FileSystemMonitor.autoCreateFileVersion): file=\(filePath.path), space=\(space.name), mode=\(space.mode)"
            )
            // TODO: Implement version creation after dependency injection is fixed
            print(
                "FileSystemMonitor: Would create version for \(filePath.path) in space: \(space.name)"
            )
        }
    }

    /// Cleans up old throttling entries to prevent memory leaks
    private func cleanupThrottlingEntries() {
        let now = Date()
        let cutoffTime = now.addingTimeInterval(-300)  // Remove entries older than 5 minutes

        // This method is called from within the barrier queue, so it's already thread-safe
        lastVersionCreationTimes = lastVersionCreationTimes.filter { _, date in
            date > cutoffTime
        }
    }

    /// Cleanup method to call periodically
    public func performMaintenance() {
        throttlingQueue.async(flags: .barrier) {
            self.cleanupThrottlingEntries()
        }
    }

    /// Public method to configure save detection
    public func configureSaveDetection(_ config: SaveDetectionConfig) {
        saveDetectionConfig = config

        // Only set up keyboard monitoring if it's enabled and not already set up
        if config.enableKeyboardMonitoring && globalKeyboardMonitor == nil {
            setupKeyboardMonitoring()
        } else if !config.enableKeyboardMonitoring && globalKeyboardMonitor != nil {
            stopKeyboardMonitoring()
        }

        print(
            "FileSystemMonitor: Save detection configured - keyboard monitoring: \(config.enableKeyboardMonitoring)"
        )
    }

    /// Cleanup debounce timers for files that are deleted or no longer monitored
    private func cleanupDebounceTimers() {
        for (filePath, timer) in debounceTimers {
            if !FileManager.default.fileExists(atPath: filePath) {
                timer.invalidate()
                debounceTimers.removeValue(forKey: filePath)
            }
        }
    }

    /// Public method to configure debounce interval
    public func setDebounceInterval(_ interval: TimeInterval) {
        debounceInterval = interval
    }

    func startMonitoring() {
        guard hasValidSpaces() else {
            print("⚠️ FileSystemMonitor: No valid spaces to monitor")
            return
        }
        let validPaths = getValidSpacePaths()
        // Try to create FSEventStream (pseudo, adapt to your FSEvents setup)
        if createFSEventStream(for: validPaths) {
            print("✅ FileSystemMonitor: Started FSEvents monitoring for \(validPaths.count) paths")
        } else {
            print(
                "⚠️ FileSystemMonitor: FSEvents failed, switching to fallback mode. Some features may be degraded."
            )
            #if canImport(AugmentCore)
                ErrorRecoveryManager.shared.handleError(
                    FileSystemMonitorError.eventProcessingError(
                        "FileSystemMonitor entered fallback mode. Some features may be degraded.")
                )
            #endif
            startFallbackMode()
        }
    }

    private func createFSEventStream(for paths: [String]) -> Bool {
        // Stop any existing stream first
        stopMonitoring()

        guard !paths.isEmpty else {
            print("⚠️ FileSystemMonitor: No paths to monitor")
            return false
        }

        // Create FSEventStream context
        var context = FSEventStreamContext(
            version: 0,
            info: Unmanaged.passUnretained(self).toOpaque(),
            retain: nil,
            release: nil,
            copyDescription: nil
        )

        // Convert paths to CFArray
        let cfPaths = paths as CFArray

        // Create the event stream
        eventStream = FSEventStreamCreate(
            kCFAllocatorDefault,
            { (streamRef, clientCallBackInfo, numEvents, eventPaths, eventFlags, eventIds) in
                // Validate parameters
                guard let clientCallBackInfo = clientCallBackInfo,
                    numEvents > 0 && numEvents < 10000
                else {
                    print("FileSystemMonitor: Invalid FSEvents callback parameters")
                    return
                }

                // Safely extract the monitor instance
                let monitor = Unmanaged<FileSystemMonitor>.fromOpaque(clientCallBackInfo)
                    .takeUnretainedValue()

                // Process events safely
                monitor.processEvents(
                    numEvents: numEvents, eventPaths: eventPaths, eventFlags: eventFlags)
            },
            &context,
            cfPaths,
            FSEventStreamEventId(kFSEventStreamEventIdSinceNow),
            1.0,  // 1 second latency
            FSEventStreamCreateFlags(kFSEventStreamCreateFlagFileEvents)
        )

        guard let eventStream = eventStream else {
            print("❌ FileSystemMonitor: Failed to create FSEventStream")
            return false
        }

        // Schedule the event stream using modern dispatch queue approach
        let queue = DispatchQueue.global(qos: .utility)
        FSEventStreamSetDispatchQueue(eventStream, queue)

        // Start the event stream
        let success = FSEventStreamStart(eventStream)

        if success {
            print("✅ FileSystemMonitor: FSEventStream started successfully")
        } else {
            print("❌ FileSystemMonitor: Failed to start FSEventStream")
            FSEventStreamRelease(eventStream)
            self.eventStream = nil
        }

        return success
    }

    private func startFallbackMode() {
        print("🔄 FileSystemMonitor: Starting fallback mode with periodic scanning")

        // Stop any existing FSEvents monitoring
        stopMonitoring()

        // Mark as in fallback mode
        isInFallbackMode = true

        // Use a timer to periodically scan all valid auto spaces
        // Store the timer reference to prevent it from being deallocated
        fallbackTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) {
            [weak self] _ in
            guard let self = self else { return }

            // Perform scan on background queue to avoid blocking main thread
            DispatchQueue.global(qos: .utility).async {
                self.performFallbackScan()
            }
        }

        // Perform initial scan
        DispatchQueue.global(qos: .utility).async { [weak self] in
            self?.performFallbackScan()
        }

        print("✅ FileSystemMonitor: Fallback mode started successfully")
    }

    private func performFallbackScan() {
        // Scan all valid spaces (both auto and manual) for file changes
        let validSpaces = spaces.values.filter { $0.isValid }

        print("FileSystemMonitor: Scanning \(validSpaces.count) spaces in fallback mode")

        for space in validSpaces {
            // Add safety check to prevent infinite recursion
            scanDirectoryForChanges(at: space.path, depth: 0, maxDepth: 10)
        }
    }

    private func scanDirectoryForChanges(at path: String, depth: Int = 0, maxDepth: Int = 10) {
        // Prevent infinite recursion
        guard depth < maxDepth else {
            print("⚠️ FileSystemMonitor: Max scan depth reached for \(path)")
            return
        }

        let fileManager = FileManager.default
        do {
            let items = try fileManager.contentsOfDirectory(atPath: path)
            for item in items {
                let itemPath = (path as NSString).appendingPathComponent(item)
                if item == ".augment" { continue }
                var isDirectory: ObjCBool = false
                if fileManager.fileExists(atPath: itemPath, isDirectory: &isDirectory) {
                    if isDirectory.boolValue {
                        scanDirectoryForChanges(at: itemPath, depth: depth + 1, maxDepth: maxDepth)
                    } else {
                        checkFileForVersioning(at: itemPath)
                    }
                }
            }
        } catch {
            if (error as NSError).code != 260 {
                print("❌ FileSystemMonitor: Error scanning \(path): \(error)")
            }
        }
    }

    private func checkFileForVersioning(at filePath: String) {
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: filePath)
            if let modificationDate = attributes[.modificationDate] as? Date {
                let timeSinceModification = Date().timeIntervalSince(modificationDate)
                if timeSinceModification < 5.0 {
                    let space = findSpaceForFile(filePath)
                    if let space = space, space.mode == "auto" {
                        // Check if auto-versioning is suppressed for this file
                        let fileURL = URL(fileURLWithPath: filePath)
                        print("🔍 FileSystemMonitor: Checking suppression for: \(filePath)")
                        print("🔍 FileSystemMonitor: Space: \(space.name), Mode: \(space.mode)")
                        if isAutoVersioningSuppressed(for: fileURL) {
                            print(
                                "🚫 FileSystemMonitor: Auto-versioning suppressed for: \(filePath) in space: \(space.name)"
                            )
                            return
                        }

                        print(
                            "🔍 FileSystemMonitor: Suppression check passed, checking if should create version"
                        )
                        if shouldCreateVersion(filePath, spaceName: space.name) {
                            print(
                                "🔄 FileSystemMonitor: Auto-creating version for: \(filePath) in space: \(space.name)"
                            )
                            // TODO: Fix VersionControl reference after dependency injection
                            print(
                                "FileSystemMonitor: Would create version for \(filePath) in space: \(space.name)"
                            )
                        } else {
                            print("🔍 FileSystemMonitor: Should not create version for: \(filePath)")
                        }
                    }
                    // For manual spaces, just log the change but don't create versions
                    else if let space = space, space.mode == "manual" {
                        print(
                            "📝 FileSystemMonitor: File change detected in manual space: \(filePath) in space: \(space.name)"
                        )
                    }
                    // If no space found, log it
                    else {
                        print(
                            "⚠️ FileSystemMonitor: File change detected but no space found: \(filePath)"
                        )
                    }
                } else {
                    print(
                        "🔍 FileSystemMonitor: File \(filePath) modified too long ago (\(timeSinceModification)s), skipping"
                    )
                }
            }
        } catch {
            print("❌ FileSystemMonitor: Error checking file attributes for \(filePath): \(error)")
        }
    }

    private func shouldCreateVersion(_ filePath: String, spaceName: String) -> Bool {
        // TODO: Integrate with your version history logic
        // For now, always return true
        return true
    }

    private func findSpaceForFile(_ filePath: String) -> SpaceConfig? {
        // Find the space whose path is the longest prefix of filePath
        let matches = spaces.values.filter { filePath.hasPrefix($0.path) && $0.isValid }
        return matches.max(by: { $0.path.count < $1.path.count })
    }

    private func hasValidSpaces() -> Bool {
        return spaces.values.contains { $0.isValid }
    }

    private func getValidSpacePaths() -> [String] {
        return spaces.values.compactMap { $0.isValid ? $0.path : nil }
    }

    private func restartMonitoring() {
        self.stopMonitoring()
        self.startMonitoring()
    }
}

/// Represents a file system event
public enum FileSystemEvent {
    /// A file was created
    case created

    /// A file was modified
    case modified

    /// A file was deleted
    case deleted

    /// A file was renamed
    case renamed

    /// An unknown event
    case unknown
}

/// Errors that can occur in FileSystemMonitor
public enum FileSystemMonitorError: Error {
    case invalidPath(String)
    case memoryAccessError(String)
    case eventProcessingError(String)

    var localizedDescription: String {
        switch self {
        case .invalidPath(let message):
            return "Invalid path: \(message)"
        case .memoryAccessError(let message):
            return "Memory access error: \(message)"
        case .eventProcessingError(let message):
            return "Event processing error: \(message)"
        }
    }
}

// MARK: - Extension for easy configuration
extension FileSystemMonitor {
    /// Quick setup for Command+S only detection
    public func enableCommandSSaveDetection() {
        var config = SaveDetectionConfig()
        config.enableKeyboardMonitoring = true
        config.saveDetectionMethods = [.keyboardShortcut]
        config.commandSTimeWindow = 1.0  // 1 second window
        configureSaveDetection(config)
    }

    /// Setup for comprehensive save detection
    public func enableComprehensiveSaveDetection() {
        var config = SaveDetectionConfig()
        config.enableKeyboardMonitoring = true
        config.enableContentHashCheck = true
        config.saveDetectionMethods = [
            .keyboardShortcut, .fileMetadata, .contentHash, .applicationFocus,
        ]
        configureSaveDetection(config)
    }

    /// Setup for lightweight save detection (no keyboard monitoring)
    public func enableLightweightSaveDetection() {
        var config = SaveDetectionConfig()
        config.enableKeyboardMonitoring = false
        config.enableContentHashCheck = false
        config.saveDetectionMethods = [.fileMetadata]
        config.minimumFileSizeChange = 10  // Only create versions for substantial changes
        configureSaveDetection(config)
    }
}

// Fallback protocol/struct declarations for build if not found (remove if not needed)
#if !canImport(AugmentCore)
    protocol SpaceFileSystemProviding { func getSpace(path: URL) -> AugmentSpace? }
    struct AugmentSpace {
        var name: String
        struct Settings { var versioningMode: VersioningMode }
        var settings: Settings
    }
    enum VersioningMode { case manual, auto }
#endif

extension FileSystemMonitor {
    struct SpaceConfig {
        let name: String
        let path: String
        let mode: String  // "auto" or "manual"
        let isValid: Bool
    }
}

extension FileSystemMonitor {
    public func addSpace(name: String, path: String, mode: String) {
        let isValid = FileManager.default.fileExists(atPath: path)
        self.spaces[name] = FileSystemMonitor.SpaceConfig(
            name: name, path: path, mode: mode, isValid: isValid)
        if !isValid {
            print("⚠️ FileSystemMonitor: Space '\(name)' has invalid path: \(path)")
        } else {
            print("✅ FileSystemMonitor: Added valid space '\(name)' at \(path)")
        }
        if self.hasValidSpaces() {
            self.restartMonitoring()
        }
    }

    public func removeSpace(name: String) {
        self.spaces.removeValue(forKey: name)
        self.restartMonitoring()
    }
}
