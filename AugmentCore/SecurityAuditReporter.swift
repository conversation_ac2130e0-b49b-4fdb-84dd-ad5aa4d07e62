import Foundation

/// Security audit reporting and analysis for Augment application
/// Provides comprehensive security event analysis and reporting capabilities
public class SecurityAuditReporter {
    
    // MARK: - Dependencies
    private let securityManager: SecurityManager
    private let logger: AugmentLogger
    
    // MARK: - Initialization
    
    /// Initializer with dependency injection
    /// - Parameters:
    ///   - securityManager: The security manager to get audit data from
    ///   - logger: The logger for reporting operations
    public init(securityManager: SecurityManager, logger: AugmentLogger) {
        self.securityManager = securityManager
        self.logger = logger
    }
    
    // MARK: - Report Generation
    
    /// Generates a comprehensive security audit report
    /// - Parameters:
    ///   - startDate: The start date for the report (optional)
    ///   - endDate: The end date for the report (optional)
    /// - Returns: A detailed security audit report
    public func generateAuditReport(startDate: Date? = nil, endDate: Date? = nil) -> SecurityAuditReport {
        let auditLog = securityManager.getAuditLog()
        let filteredEntries = filterEntries(auditLog, startDate: startDate, endDate: endDate)
        
        let report = SecurityAuditReport(
            generatedAt: Date(),
            startDate: startDate,
            endDate: endDate,
            totalEvents: filteredEntries.count,
            eventsByType: categorizeEvents(filteredEntries),
            securityViolations: extractSecurityViolations(filteredEntries),
            fileAccessSummary: generateFileAccessSummary(filteredEntries),
            riskAssessment: assessSecurityRisks(filteredEntries),
            recommendations: generateRecommendations(filteredEntries)
        )
        
        logger.info("Generated security audit report with \(filteredEntries.count) events", category: .security)
        return report
    }
    
    /// Exports the audit report to various formats
    /// - Parameters:
    ///   - report: The audit report to export
    ///   - format: The export format
    ///   - outputURL: The output file URL
    /// - Returns: True if export was successful
    public func exportReport(_ report: SecurityAuditReport, format: ExportFormat, to outputURL: URL) -> Bool {
        do {
            let exportData: Data
            
            switch format {
            case .json:
                exportData = try JSONEncoder().encode(report)
            case .csv:
                exportData = generateCSVReport(report).data(using: .utf8) ?? Data()
            case .html:
                exportData = generateHTMLReport(report).data(using: .utf8) ?? Data()
            }
            
            try exportData.write(to: outputURL)
            logger.info("Exported security audit report to: \(outputURL.path)", category: .security)
            return true
        } catch {
            logger.error("Failed to export security audit report: \(error)", category: .security)
            return false
        }
    }
    
    // MARK: - Analysis Methods
    
    /// Filters audit entries by date range
    /// - Parameters:
    ///   - entries: The audit entries to filter
    ///   - startDate: The start date (optional)
    ///   - endDate: The end date (optional)
    /// - Returns: Filtered audit entries
    private func filterEntries(_ entries: [SecurityAuditEntry], startDate: Date?, endDate: Date?) -> [SecurityAuditEntry] {
        let formatter = ISO8601DateFormatter()
        
        return entries.filter { entry in
            guard let entryDate = formatter.date(from: entry.timestamp) else { return true }
            
            if let start = startDate, entryDate < start { return false }
            if let end = endDate, entryDate > end { return false }
            
            return true
        }
    }
    
    /// Categorizes events by type
    /// - Parameter entries: The audit entries to categorize
    /// - Returns: Dictionary of event types and their counts
    private func categorizeEvents(_ entries: [SecurityAuditEntry]) -> [String: Int] {
        var categories: [String: Int] = [:]
        
        for entry in entries {
            let eventType = extractEventType(from: entry.event)
            categories[eventType, default: 0] += 1
        }
        
        return categories
    }
    
    /// Extracts security violations from audit entries
    /// - Parameter entries: The audit entries to analyze
    /// - Returns: Array of security violations
    private func extractSecurityViolations(_ entries: [SecurityAuditEntry]) -> [SecurityViolation] {
        return entries.compactMap { entry in
            if entry.event.contains("SECURITY_POLICY_VIOLATION") || 
               entry.event.contains("FILE_ACCESS_DENIED") ||
               entry.event.contains("RESOURCE_ACCESS_FAILED") {
                return SecurityViolation(
                    timestamp: entry.timestamp,
                    type: extractEventType(from: entry.event),
                    description: entry.event,
                    userId: entry.userId,
                    severity: determineSeverity(from: entry.event)
                )
            }
            return nil
        }
    }
    
    /// Generates file access summary
    /// - Parameter entries: The audit entries to analyze
    /// - Returns: File access summary
    private func generateFileAccessSummary(_ entries: [SecurityAuditEntry]) -> FileAccessSummary {
        var totalAccess = 0
        var deniedAccess = 0
        var accessedFiles: Set<String> = []
        
        for entry in entries {
            if entry.event.contains("FILE_ACCESS_GRANTED") {
                totalAccess += 1
                if let filePath = extractFilePath(from: entry.event) {
                    accessedFiles.insert(filePath)
                }
            } else if entry.event.contains("FILE_ACCESS_DENIED") {
                deniedAccess += 1
            }
        }
        
        return FileAccessSummary(
            totalFileAccess: totalAccess,
            deniedFileAccess: deniedAccess,
            uniqueFilesAccessed: accessedFiles.count,
            accessSuccessRate: totalAccess > 0 ? Double(totalAccess - deniedAccess) / Double(totalAccess) : 1.0
        )
    }
    
    /// Assesses security risks based on audit entries
    /// - Parameter entries: The audit entries to analyze
    /// - Returns: Risk assessment
    private func assessSecurityRisks(_ entries: [SecurityAuditEntry]) -> RiskAssessment {
        let violations = extractSecurityViolations(entries)
        let highRiskEvents = violations.filter { $0.severity == .high }.count
        let mediumRiskEvents = violations.filter { $0.severity == .medium }.count
        let lowRiskEvents = violations.filter { $0.severity == .low }.count
        
        let overallRisk: RiskLevel
        if highRiskEvents > 0 {
            overallRisk = .high
        } else if mediumRiskEvents > 5 {
            overallRisk = .medium
        } else if lowRiskEvents > 10 {
            overallRisk = .low
        } else {
            overallRisk = .minimal
        }
        
        return RiskAssessment(
            overallRisk: overallRisk,
            highRiskEvents: highRiskEvents,
            mediumRiskEvents: mediumRiskEvents,
            lowRiskEvents: lowRiskEvents,
            riskFactors: identifyRiskFactors(entries)
        )
    }
    
    /// Generates security recommendations based on audit analysis
    /// - Parameter entries: The audit entries to analyze
    /// - Returns: Array of security recommendations
    private func generateRecommendations(_ entries: [SecurityAuditEntry]) -> [SecurityRecommendation] {
        var recommendations: [SecurityRecommendation] = []
        
        let violations = extractSecurityViolations(entries)
        let fileAccessSummary = generateFileAccessSummary(entries)
        
        // Check for high denial rates
        if fileAccessSummary.accessSuccessRate < 0.9 {
            recommendations.append(SecurityRecommendation(
                priority: .high,
                category: "File Access",
                title: "High File Access Denial Rate",
                description: "File access denial rate is \(String(format: "%.1f", (1.0 - fileAccessSummary.accessSuccessRate) * 100))%. Review file permissions and security policies.",
                action: "Review and adjust file access permissions and security policies"
            ))
        }
        
        // Check for security violations
        if violations.count > 0 {
            recommendations.append(SecurityRecommendation(
                priority: .high,
                category: "Security Violations",
                title: "Security Policy Violations Detected",
                description: "\(violations.count) security violations detected. Immediate review required.",
                action: "Investigate security violations and strengthen security policies"
            ))
        }
        
        // Check for stale bookmarks
        let staleBookmarkEvents = entries.filter { $0.event.contains("stale bookmarks") }
        if !staleBookmarkEvents.isEmpty {
            recommendations.append(SecurityRecommendation(
                priority: .medium,
                category: "Bookmark Management",
                title: "Stale Security Bookmarks",
                description: "Stale security-scoped bookmarks detected. This may affect file access reliability.",
                action: "Clean up stale bookmarks and refresh security-scoped access"
            ))
        }
        
        return recommendations
    }
    
    // MARK: - Helper Methods
    
    private func extractEventType(from event: String) -> String {
        let components = event.components(separatedBy: ":")
        return components.first?.trimmingCharacters(in: .whitespaces) ?? "UNKNOWN"
    }
    
    private func extractFilePath(from event: String) -> String? {
        // Extract file path from event description
        let components = event.components(separatedBy: " on ")
        if components.count > 1 {
            return components[1].components(separatedBy: " ").first
        }
        return nil
    }
    
    private func determineSeverity(from event: String) -> SecurityViolation.Severity {
        if event.contains("SECURITY_POLICY_VIOLATION") {
            return .high
        } else if event.contains("FILE_ACCESS_DENIED") {
            return .medium
        } else {
            return .low
        }
    }
    
    private func identifyRiskFactors(_ entries: [SecurityAuditEntry]) -> [String] {
        var riskFactors: [String] = []
        
        let violations = extractSecurityViolations(entries)
        if violations.count > 10 {
            riskFactors.append("High number of security violations")
        }
        
        let failedAccess = entries.filter { $0.event.contains("ACCESS_FAILED") }
        if failedAccess.count > 5 {
            riskFactors.append("Multiple access failures detected")
        }
        
        return riskFactors
    }
    
    // MARK: - Export Format Generators
    
    private func generateCSVReport(_ report: SecurityAuditReport) -> String {
        var csv = "Timestamp,Event Type,Description,User ID,Process ID\n"
        
        // This would need access to the original entries, simplified for now
        csv += "Report generated at,\(report.generatedAt),Total Events: \(report.totalEvents),,\n"
        
        return csv
    }
    
    private func generateHTMLReport(_ report: SecurityAuditReport) -> String {
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Security Audit Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
                .section { margin: 20px 0; }
                .risk-high { color: red; font-weight: bold; }
                .risk-medium { color: orange; font-weight: bold; }
                .risk-low { color: green; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Security Audit Report</h1>
                <p>Generated: \(report.generatedAt)</p>
                <p>Total Events: \(report.totalEvents)</p>
            </div>
            
            <div class="section">
                <h2>Risk Assessment</h2>
                <p class="risk-\(report.riskAssessment.overallRisk.rawValue)">
                    Overall Risk: \(report.riskAssessment.overallRisk.rawValue.uppercased())
                </p>
            </div>
            
            <div class="section">
                <h2>File Access Summary</h2>
                <p>Total File Access: \(report.fileAccessSummary.totalFileAccess)</p>
                <p>Denied Access: \(report.fileAccessSummary.deniedFileAccess)</p>
                <p>Success Rate: \(String(format: "%.1f", report.fileAccessSummary.accessSuccessRate * 100))%</p>
            </div>
        </body>
        </html>
        """
    }
}

// MARK: - Supporting Types

/// Export formats for audit reports
public enum ExportFormat: String, CaseIterable {
    case json = "json"
    case csv = "csv"
    case html = "html"
}

/// Comprehensive security audit report
public struct SecurityAuditReport: Codable {
    public let generatedAt: Date
    public let startDate: Date?
    public let endDate: Date?
    public let totalEvents: Int
    public let eventsByType: [String: Int]
    public let securityViolations: [SecurityViolation]
    public let fileAccessSummary: FileAccessSummary
    public let riskAssessment: RiskAssessment
    public let recommendations: [SecurityRecommendation]
}

/// Security violation details
public struct SecurityViolation: Codable {
    public let timestamp: String
    public let type: String
    public let description: String
    public let userId: String
    public let severity: Severity
    
    public enum Severity: String, Codable {
        case low = "low"
        case medium = "medium"
        case high = "high"
    }
}

/// File access summary statistics
public struct FileAccessSummary: Codable {
    public let totalFileAccess: Int
    public let deniedFileAccess: Int
    public let uniqueFilesAccessed: Int
    public let accessSuccessRate: Double
}

/// Security risk assessment
public struct RiskAssessment: Codable {
    public let overallRisk: RiskLevel
    public let highRiskEvents: Int
    public let mediumRiskEvents: Int
    public let lowRiskEvents: Int
    public let riskFactors: [String]
}

/// Risk levels for security assessment
public enum RiskLevel: String, Codable {
    case minimal = "minimal"
    case low = "low"
    case medium = "medium"
    case high = "high"
}

/// Security recommendation
public struct SecurityRecommendation: Codable {
    public let priority: Priority
    public let category: String
    public let title: String
    public let description: String
    public let action: String
    
    public enum Priority: String, Codable {
        case low = "low"
        case medium = "medium"
        case high = "high"
    }
}
