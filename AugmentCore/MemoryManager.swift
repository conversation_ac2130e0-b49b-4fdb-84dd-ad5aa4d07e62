import Foundation
import os.log

/// Comprehensive memory management and monitoring for Augment application
/// Provides memory leak detection, optimization, and resource cleanup
public class MemoryManager {
    
    // MARK: - Configuration
    
    /// Memory warning threshold (80% of available memory)
    private static let memoryWarningThreshold: Double = 0.8
    
    /// Memory critical threshold (90% of available memory)
    private static let memoryCriticalThreshold: Double = 0.9
    
    /// Cleanup interval in seconds
    private static let cleanupInterval: TimeInterval = 300 // 5 minutes
    
    // MARK: - Dependencies
    
    private let logger: AugmentLogger
    private let performanceMonitor: PerformanceMonitor
    
    // MARK: - State Management
    
    private var memoryPressureSource: DispatchSourceMemoryPressure?
    private var cleanupTimer: Timer?
    private var isMonitoring = false
    private let monitoringQueue = DispatchQueue(label: "com.augment.memory.monitoring", qos: .utility)
    
    // MARK: - Memory Tracking
    
    private var trackedObjects: NSHashTable<AnyObject> = NSHashTable.weakObjects()
    private var memoryUsageHistory: [MemoryUsageSnapshot] = []
    private let trackingQueue = DispatchQueue(label: "com.augment.memory.tracking", attributes: .concurrent)
    
    // MARK: - Cleanup Handlers
    
    private var cleanupHandlers: [String: () -> Void] = [:]
    private let cleanupQueue = DispatchQueue(label: "com.augment.memory.cleanup")
    
    // MARK: - Initialization
    
    public init(logger: AugmentLogger, performanceMonitor: PerformanceMonitor) {
        self.logger = logger
        self.performanceMonitor = performanceMonitor
        setupMemoryMonitoring()
        startPeriodicCleanup()
    }
    
    deinit {
        stopMonitoring()
    }
    
    // MARK: - Memory Monitoring
    
    /// Starts memory monitoring and pressure detection
    public func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        setupMemoryPressureMonitoring()
        logger.info("Memory monitoring started", category: .performance)
    }
    
    /// Stops memory monitoring
    public func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        memoryPressureSource?.cancel()
        memoryPressureSource = nil
        cleanupTimer?.invalidate()
        cleanupTimer = nil
        logger.info("Memory monitoring stopped", category: .performance)
    }
    
    /// Gets current memory usage information
    /// - Returns: Current memory usage snapshot
    public func getCurrentMemoryUsage() -> MemoryUsageSnapshot {
        let usage = getMemoryUsage()
        let snapshot = MemoryUsageSnapshot(
            timestamp: Date(),
            physicalMemoryUsed: usage.physicalMemoryUsed,
            physicalMemoryTotal: usage.physicalMemoryTotal,
            virtualMemoryUsed: usage.virtualMemoryUsed,
            memoryPressure: usage.memoryPressure,
            trackedObjectCount: trackedObjects.count
        )
        
        trackingQueue.async(flags: .barrier) {
            self.memoryUsageHistory.append(snapshot)
            // Keep only last 100 snapshots
            if self.memoryUsageHistory.count > 100 {
                self.memoryUsageHistory.removeFirst()
            }
        }
        
        return snapshot
    }
    
    /// Gets memory usage history
    /// - Returns: Array of memory usage snapshots
    public func getMemoryUsageHistory() -> [MemoryUsageSnapshot] {
        return trackingQueue.sync {
            return Array(memoryUsageHistory)
        }
    }
    
    // MARK: - Object Tracking
    
    /// Tracks an object for memory leak detection
    /// - Parameter object: Object to track
    public func trackObject(_ object: AnyObject) {
        trackingQueue.async(flags: .barrier) {
            self.trackedObjects.add(object)
        }
    }
    
    /// Gets count of currently tracked objects
    /// - Returns: Number of tracked objects
    public func getTrackedObjectCount() -> Int {
        return trackingQueue.sync {
            return trackedObjects.count
        }
    }
    
    // MARK: - Memory Cleanup
    
    /// Registers a cleanup handler for a specific component
    /// - Parameters:
    ///   - identifier: Unique identifier for the cleanup handler
    ///   - handler: Cleanup closure to execute
    public func registerCleanupHandler(identifier: String, handler: @escaping () -> Void) {
        cleanupQueue.async(flags: .barrier) {
            self.cleanupHandlers[identifier] = handler
        }
    }
    
    /// Unregisters a cleanup handler
    /// - Parameter identifier: Identifier of the handler to remove
    public func unregisterCleanupHandler(identifier: String) {
        cleanupQueue.async(flags: .barrier) {
            self.cleanupHandlers.removeValue(forKey: identifier)
        }
    }
    
    /// Performs immediate memory cleanup
    public func performCleanup() {
        let operationId = performanceMonitor.startOperation("MemoryCleanup")
        
        cleanupQueue.async {
            let startTime = Date()
            var cleanupCount = 0
            
            // Execute all registered cleanup handlers
            for (identifier, handler) in self.cleanupHandlers {
                autoreleasepool {
                    handler()
                    cleanupCount += 1
                    self.logger.debug("Executed cleanup handler: \(identifier)", category: .performance)
                }
            }
            
            // Force garbage collection
            self.forceGarbageCollection()
            
            let duration = Date().timeIntervalSince(startTime)
            self.logger.info("Memory cleanup completed: \(cleanupCount) handlers executed in \(String(format: "%.3f", duration))s", category: .performance)
            
            self.performanceMonitor.endOperation(operationId)
        }
    }
    
    /// Forces garbage collection and memory cleanup
    public func forceGarbageCollection() {
        // Create and release autoreleasepool to force cleanup
        autoreleasepool {
            // Trigger memory cleanup
            if #available(macOS 10.12, *) {
                // Use modern memory management
                DispatchQueue.global(qos: .utility).async {
                    autoreleasepool {
                        // Force cleanup of autorelease objects
                    }
                }
            }
        }
        
        logger.debug("Forced garbage collection", category: .performance)
    }
    
    // MARK: - Memory Optimization
    
    /// Optimizes memory usage based on current conditions
    public func optimizeMemoryUsage() {
        let currentUsage = getCurrentMemoryUsage()
        let memoryPressureRatio = Double(currentUsage.physicalMemoryUsed) / Double(currentUsage.physicalMemoryTotal)
        
        if memoryPressureRatio > Self.memoryCriticalThreshold {
            logger.warning("Critical memory pressure detected (\(String(format: "%.1f", memoryPressureRatio * 100))%), performing aggressive cleanup", category: .performance)
            performAggressiveCleanup()
        } else if memoryPressureRatio > Self.memoryWarningThreshold {
            logger.info("Memory pressure detected (\(String(format: "%.1f", memoryPressureRatio * 100))%), performing standard cleanup", category: .performance)
            performCleanup()
        }
    }
    
    /// Performs aggressive memory cleanup during critical memory pressure
    private func performAggressiveCleanup() {
        let operationId = performanceMonitor.startOperation("AggressiveMemoryCleanup")
        
        cleanupQueue.async {
            // Execute all cleanup handlers
            self.performCleanup()
            
            // Additional aggressive cleanup measures
            self.clearMemoryUsageHistory()
            self.forceGarbageCollection()
            
            // Wait a bit and force another cleanup
            DispatchQueue.global(qos: .utility).asyncAfter(deadline: .now() + 1.0) {
                self.forceGarbageCollection()
                self.performanceMonitor.endOperation(operationId)
            }
        }
    }
    
    /// Clears memory usage history to free memory
    private func clearMemoryUsageHistory() {
        trackingQueue.async(flags: .barrier) {
            // Keep only the last 10 snapshots during aggressive cleanup
            if self.memoryUsageHistory.count > 10 {
                let recentHistory = Array(self.memoryUsageHistory.suffix(10))
                self.memoryUsageHistory = recentHistory
            }
        }
    }
    
    // MARK: - Private Implementation
    
    private func setupMemoryMonitoring() {
        // Initial memory usage snapshot
        _ = getCurrentMemoryUsage()
    }
    
    private func setupMemoryPressureMonitoring() {
        memoryPressureSource = DispatchSource.makeMemoryPressureSource(eventMask: [.warning, .critical], queue: monitoringQueue)
        
        memoryPressureSource?.setEventHandler { [weak self] in
            guard let self = self else { return }
            
            let event = self.memoryPressureSource?.mask
            if event?.contains(.critical) == true {
                self.logger.warning("Critical memory pressure detected by system", category: .performance)
                self.performAggressiveCleanup()
            } else if event?.contains(.warning) == true {
                self.logger.info("Memory pressure warning from system", category: .performance)
                self.performCleanup()
            }
        }
        
        memoryPressureSource?.resume()
    }
    
    private func startPeriodicCleanup() {
        cleanupTimer = Timer.scheduledTimer(withTimeInterval: Self.cleanupInterval, repeats: true) { [weak self] _ in
            self?.optimizeMemoryUsage()
        }
    }
    
    private func getMemoryUsage() -> (physicalMemoryUsed: UInt64, physicalMemoryTotal: UInt64, virtualMemoryUsed: UInt64, memoryPressure: MemoryPressureLevel) {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size) / 4
        
        let result = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }
        
        let physicalMemoryTotal = ProcessInfo.processInfo.physicalMemory
        let physicalMemoryUsed = result == KERN_SUCCESS ? info.resident_size : 0
        let virtualMemoryUsed = result == KERN_SUCCESS ? info.virtual_size : 0
        
        let memoryPressureRatio = Double(physicalMemoryUsed) / Double(physicalMemoryTotal)
        let memoryPressure: MemoryPressureLevel
        
        if memoryPressureRatio > Self.memoryCriticalThreshold {
            memoryPressure = .critical
        } else if memoryPressureRatio > Self.memoryWarningThreshold {
            memoryPressure = .warning
        } else {
            memoryPressure = .normal
        }
        
        return (physicalMemoryUsed, physicalMemoryTotal, virtualMemoryUsed, memoryPressure)
    }
}

// MARK: - Supporting Types

/// Memory usage snapshot for tracking and analysis
public struct MemoryUsageSnapshot: Codable {
    public let timestamp: Date
    public let physicalMemoryUsed: UInt64
    public let physicalMemoryTotal: UInt64
    public let virtualMemoryUsed: UInt64
    public let memoryPressure: MemoryPressureLevel
    public let trackedObjectCount: Int
    
    /// Memory usage percentage (0.0 to 1.0)
    public var memoryUsagePercentage: Double {
        return Double(physicalMemoryUsed) / Double(physicalMemoryTotal)
    }
    
    /// Formatted memory usage string
    public var formattedMemoryUsage: String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .memory
        let used = formatter.string(fromByteCount: Int64(physicalMemoryUsed))
        let total = formatter.string(fromByteCount: Int64(physicalMemoryTotal))
        let percentage = String(format: "%.1f", memoryUsagePercentage * 100)
        return "\(used) / \(total) (\(percentage)%)"
    }
}

/// Memory pressure levels
public enum MemoryPressureLevel: String, Codable {
    case normal = "normal"
    case warning = "warning"
    case critical = "critical"
}

// Import required frameworks
import Darwin.Mach
