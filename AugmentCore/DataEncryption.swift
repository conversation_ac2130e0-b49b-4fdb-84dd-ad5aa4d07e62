import Foundation
import CryptoKit
import Security

/// Data encryption and decryption utilities for Augment application
/// Provides secure encryption for sensitive metadata and version data
public class DataEncryption {
    
    // MARK: - Encryption Configuration
    
    private static let keySize = 32 // 256-bit key
    private static let nonceSize = 12 // 96-bit nonce for AES-GCM
    private static let tagSize = 16 // 128-bit authentication tag
    
    // MARK: - Keychain Configuration
    
    private static let keychainService = "com.augment.encryption"
    private static let masterKeyIdentifier = "augment_master_key"
    
    // MARK: - Error Types
    
    public enum EncryptionError: Error, LocalizedError {
        case keyGenerationFailed
        case keyRetrievalFailed
        case keyStorageFailed
        case encryptionFailed
        case decryptionFailed
        case invalidData
        case keychainError(OSStatus)
        
        public var errorDescription: String? {
            switch self {
            case .keyGenerationFailed:
                return "Failed to generate encryption key"
            case .keyRetrievalFailed:
                return "Failed to retrieve encryption key from keychain"
            case .keyStorageFailed:
                return "Failed to store encryption key in keychain"
            case .encryptionFailed:
                return "Data encryption failed"
            case .decryptionFailed:
                return "Data decryption failed"
            case .invalidData:
                return "Invalid data format for encryption/decryption"
            case .keychainError(let status):
                return "Keychain error: \(status)"
            }
        }
    }
    
    // MARK: - Public Interface
    
    /// Encrypts data using AES-GCM encryption
    /// - Parameter data: The data to encrypt
    /// - Returns: Encrypted data with nonce and tag
    /// - Throws: EncryptionError if encryption fails
    public static func encrypt(_ data: Data) throws -> Data {
        // Get or create master key
        let masterKey = try getMasterKey()
        
        // Generate random nonce
        let nonce = try AES.GCM.Nonce()
        
        // Encrypt data
        let sealedBox = try AES.GCM.seal(data, using: masterKey, nonce: nonce)
        
        // Combine nonce + ciphertext + tag
        var encryptedData = Data()
        encryptedData.append(sealedBox.nonce.withUnsafeBytes { Data($0) })
        encryptedData.append(sealedBox.ciphertext)
        encryptedData.append(sealedBox.tag)
        
        return encryptedData
    }
    
    /// Decrypts data that was encrypted with encrypt()
    /// - Parameter encryptedData: The encrypted data to decrypt
    /// - Returns: Decrypted original data
    /// - Throws: EncryptionError if decryption fails
    public static func decrypt(_ encryptedData: Data) throws -> Data {
        // Validate minimum size (nonce + tag)
        guard encryptedData.count >= nonceSize + tagSize else {
            throw EncryptionError.invalidData
        }
        
        // Extract components
        let nonceData = encryptedData.prefix(nonceSize)
        let tagStart = encryptedData.count - tagSize
        let ciphertext = encryptedData.dropFirst(nonceSize).dropLast(tagSize)
        let tagData = encryptedData.suffix(tagSize)
        
        // Create nonce and tag
        let nonce = try AES.GCM.Nonce(data: nonceData)
        
        // Create sealed box
        let sealedBox = try AES.GCM.SealedBox(nonce: nonce, ciphertext: ciphertext, tag: tagData)
        
        // Get master key and decrypt
        let masterKey = try getMasterKey()
        let decryptedData = try AES.GCM.open(sealedBox, using: masterKey)
        
        return decryptedData
    }
    
    /// Encrypts a string using UTF-8 encoding
    /// - Parameter string: The string to encrypt
    /// - Returns: Encrypted data
    /// - Throws: EncryptionError if encryption fails
    public static func encrypt(_ string: String) throws -> Data {
        guard let data = string.data(using: .utf8) else {
            throw EncryptionError.invalidData
        }
        return try encrypt(data)
    }
    
    /// Decrypts data to a UTF-8 string
    /// - Parameter encryptedData: The encrypted data to decrypt
    /// - Returns: Decrypted string
    /// - Throws: EncryptionError if decryption fails
    public static func decryptToString(_ encryptedData: Data) throws -> String {
        let decryptedData = try decrypt(encryptedData)
        guard let string = String(data: decryptedData, encoding: .utf8) else {
            throw EncryptionError.invalidData
        }
        return string
    }
    
    /// Encrypts a Codable object to JSON and then encrypts the JSON data
    /// - Parameter object: The object to encrypt
    /// - Returns: Encrypted data
    /// - Throws: EncryptionError if encryption fails
    public static func encrypt<T: Codable>(_ object: T) throws -> Data {
        let jsonData = try JSONEncoder().encode(object)
        return try encrypt(jsonData)
    }
    
    /// Decrypts data and decodes it as a Codable object
    /// - Parameters:
    ///   - encryptedData: The encrypted data to decrypt
    ///   - type: The type to decode to
    /// - Returns: Decoded object
    /// - Throws: EncryptionError if decryption or decoding fails
    public static func decrypt<T: Codable>(_ encryptedData: Data, as type: T.Type) throws -> T {
        let decryptedData = try decrypt(encryptedData)
        do {
            return try JSONDecoder().decode(type, from: decryptedData)
        } catch {
            throw EncryptionError.decryptionFailed
        }
    }
    
    // MARK: - Key Management
    
    /// Gets the master encryption key from keychain, creating one if it doesn't exist
    /// - Returns: The master encryption key
    /// - Throws: EncryptionError if key operations fail
    private static func getMasterKey() throws -> SymmetricKey {
        // Try to retrieve existing key
        if let existingKeyData = try? retrieveKeyFromKeychain() {
            return SymmetricKey(data: existingKeyData)
        }
        
        // Generate new key if none exists
        let newKey = SymmetricKey(size: .bits256)
        try storeKeyInKeychain(newKey.withUnsafeBytes { Data($0) })
        
        return newKey
    }
    
    /// Retrieves the encryption key from the keychain
    /// - Returns: The key data
    /// - Throws: EncryptionError if retrieval fails
    private static func retrieveKeyFromKeychain() throws -> Data {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: masterKeyIdentifier,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess else {
            if status == errSecItemNotFound {
                throw EncryptionError.keyRetrievalFailed
            } else {
                throw EncryptionError.keychainError(status)
            }
        }
        
        guard let keyData = result as? Data else {
            throw EncryptionError.keyRetrievalFailed
        }
        
        return keyData
    }
    
    /// Stores the encryption key in the keychain
    /// - Parameter keyData: The key data to store
    /// - Throws: EncryptionError if storage fails
    private static func storeKeyInKeychain(_ keyData: Data) throws {
        // First, try to delete any existing key
        let deleteQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: masterKeyIdentifier
        ]
        SecItemDelete(deleteQuery as CFDictionary)
        
        // Add new key
        let addQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: masterKeyIdentifier,
            kSecValueData as String: keyData,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        let status = SecItemAdd(addQuery as CFDictionary, nil)
        
        guard status == errSecSuccess else {
            throw EncryptionError.keychainError(status)
        }
    }
    
    /// Rotates the master encryption key (for security best practices)
    /// - Throws: EncryptionError if key rotation fails
    public static func rotateMasterKey() throws {
        // Generate new key
        let newKey = SymmetricKey(size: .bits256)
        
        // Store new key in keychain
        try storeKeyInKeychain(newKey.withUnsafeBytes { Data($0) })
        
        // Note: In a production system, you would need to re-encrypt all existing data
        // with the new key. This is a simplified implementation.
    }
    
    /// Deletes the master encryption key from keychain
    /// - Throws: EncryptionError if deletion fails
    public static func deleteMasterKey() throws {
        let deleteQuery: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: keychainService,
            kSecAttrAccount as String: masterKeyIdentifier
        ]
        
        let status = SecItemDelete(deleteQuery as CFDictionary)
        
        guard status == errSecSuccess || status == errSecItemNotFound else {
            throw EncryptionError.keychainError(status)
        }
    }
    
    // MARK: - Utility Methods
    
    /// Generates a secure random salt for key derivation
    /// - Parameter size: The size of the salt in bytes
    /// - Returns: Random salt data
    public static func generateSalt(size: Int = 32) -> Data {
        var salt = Data(count: size)
        _ = salt.withUnsafeMutableBytes { bytes in
            SecRandomCopyBytes(kSecRandomDefault, size, bytes.bindMemory(to: UInt8.self).baseAddress!)
        }
        return salt
    }
    
    /// Derives a key from a password using PBKDF2
    /// - Parameters:
    ///   - password: The password to derive from
    ///   - salt: The salt for key derivation
    ///   - iterations: Number of PBKDF2 iterations (default: 100,000)
    /// - Returns: Derived key
    public static func deriveKey(from password: String, salt: Data, iterations: Int = 100_000) -> SymmetricKey {
        let passwordData = password.data(using: .utf8)!
        let derivedKey = PBKDF2.deriveKey(
            from: passwordData,
            salt: salt,
            using: .sha256,
            outputByteCount: keySize,
            rounds: iterations
        )
        return SymmetricKey(data: derivedKey)
    }
    
    /// Securely compares two data objects to prevent timing attacks
    /// - Parameters:
    ///   - lhs: First data object
    ///   - rhs: Second data object
    /// - Returns: True if data objects are equal
    public static func secureCompare(_ lhs: Data, _ rhs: Data) -> Bool {
        guard lhs.count == rhs.count else { return false }
        
        var result: UInt8 = 0
        for i in 0..<lhs.count {
            result |= lhs[i] ^ rhs[i]
        }
        
        return result == 0
    }
}

// MARK: - PBKDF2 Implementation

private struct PBKDF2 {
    static func deriveKey(from password: Data, salt: Data, using hashFunction: HashFunction, outputByteCount: Int, rounds: Int) -> Data {
        var derivedKey = Data(count: outputByteCount)
        
        derivedKey.withUnsafeMutableBytes { derivedKeyBytes in
            salt.withUnsafeBytes { saltBytes in
                password.withUnsafeBytes { passwordBytes in
                    CCKeyDerivationPBKDF(
                        CCPBKDFAlgorithm(kCCPBKDF2),
                        passwordBytes.bindMemory(to: Int8.self).baseAddress,
                        password.count,
                        saltBytes.bindMemory(to: UInt8.self).baseAddress,
                        salt.count,
                        hashFunction.ccHashFunction,
                        UInt32(rounds),
                        derivedKeyBytes.bindMemory(to: UInt8.self).baseAddress,
                        outputByteCount
                    )
                }
            }
        }
        
        return derivedKey
    }
    
    enum HashFunction {
        case sha256
        
        var ccHashFunction: CCPseudoRandomAlgorithm {
            switch self {
            case .sha256:
                return CCPseudoRandomAlgorithm(kCCPRFHmacAlgSHA256)
            }
        }
    }
}

// Import CommonCrypto for PBKDF2
import CommonCrypto
