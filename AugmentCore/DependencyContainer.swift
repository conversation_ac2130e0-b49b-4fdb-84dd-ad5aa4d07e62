import Foundation

/// Dependency injection container for managing Augment application dependencies
/// This replaces the singleton anti-pattern with proper dependency injection
public class DependencyContainer {

    // MARK: - Singleton for backward compatibility
    /// Shared instance for backward compatibility during transition
    public static let shared = DependencyContainer()

    // MARK: - Core Dependencies
    private var _metadataManager: MetadataManager?
    private var _versionControl: VersionControl?
    private var _searchEngine: SearchEngine?
    private var _fileSystemMonitor: FileSystemMonitor?
    private var _backupManager: BackupManager?
    private var _storageManager: StorageManager?
    private var _preferencesManager: PreferencesManager?
    private var _errorRecoveryManager: ErrorRecoveryManager?
    private var _augmentLogger: AugmentLogger?
    private var _configuration: AugmentConfiguration?
    private var _conflictManager: ConflictManager?
    private var _notificationManager: NotificationManager?
    private var _securityManager: SecurityManager?
    private var _largeFileHandler: LargeFileHandler?
    private var _performanceMonitor: PerformanceMonitor?
    private var _memoryManager: MemoryManager?
    private var _memoryOptimizationService: MemoryOptimizationService?
    private var _backgroundProcessingService: BackgroundProcessingService?

    // MARK: - Thread Safety
    private let containerQueue = DispatchQueue(
        label: "com.augment.dependencycontainer",
        attributes: .concurrent
    )

    /// Private initializer
    private init() {}

    // MARK: - Dependency Factories

    /// Gets or creates MetadataManager instance
    public func metadataManager() -> MetadataManager {
        return containerQueue.sync {
            if let existing = _metadataManager {
                return existing
            }
            let instance = MetadataManager(logger: augmentLogger(), encryptionEnabled: true)
            _metadataManager = instance
            return instance
        }
    }

    /// Gets or creates VersionControl instance
    public func versionControl() -> VersionControl {
        return containerQueue.sync {
            if let existing = _versionControl {
                return existing
            }
            let instance = VersionControl(metadataManager: metadataManager())
            _versionControl = instance
            return instance
        }
    }

    /// Gets or creates SearchEngine instance
    public func searchEngine() -> SearchEngine {
        return containerQueue.sync {
            if let existing = _searchEngine {
                return existing
            }
            let instance = SearchEngine()
            _searchEngine = instance
            return instance
        }
    }

    /// Gets or creates FileSystemMonitor instance
    public func fileSystemMonitor() -> FileSystemMonitor {
        return containerQueue.sync {
            if let existing = _fileSystemMonitor {
                return existing
            }
            let instance = FileSystemMonitor(
                logger: augmentLogger(),
                errorRecoveryManager: errorRecoveryManager(),
                memoryManager: memoryManager()
            )

            // Set up version creation delegate to enable auto-versioning
            instance.versionCreationDelegate = DefaultVersionCreationDelegate()

            _fileSystemMonitor = instance
            return instance
        }
    }

    /// Gets or creates BackupManager instance
    public func backupManager() -> BackupManager {
        return containerQueue.sync {
            if let existing = _backupManager {
                return existing
            }
            let instance = BackupManager()
            _backupManager = instance
            return instance
        }
    }

    /// Gets or creates StorageManager instance
    public func storageManager() -> StorageManager {
        return containerQueue.sync {
            if let existing = _storageManager {
                return existing
            }
            let instance = StorageManager(
                logger: augmentLogger(),
                memoryManager: memoryManager()
            )
            _storageManager = instance
            return instance
        }
    }

    /// Gets or creates PreferencesManager instance
    public func preferencesManager() -> PreferencesManager {
        return containerQueue.sync {
            if let existing = _preferencesManager {
                return existing
            }
            let instance = PreferencesManager(
                configuration: configuration(),
                storageManager: storageManager(),
                notificationManager: notificationManager(),
                logger: augmentLogger()
            )
            _preferencesManager = instance
            return instance
        }
    }

    /// Gets or creates ErrorRecoveryManager instance
    public func errorRecoveryManager() -> ErrorRecoveryManager {
        return containerQueue.sync {
            if let existing = _errorRecoveryManager {
                return existing
            }
            let instance = ErrorRecoveryManager(
                configuration: configuration(),
                preferencesManager: preferencesManager(),
                logger: augmentLogger()
            )
            _errorRecoveryManager = instance
            return instance
        }
    }

    /// Gets or creates AugmentLogger instance
    public func augmentLogger() -> AugmentLogger {
        return containerQueue.sync {
            if let existing = _augmentLogger {
                return existing
            }
            let instance = AugmentLogger()
            _augmentLogger = instance
            return instance
        }
    }

    /// Gets or creates AugmentConfiguration instance
    public func configuration() -> AugmentConfiguration {
        return containerQueue.sync {
            if let existing = _configuration {
                return existing
            }
            let instance = AugmentConfiguration()
            _configuration = instance
            return instance
        }
    }

    /// Gets or creates ConflictManager instance
    public func conflictManager() -> ConflictManager {
        return containerQueue.sync {
            if let existing = _conflictManager {
                return existing
            }
            let instance = ConflictManager()
            _conflictManager = instance
            return instance
        }
    }

    /// Gets or creates NotificationManager instance
    public func notificationManager() -> NotificationManager {
        return containerQueue.sync {
            if let existing = _notificationManager {
                return existing
            }
            let instance = NotificationManager(logger: augmentLogger())
            _notificationManager = instance
            return instance
        }
    }

    /// Gets or creates SecurityManager instance
    public func securityManager() -> SecurityManager {
        return containerQueue.sync {
            if let existing = _securityManager {
                return existing
            }
            let instance = SecurityManager(logger: augmentLogger())
            _securityManager = instance
            return instance
        }
    }

    /// Gets or creates PerformanceMonitor instance
    public func performanceMonitor() -> PerformanceMonitor {
        return containerQueue.sync {
            if let existing = _performanceMonitor {
                return existing
            }
            let instance = PerformanceMonitor(logger: augmentLogger())
            _performanceMonitor = instance
            return instance
        }
    }

    /// Gets or creates LargeFileHandler instance
    public func largeFileHandler() -> LargeFileHandler {
        return containerQueue.sync {
            if let existing = _largeFileHandler {
                return existing
            }
            let instance = LargeFileHandler(
                logger: augmentLogger(),
                performanceMonitor: performanceMonitor()
            )
            _largeFileHandler = instance
            return instance
        }
    }

    /// Gets or creates MemoryManager instance
    public func memoryManager() -> MemoryManager {
        return containerQueue.sync {
            if let existing = _memoryManager {
                return existing
            }
            let instance = MemoryManager(
                logger: augmentLogger(),
                performanceMonitor: performanceMonitor()
            )
            _memoryManager = instance
            return instance
        }
    }

    /// Gets or creates MemoryOptimizationService instance
    public func memoryOptimizationService() -> MemoryOptimizationService {
        return containerQueue.sync {
            if let existing = _memoryOptimizationService {
                return existing
            }
            let instance = MemoryOptimizationService(
                memoryManager: memoryManager(),
                logger: augmentLogger(),
                performanceMonitor: performanceMonitor()
            )
            _memoryOptimizationService = instance
            return instance
        }
    }

    /// Gets or creates BackgroundProcessingService instance
    public func backgroundProcessingService() -> BackgroundProcessingService {
        return containerQueue.sync {
            if let existing = _backgroundProcessingService {
                return existing
            }
            let instance = BackgroundProcessingService(
                logger: augmentLogger(),
                performanceMonitor: performanceMonitor(),
                memoryManager: memoryManager()
            )
            _backgroundProcessingService = instance
            return instance
        }
    }

    // MARK: - Testing Support

    /// Resets all dependencies - useful for testing
    public func reset() {
        containerQueue.async(flags: .barrier) {
            self._metadataManager = nil
            self._versionControl = nil
            self._searchEngine = nil
            self._fileSystemMonitor = nil
            self._backupManager = nil
            self._storageManager = nil
            self._preferencesManager = nil
            self._errorRecoveryManager = nil
            self._augmentLogger = nil
            self._configuration = nil
            self._conflictManager = nil
            self._notificationManager = nil
            self._securityManager = nil
            self._largeFileHandler = nil
            self._performanceMonitor = nil
            self._memoryManager = nil
            self._memoryOptimizationService = nil
            self._backgroundProcessingService = nil
        }
    }

    /// Creates a new container with fresh dependencies - useful for testing
    public static func createTestContainer() -> DependencyContainer {
        let container = DependencyContainer()
        return container
    }

    // MARK: - Dependency Injection Helpers

    /// Injects custom MetadataManager instance (for testing)
    public func inject(metadataManager: MetadataManager) {
        containerQueue.async(flags: .barrier) {
            self._metadataManager = metadataManager
        }
    }

    /// Injects custom VersionControl instance (for testing)
    public func inject(versionControl: VersionControl) {
        containerQueue.async(flags: .barrier) {
            self._versionControl = versionControl
        }
    }

    /// Injects custom SearchEngine instance (for testing)
    public func inject(searchEngine: SearchEngine) {
        containerQueue.async(flags: .barrier) {
            self._searchEngine = searchEngine
        }
    }

    /// Injects custom FileSystemMonitor instance (for testing)
    public func inject(fileSystemMonitor: FileSystemMonitor) {
        containerQueue.async(flags: .barrier) {
            self._fileSystemMonitor = fileSystemMonitor
        }
    }

    /// Injects custom BackupManager instance (for testing)
    public func inject(backupManager: BackupManager) {
        containerQueue.async(flags: .barrier) {
            self._backupManager = backupManager
        }
    }

    /// Injects custom AugmentConfiguration instance (for testing)
    public func inject(configuration: AugmentConfiguration) {
        containerQueue.async(flags: .barrier) {
            self._configuration = configuration
        }
    }

    /// Injects custom PreferencesManager instance (for testing)
    public func inject(preferencesManager: PreferencesManager) {
        containerQueue.async(flags: .barrier) {
            self._preferencesManager = preferencesManager
        }
    }

    /// Injects custom ConflictManager instance (for testing)
    public func inject(conflictManager: ConflictManager) {
        containerQueue.async(flags: .barrier) {
            self._conflictManager = conflictManager
        }
    }

    /// Injects custom NotificationManager instance (for testing)
    public func inject(notificationManager: NotificationManager) {
        containerQueue.async(flags: .barrier) {
            self._notificationManager = notificationManager
        }
    }

    /// Injects custom SecurityManager instance (for testing)
    public func inject(securityManager: SecurityManager) {
        containerQueue.async(flags: .barrier) {
            self._securityManager = securityManager
        }
    }

    /// Injects custom LargeFileHandler instance (for testing)
    public func inject(largeFileHandler: LargeFileHandler) {
        containerQueue.async(flags: .barrier) {
            self._largeFileHandler = largeFileHandler
        }
    }

    /// Injects custom PerformanceMonitor instance (for testing)
    public func inject(performanceMonitor: PerformanceMonitor) {
        containerQueue.async(flags: .barrier) {
            self._performanceMonitor = performanceMonitor
        }
    }

    /// Injects custom MemoryManager instance (for testing)
    public func inject(memoryManager: MemoryManager) {
        containerQueue.async(flags: .barrier) {
            self._memoryManager = memoryManager
        }
    }

    /// Injects custom MemoryOptimizationService instance (for testing)
    public func inject(memoryOptimizationService: MemoryOptimizationService) {
        containerQueue.async(flags: .barrier) {
            self._memoryOptimizationService = memoryOptimizationService
        }
    }

    /// Injects custom BackgroundProcessingService instance (for testing)
    public func inject(backgroundProcessingService: BackgroundProcessingService) {
        containerQueue.async(flags: .barrier) {
            self._backgroundProcessingService = backgroundProcessingService
        }
    }
}

// MARK: - Convenience Extensions

/// Protocol for dependency injection
public protocol DependencyInjectable {
    var dependencies: DependencyContainer { get }
}

/// Default implementation for dependency injection
extension DependencyInjectable {
    public var dependencies: DependencyContainer {
        return DependencyContainer.shared
    }
}
