import Foundation
import os.log

/// Comprehensive background processing service for Augment application
/// Manages heavy operations, task queuing, and thread coordination
public class BackgroundProcessingService {

    // MARK: - Task Priority Levels

    public enum TaskPriority: Int, CaseIterable {
        case low = 0
        case normal = 1
        case high = 2
        case critical = 3

        var qos: DispatchQoS.QoSClass {
            switch self {
            case .low: return .background
            case .normal: return .utility
            case .high: return .userInitiated
            case .critical: return .userInteractive
            }
        }

        var description: String {
            switch self {
            case .low: return "Low"
            case .normal: return "Normal"
            case .high: return "High"
            case .critical: return "Critical"
            }
        }
    }

    // MARK: - Task Types

    public enum TaskType: String, CaseIterable {
        case fileOperation = "file_operation"
        case indexing = "indexing"
        case backup = "backup"
        case cleanup = "cleanup"
        case sync = "sync"
        case analysis = "analysis"
        case compression = "compression"
        case encryption = "encryption"

        var defaultPriority: TaskPriority {
            switch self {
            case .fileOperation, .sync: return .high
            case .indexing, .analysis: return .normal
            case .backup, .compression, .encryption: return .low
            case .cleanup: return .low
            }
        }
    }

    // MARK: - Background Task

    public class BackgroundTask {
        public let id: UUID
        public let name: String
        public let type: TaskType
        public let priority: TaskPriority
        public let createdAt: Date
        public private(set) var startedAt: Date?
        public private(set) var completedAt: Date?
        public private(set) var status: TaskStatus
        public private(set) var progress: Double = 0.0
        public private(set) var error: Error?

        private let workItem: () throws -> Void
        private let progressHandler: ((Double) -> Void)?
        private let completionHandler: ((Result<Void, Error>) -> Void)?

        public enum TaskStatus: String {
            case pending = "pending"
            case running = "running"
            case completed = "completed"
            case failed = "failed"
            case cancelled = "cancelled"
        }

        public var duration: TimeInterval? {
            guard let startedAt = startedAt else { return nil }
            let endTime = completedAt ?? Date()
            return endTime.timeIntervalSince(startedAt)
        }

        public var isCompleted: Bool {
            return [.completed, .failed, .cancelled].contains(status)
        }

        internal init(
            name: String,
            type: TaskType,
            priority: TaskPriority? = nil,
            workItem: @escaping () throws -> Void,
            progressHandler: ((Double) -> Void)? = nil,
            completionHandler: ((Result<Void, Error>) -> Void)? = nil
        ) {
            self.id = UUID()
            self.name = name
            self.type = type
            self.priority = priority ?? type.defaultPriority
            self.createdAt = Date()
            self.status = .pending
            self.workItem = workItem
            self.progressHandler = progressHandler
            self.completionHandler = completionHandler
        }

        internal func updateProgress(_ progress: Double) {
            self.progress = min(1.0, max(0.0, progress))
            progressHandler?(self.progress)
        }

        internal func execute() {
            startedAt = Date()
            status = .running

            do {
                try workItem()
                status = .completed
                completedAt = Date()
                completionHandler?(.success(()))
            } catch {
                self.error = error
                status = .failed
                completedAt = Date()
                completionHandler?(.failure(error))
            }
        }

        internal func cancel() {
            guard status == .pending || status == .running else { return }
            status = .cancelled
            completedAt = Date()
            completionHandler?(.failure(CancellationError()))
        }
    }

    // MARK: - Dependencies

    private let logger: AugmentLogger
    private let performanceMonitor: PerformanceMonitor
    private let memoryManager: MemoryManager?

    // MARK: - Queue Management

    private let taskQueues: [TaskPriority: DispatchQueue]
    private let coordinationQueue = DispatchQueue(
        label: "com.augment.background.coordination", attributes: .concurrent)

    // MARK: - Task Tracking

    private var pendingTasks: [TaskPriority: [BackgroundTask]] = [:]
    private var runningTasks: [UUID: BackgroundTask] = [:]
    private var completedTasks: [BackgroundTask] = []
    private let taskTrackingQueue = DispatchQueue(
        label: "com.augment.background.tracking", attributes: .concurrent)

    // MARK: - Configuration

    public struct ProcessingConfig {
        public let maxConcurrentTasks: [TaskPriority: Int]
        public let taskTimeout: TimeInterval
        public let enableTaskHistory: Bool
        public let maxHistorySize: Int
        public let enableProgressTracking: Bool

        public init(
            maxConcurrentTasks: [TaskPriority: Int] = [
                .critical: 2,
                .high: 3,
                .normal: 4,
                .low: 2,
            ],
            taskTimeout: TimeInterval = 300,  // 5 minutes
            enableTaskHistory: Bool = true,
            maxHistorySize: Int = 1000,
            enableProgressTracking: Bool = true
        ) {
            self.maxConcurrentTasks = maxConcurrentTasks
            self.taskTimeout = taskTimeout
            self.enableTaskHistory = enableTaskHistory
            self.maxHistorySize = maxHistorySize
            self.enableProgressTracking = enableProgressTracking
        }
    }

    private var config: ProcessingConfig

    // MARK: - Statistics

    public struct ProcessingStats {
        public let totalTasksProcessed: Int
        public let tasksInQueue: Int
        public let runningTasks: Int
        public let averageProcessingTime: TimeInterval
        public let successRate: Double
        public let tasksByType: [TaskType: Int]
        public let tasksByPriority: [TaskPriority: Int]
    }

    // MARK: - Initialization

    public init(
        logger: AugmentLogger,
        performanceMonitor: PerformanceMonitor,
        memoryManager: MemoryManager? = nil,
        config: ProcessingConfig = ProcessingConfig()
    ) {
        self.logger = logger
        self.performanceMonitor = performanceMonitor
        self.memoryManager = memoryManager
        self.config = config

        // Initialize task queues for each priority level
        var queues: [TaskPriority: DispatchQueue] = [:]
        for priority in TaskPriority.allCases {
            queues[priority] = DispatchQueue(
                label: "com.augment.background.\(priority.description.lowercased())",
                qos: DispatchQoS(qosClass: priority.qos, relativePriority: 0),
                attributes: .concurrent
            )
        }
        self.taskQueues = queues

        // Initialize pending tasks arrays
        for priority in TaskPriority.allCases {
            pendingTasks[priority] = []
        }

        setupMemoryManagement()
        startTaskProcessing()

        logger.info(
            "BackgroundProcessingService initialized with config: \(config)", category: .performance
        )
    }

    deinit {
        stopTaskProcessing()
    }

    // MARK: - Task Submission

    /// Submits a background task for execution
    /// - Parameters:
    ///   - name: Human-readable name for the task
    ///   - type: Type of task being submitted
    ///   - priority: Priority level (optional, defaults to type's default priority)
    ///   - workItem: The work to be performed
    ///   - progressHandler: Optional progress callback
    ///   - completionHandler: Optional completion callback
    /// - Returns: The created background task
    @discardableResult
    public func submitTask(
        name: String,
        type: TaskType,
        priority: TaskPriority? = nil,
        workItem: @escaping () throws -> Void,
        progressHandler: ((Double) -> Void)? = nil,
        completionHandler: ((Result<Void, Error>) -> Void)? = nil
    ) -> BackgroundTask {

        let task = BackgroundTask(
            name: name,
            type: type,
            priority: priority,
            workItem: workItem,
            progressHandler: progressHandler,
            completionHandler: completionHandler
        )

        taskTrackingQueue.async(flags: .barrier) {
            self.pendingTasks[task.priority, default: []].append(task)
            self.logger.debug(
                "Submitted background task: \(task.name) (\(task.type.rawValue), \(task.priority.description))",
                category: .performance)
        }

        processNextTask(for: task.priority)
        return task
    }

    /// Submits a task with async/await support
    /// - Parameters:
    ///   - name: Human-readable name for the task
    ///   - type: Type of task being submitted
    ///   - priority: Priority level (optional)
    ///   - workItem: The async work to be performed
    /// - Returns: The result of the task execution
    @available(macOS 10.15, *)
    public func submitTask<T>(
        name: String,
        type: TaskType,
        priority: TaskPriority? = nil,
        workItem: @escaping () async throws -> T
    ) async throws -> T {

        return try await withCheckedThrowingContinuation { continuation in
            submitTask(
                name: name,
                type: type,
                priority: priority,
                workItem: {
                    Task {
                        do {
                            let result = try await workItem()
                            continuation.resume(returning: result)
                        } catch {
                            continuation.resume(throwing: error)
                        }
                    }
                },
                completionHandler: { result in
                    switch result {
                    case .success:
                        break  // Result already handled in workItem
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }
            )
        }
    }

    // MARK: - Task Management

    /// Cancels a specific task
    /// - Parameter taskId: ID of the task to cancel
    /// - Returns: True if the task was found and cancelled
    @discardableResult
    public func cancelTask(taskId: UUID) -> Bool {
        return taskTrackingQueue.sync {
            // Check running tasks
            if let task = runningTasks[taskId] {
                task.cancel()
                runningTasks.removeValue(forKey: taskId)
                logger.info("Cancelled running task: \(task.name)", category: .performance)
                return true
            }

            // Check pending tasks
            for priority in TaskPriority.allCases {
                if let index = pendingTasks[priority]?.firstIndex(where: { $0.id == taskId }) {
                    let task = pendingTasks[priority]![index]
                    task.cancel()
                    pendingTasks[priority]?.remove(at: index)
                    logger.info("Cancelled pending task: \(task.name)", category: .performance)
                    return true
                }
            }

            return false
        }
    }

    /// Cancels all tasks of a specific type
    /// - Parameter type: Type of tasks to cancel
    /// - Returns: Number of tasks cancelled
    @discardableResult
    public func cancelTasks(ofType type: TaskType) -> Int {
        return taskTrackingQueue.sync {
            var cancelledCount = 0

            // Cancel running tasks
            let runningTasksToCancel = runningTasks.values.filter { $0.type == type }
            for task in runningTasksToCancel {
                task.cancel()
                runningTasks.removeValue(forKey: task.id)
                cancelledCount += 1
            }

            // Cancel pending tasks
            for priority in TaskPriority.allCases {
                let tasksToCancel = pendingTasks[priority]?.filter { $0.type == type } ?? []
                pendingTasks[priority] = pendingTasks[priority]?.filter { $0.type != type } ?? []

                for task in tasksToCancel {
                    task.cancel()
                    cancelledCount += 1
                }
            }

            if cancelledCount > 0 {
                logger.info(
                    "Cancelled \(cancelledCount) tasks of type \(type.rawValue)",
                    category: .performance)
            }

            return cancelledCount
        }
    }

    /// Gets current processing statistics
    /// - Returns: Processing statistics
    public func getProcessingStats() -> ProcessingStats {
        return taskTrackingQueue.sync {
            let totalCompleted = completedTasks.count
            let totalInQueue = pendingTasks.values.reduce(0) { $0 + $1.count }
            let totalRunning = runningTasks.count

            let completedTasks = self.completedTasks.filter { $0.status == .completed }
            let averageTime =
                completedTasks.isEmpty
                ? 0
                : completedTasks.compactMap { $0.duration }.reduce(0, +)
                    / Double(completedTasks.count)

            let successfulTasks = self.completedTasks.filter { $0.status == .completed }.count
            let successRate =
                totalCompleted > 0 ? Double(successfulTasks) / Double(totalCompleted) : 0

            var tasksByType: [TaskType: Int] = [:]
            var tasksByPriority: [TaskPriority: Int] = [:]

            for task in self.completedTasks {
                tasksByType[task.type, default: 0] += 1
                tasksByPriority[task.priority, default: 0] += 1
            }

            return ProcessingStats(
                totalTasksProcessed: totalCompleted,
                tasksInQueue: totalInQueue,
                runningTasks: totalRunning,
                averageProcessingTime: averageTime,
                successRate: successRate,
                tasksByType: tasksByType,
                tasksByPriority: tasksByPriority
            )
        }
    }

    // MARK: - Private Implementation

    private func setupMemoryManagement() {
        memoryManager?.registerCleanupHandler(identifier: "BackgroundProcessingService") {
            [weak self] in
            self?.performMemoryCleanup()
        }
    }

    private func performMemoryCleanup() {
        taskTrackingQueue.async(flags: .barrier) {
            // Limit completed tasks history
            if self.completedTasks.count > self.config.maxHistorySize {
                let excessCount = self.completedTasks.count - self.config.maxHistorySize
                self.completedTasks.removeFirst(excessCount)
                self.logger.debug(
                    "BackgroundProcessingService: Cleaned up \(excessCount) old task records")
            }
        }
    }

    private func startTaskProcessing() {
        // Task processing is handled on-demand when tasks are submitted
        logger.debug("Background task processing started")
    }

    private func stopTaskProcessing() {
        // Cancel all pending and running tasks
        taskTrackingQueue.sync {
            for tasks in pendingTasks.values {
                for task in tasks {
                    task.cancel()
                }
            }

            for task in runningTasks.values {
                task.cancel()
            }

            pendingTasks.removeAll()
            runningTasks.removeAll()
        }

        logger.info("Background task processing stopped")
    }

    private func processNextTask(for priority: TaskPriority) {
        coordinationQueue.async {
            self.taskTrackingQueue.sync {
                // Check if we can run more tasks of this priority
                let maxConcurrent = self.config.maxConcurrentTasks[priority] ?? 1
                let currentRunning = self.runningTasks.values.filter { $0.priority == priority }
                    .count

                guard currentRunning < maxConcurrent,
                    let nextTask = self.pendingTasks[priority]?.first
                else {
                    return
                }

                // Move task from pending to running
                self.pendingTasks[priority]?.removeFirst()
                self.runningTasks[nextTask.id] = nextTask

                // Execute the task on appropriate queue
                self.taskQueues[priority]?.async {
                    let operationId = self.performanceMonitor.startOperation(
                        "BackgroundTask_\(nextTask.type.rawValue)")

                    nextTask.execute()

                    self.performanceMonitor.endOperation(operationId)

                    // Move task to completed and clean up
                    self.taskTrackingQueue.async(flags: .barrier) {
                        self.runningTasks.removeValue(forKey: nextTask.id)

                        if self.config.enableTaskHistory {
                            self.completedTasks.append(nextTask)
                        }

                        self.logger.debug(
                            "Completed background task: \(nextTask.name) (\(nextTask.status.rawValue))"
                        )

                        // Process next task of same priority
                        self.processNextTask(for: priority)
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Types

public struct CancellationError: Error, LocalizedError {
    public var errorDescription: String? {
        return "Task was cancelled"
    }
}
