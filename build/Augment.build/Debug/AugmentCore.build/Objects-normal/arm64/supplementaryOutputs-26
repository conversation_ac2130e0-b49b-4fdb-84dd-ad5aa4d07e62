"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift":
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues"
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.dia"
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.d"
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftdeps"
"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift":
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.dia"
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues"
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.d"
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftdeps"
"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift":
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.d"
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftdeps"
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o"
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.dia"
