 @(#)PROGRAM:ld PROJECT:ld-1167.5
 /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Accessibility.framework/Accessibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AppKit.framework/AppKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CFNetwork.framework/CFNetwork.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Cocoa.framework/Cocoa.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSync.framework/ColorSync.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Combine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreData.framework/CoreData.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreImage.framework/CoreImage.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/CoreServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreText.framework/CoreText.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreTransferable.framework/CoreTransferable.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreVideo.framework/CoreVideo.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CryptoKit.framework/CryptoKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DataDetection.framework/DataDetection.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework/DiskArbitration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Foundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOKit.framework/IOKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOSurface.framework/IOSurface.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ImageIO.framework/ImageIO.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LocalAuthentication.framework/LocalAuthentication.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metal.framework/Metal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSLog.framework/OSLog.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuartzCore.framework/QuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuickLook.framework/QuickLook.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Security.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUI.framework/SwiftUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Symbols.framework/Symbols.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UserNotifications.framework/UserNotifications.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcups.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreImage.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDarwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDataDetection.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftIOKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftMetal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftOSLog.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObjectiveC.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObservation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftQuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSpatial.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSwiftOnoneSupport.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftUniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftXPC.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Builtin_float.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Concurrency.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_StringProcessing.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_errno.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_math.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_signal.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_stdio.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_time.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftos.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsimd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsys_time.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftunistd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionCreationDelegate.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/lib/darwin/libclang_rt.osx.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AE.framework/Versions/A/AE /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AE.framework/Versions/A/AE.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATS.framework/Versions/A/ATS /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATS.framework/Versions/A/ATS.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATSUI.framework/Versions/A/ATSUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ATSUI.framework/Versions/A/ATSUI.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Accessibility.framework/Accessibility /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/AppKit.framework/AppKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/ApplicationServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATS.framework/Versions/A/ATS /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ATSUI.framework/Versions/A/ATSUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/HIServices.framework/Versions/A/HIServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/PrintCore.framework/Versions/A/PrintCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/QD.framework/Versions/A/QD /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ApplicationServices.framework/Versions/A/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CFNetwork.framework/CFNetwork /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CarbonCore.framework/Versions/A/CarbonCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CarbonCore.framework/Versions/A/CarbonCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Cocoa.framework/Cocoa /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSync.framework/ColorSync /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Combine.framework/Combine /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreData.framework/CoreData /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreFoundation.framework/CoreFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreGraphics.framework/CoreGraphics /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreImage.framework/CoreImage /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/CoreServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/AE.framework/Versions/A/AE /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/CarbonCore.framework/Versions/A/CarbonCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/FSEvents.framework/Versions/A/FSEvents /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/LaunchServices.framework/Versions/A/LaunchServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/Metadata.framework/Versions/A/Metadata /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/OSServices.framework/Versions/A/OSServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SearchKit.framework/Versions/A/SearchKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreServices.framework/Versions/A/Frameworks/SharedFileList.framework/Versions/A/SharedFileList /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreText.framework/CoreText /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreTransferable.framework/CoreTransferable /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CoreVideo.framework/CoreVideo /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/CryptoKit.framework/CryptoKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DataDetection.framework/DataDetection /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DeveloperToolsSupport.framework/DeveloperToolsSupport /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/DiskArbitration.framework/DiskArbitration /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/FSEvents.framework/Versions/A/FSEvents /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/FSEvents.framework/Versions/A/FSEvents.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Foundation.framework/Foundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/HIServices.framework/Versions/A/HIServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/HIServices.framework/Versions/A/HIServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOKit.framework/IOKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/IOSurface.framework/IOSurface /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/ImageIO.framework/ImageIO /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LaunchServices.framework/Versions/A/LaunchServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LaunchServices.framework/Versions/A/LaunchServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/LocalAuthentication.framework/LocalAuthentication /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metadata.framework/Versions/A/Metadata /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metadata.framework/Versions/A/Metadata.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Metal.framework/Metal /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSLog.framework/OSLog /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSServices.framework/Versions/A/OSServices /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OSServices.framework/Versions/A/OSServices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/OpenGL /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGL.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/OpenGL.framework/Versions/A/Libraries/libGLU.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/PrintCore.framework/Versions/A/PrintCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/PrintCore.framework/Versions/A/PrintCore.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QD.framework/Versions/A/QD /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QD.framework/Versions/A/QD.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuartzCore.framework/QuartzCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/QuickLook.framework/QuickLook /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SearchKit.framework/Versions/A/SearchKit /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SearchKit.framework/Versions/A/SearchKit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Security.framework/Security /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SharedFileList.framework/Versions/A/SharedFileList /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SharedFileList.framework/Versions/A/SharedFileList.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUI.framework/SwiftUI /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/SwiftUICore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/SwiftUICore.framework/Versions/A/SwiftUICore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Symbols.framework/Symbols /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UIFoundation.framework/Versions/A/UIFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UIFoundation.framework/Versions/A/UIFoundation.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/UserNotifications.framework/UserNotifications /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/CollectionViewCore.framework/Versions/A/CollectionViewCore /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/PrivateFrameworks/UIFoundation.framework/Versions/A/UIFoundation /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libcups.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcache.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libcups.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.a /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.so /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCore.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreFoundation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftCoreImage.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDarwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDataDetection.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftDispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftFoundation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftIOKit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftMetal.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftOSLog.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObjectiveC.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftObservation.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftQuartzCore.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSpatial.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSwiftOnoneSupport.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftSystem.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftUniformTypeIdentifiers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftXPC.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Builtin_float.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_Concurrency.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_StringProcessing.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_errno.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_math.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_signal.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_stdio.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswift_time.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftos.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsimd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftsys_time.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libswiftunistd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/swift/libxpc.tbd /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcache.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/usr/lib/system/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libSystem.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcache.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcache.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcommonCrypto.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcommonCrypto.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcompiler_rt.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcompiler_rt.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcopyfile.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcopyfile.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcorecrypto.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcorecrypto.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libcups.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdispatch.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdispatch.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdyld.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libdyld.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libkeymgr.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libkeymgr.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libmacho.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libmacho.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libobjc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libquarantine.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libquarantine.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libremovefile.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libremovefile.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCore.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreFoundation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftCoreImage.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDarwin.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDataDetection.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftDispatch.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftFoundation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftIOKit.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftMetal.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftOSLog.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObjectiveC.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftObservation.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftQuartzCore.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSpatial.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSwiftOnoneSupport.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSwiftOnoneSupport.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSwiftOnoneSupport.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSwiftOnoneSupport.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftSystem.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftUniformTypeIdentifiers.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftXPC.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Builtin_float.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_Concurrency.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_StringProcessing.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_errno.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_math.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_signal.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_stdio.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswift_time.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftos.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsimd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftsys_time.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.a /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.so /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libswiftunistd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_asl.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_asl.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_blocks.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_blocks.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_c.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_c.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_collections.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_collections.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_configuration.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_configuration.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_containermanager.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_containermanager.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_coreservices.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_coreservices.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwin.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwin.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwindirectory.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_darwindirectory.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_dnssd.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_dnssd.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_eligibility.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_eligibility.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_featureflags.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_featureflags.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_info.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_info.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_kernel.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_kernel.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_m.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_m.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_malloc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_malloc.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_networkextension.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_networkextension.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_notify.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_notify.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_platform.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_platform.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_pthread.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_pthread.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sandbox.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sandbox.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sanitizers.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_sanitizers.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_secinit.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_secinit.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_symptoms.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_symptoms.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_trace.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libsystem_trace.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libunwind.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libunwind.tbd /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libxpc.dylib /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx/libxpc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AE.framework/Versions/A/AE /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AE.framework/Versions/A/AE.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ATS.framework/Versions/A/ATS /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ATS.framework/Versions/A/ATS.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ATSUI.framework/Versions/A/ATSUI /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ATSUI.framework/Versions/A/ATSUI.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Accessibility.framework/Accessibility /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Accessibility.framework/Accessibility.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AppKit.framework/AppKit /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AppKit.framework/AppKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ApplicationServices.framework/ApplicationServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ApplicationServices.framework/ApplicationServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CFNetwork.framework/CFNetwork /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CFNetwork.framework/CFNetwork.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CarbonCore.framework/Versions/A/CarbonCore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CarbonCore.framework/Versions/A/CarbonCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Cocoa.framework/Cocoa /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Cocoa.framework/Cocoa.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CollectionViewCore.framework/Versions/A/CollectionViewCore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ColorSync.framework/ColorSync /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ColorSync.framework/ColorSync.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Combine.framework/Combine /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Combine.framework/Combine.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreData.framework/CoreData /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreData.framework/CoreData.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreFoundation.framework/CoreFoundation /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreFoundation.framework/CoreFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreGraphics.framework/CoreGraphics /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreGraphics.framework/CoreGraphics.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreImage.framework/CoreImage /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreImage.framework/CoreImage.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreServices.framework/CoreServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreServices.framework/CoreServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreText.framework/CoreText /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreText.framework/CoreText.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreTransferable.framework/CoreTransferable /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreTransferable.framework/CoreTransferable.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreVideo.framework/CoreVideo /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CoreVideo.framework/CoreVideo.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CryptoKit.framework/CryptoKit /Users/<USER>/Downloads/AugmentApp 2/build/Debug/CryptoKit.framework/CryptoKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DataDetection.framework/DataDetection /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DataDetection.framework/DataDetection.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DeveloperToolsSupport.framework/DeveloperToolsSupport /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DictionaryServices.framework/Versions/A/DictionaryServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DiskArbitration.framework/DiskArbitration /Users/<USER>/Downloads/AugmentApp 2/build/Debug/DiskArbitration.framework/DiskArbitration.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/FSEvents.framework/Versions/A/FSEvents /Users/<USER>/Downloads/AugmentApp 2/build/Debug/FSEvents.framework/Versions/A/FSEvents.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Foundation.framework/Foundation /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Foundation.framework/Foundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/HIServices.framework/Versions/A/HIServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/HIServices.framework/Versions/A/HIServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/IOKit.framework/IOKit /Users/<USER>/Downloads/AugmentApp 2/build/Debug/IOKit.framework/IOKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/IOSurface.framework/IOSurface /Users/<USER>/Downloads/AugmentApp 2/build/Debug/IOSurface.framework/IOSurface.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ImageIO.framework/ImageIO /Users/<USER>/Downloads/AugmentApp 2/build/Debug/ImageIO.framework/ImageIO.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/LaunchServices.framework/Versions/A/LaunchServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/LaunchServices.framework/Versions/A/LaunchServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/LocalAuthentication.framework/LocalAuthentication /Users/<USER>/Downloads/AugmentApp 2/build/Debug/LocalAuthentication.framework/LocalAuthentication.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Metadata.framework/Versions/A/Metadata /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Metadata.framework/Versions/A/Metadata.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Metal.framework/Metal /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Metal.framework/Metal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OSLog.framework/OSLog /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OSLog.framework/OSLog.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OSServices.framework/Versions/A/OSServices /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OSServices.framework/Versions/A/OSServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OpenGL.framework/OpenGL /Users/<USER>/Downloads/AugmentApp 2/build/Debug/OpenGL.framework/OpenGL.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/PrintCore.framework/Versions/A/PrintCore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/PrintCore.framework/Versions/A/PrintCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QD.framework/Versions/A/QD /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QD.framework/Versions/A/QD.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QuartzCore.framework/QuartzCore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QuartzCore.framework/QuartzCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QuickLook.framework/QuickLook /Users/<USER>/Downloads/AugmentApp 2/build/Debug/QuickLook.framework/QuickLook.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SearchKit.framework/Versions/A/SearchKit /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SearchKit.framework/Versions/A/SearchKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Security.framework/Security /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Security.framework/Security.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SharedFileList.framework/Versions/A/SharedFileList /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SharedFileList.framework/Versions/A/SharedFileList.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUI.framework/SwiftUI /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUI.framework/SwiftUI.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUICore.framework/SwiftUICore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUICore.framework/SwiftUICore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUICore.framework/Versions/A/SwiftUICore /Users/<USER>/Downloads/AugmentApp 2/build/Debug/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Symbols.framework/Symbols /Users/<USER>/Downloads/AugmentApp 2/build/Debug/Symbols.framework/Symbols.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UIFoundation.framework/Versions/A/UIFoundation /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UIFoundation.framework/Versions/A/UIFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UserNotifications.framework/UserNotifications /Users/<USER>/Downloads/AugmentApp 2/build/Debug/UserNotifications.framework/UserNotifications.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libSystem.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libSystem.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libSystem.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libSystem.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcache.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcache.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcommonCrypto.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcommonCrypto.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcompiler_rt.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcompiler_rt.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcopyfile.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcopyfile.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcorecrypto.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcorecrypto.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcups.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcups.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcups.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libcups.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libdispatch.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libdispatch.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libdyld.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libdyld.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libkeymgr.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libkeymgr.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libmacho.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libmacho.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libobjc.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libobjc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libobjc.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libobjc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libquarantine.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libquarantine.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libremovefile.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libremovefile.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCore.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCore.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCore.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreFoundation.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreFoundation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreFoundation.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreImage.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreImage.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreImage.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftCoreImage.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDarwin.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDarwin.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDarwin.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDarwin.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDataDetection.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDataDetection.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDataDetection.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDataDetection.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDispatch.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDispatch.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDispatch.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftDispatch.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftFoundation.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftFoundation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftFoundation.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftIOKit.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftIOKit.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftIOKit.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftIOKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftMetal.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftMetal.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftMetal.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftMetal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftOSLog.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftOSLog.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftOSLog.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftOSLog.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObjectiveC.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObjectiveC.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObjectiveC.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObjectiveC.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObservation.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObservation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObservation.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftObservation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftQuartzCore.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftQuartzCore.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftQuartzCore.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftQuartzCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSpatial.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSpatial.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSpatial.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSpatial.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSwiftOnoneSupport.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSwiftOnoneSupport.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSwiftOnoneSupport.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSwiftOnoneSupport.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSystem.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSystem.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSystem.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftSystem.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftUniformTypeIdentifiers.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftUniformTypeIdentifiers.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftUniformTypeIdentifiers.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftUniformTypeIdentifiers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftXPC.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftXPC.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftXPC.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftXPC.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Builtin_float.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Builtin_float.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Builtin_float.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Builtin_float.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Concurrency.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Concurrency.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Concurrency.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_Concurrency.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_StringProcessing.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_StringProcessing.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_StringProcessing.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_StringProcessing.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_errno.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_errno.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_errno.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_errno.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_math.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_math.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_math.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_math.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_signal.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_signal.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_signal.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_signal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_stdio.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_stdio.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_stdio.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_stdio.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_time.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_time.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_time.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswift_time.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftos.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftos.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftos.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftos.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsimd.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsimd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsimd.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsimd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsys_time.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsys_time.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsys_time.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftsys_time.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftunistd.a /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftunistd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftunistd.so /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libswiftunistd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_asl.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_asl.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_blocks.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_blocks.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_c.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_c.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_collections.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_collections.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_configuration.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_configuration.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_containermanager.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_containermanager.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_coreservices.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_coreservices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_darwin.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_darwin.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_darwindirectory.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_darwindirectory.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_dnssd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_dnssd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_eligibility.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_eligibility.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_featureflags.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_featureflags.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_info.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_info.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_kernel.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_kernel.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_m.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_m.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_malloc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_malloc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_networkextension.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_networkextension.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_notify.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_notify.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_platform.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_platform.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_pthread.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_pthread.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_sandbox.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_sandbox.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_sanitizers.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_sanitizers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_secinit.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_secinit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_symptoms.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_symptoms.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_trace.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libsystem_trace.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libunwind.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libunwind.tbd /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libxpc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/Debug/libxpc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AE.framework/Versions/A/AE /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AE.framework/Versions/A/AE.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ATS.framework/Versions/A/ATS /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ATS.framework/Versions/A/ATS.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ATSUI.framework/Versions/A/ATSUI /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ATSUI.framework/Versions/A/ATSUI.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Accessibility.framework/Accessibility /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Accessibility.framework/Accessibility.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AppKit.framework/AppKit /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AppKit.framework/AppKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ApplicationServices.framework/ApplicationServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ApplicationServices.framework/ApplicationServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CFNetwork.framework/CFNetwork /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CFNetwork.framework/CFNetwork.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CarbonCore.framework/Versions/A/CarbonCore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CarbonCore.framework/Versions/A/CarbonCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Cocoa.framework/Cocoa /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Cocoa.framework/Cocoa.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CollectionViewCore.framework/Versions/A/CollectionViewCore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CollectionViewCore.framework/Versions/A/CollectionViewCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ColorSync.framework/ColorSync /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ColorSync.framework/ColorSync.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ColorSyncLegacy.framework/Versions/A/ColorSyncLegacy.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Combine.framework/Combine /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Combine.framework/Combine.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreData.framework/CoreData /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreData.framework/CoreData.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreFoundation.framework/CoreFoundation /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreFoundation.framework/CoreFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreGraphics.framework/CoreGraphics /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreGraphics.framework/CoreGraphics.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreImage.framework/CoreImage /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreImage.framework/CoreImage.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreServices.framework/CoreServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreServices.framework/CoreServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreText.framework/CoreText /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreText.framework/CoreText.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreTransferable.framework/CoreTransferable /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreTransferable.framework/CoreTransferable.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreVideo.framework/CoreVideo /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CoreVideo.framework/CoreVideo.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CryptoKit.framework/CryptoKit /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/CryptoKit.framework/CryptoKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DataDetection.framework/DataDetection /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DataDetection.framework/DataDetection.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DeveloperToolsSupport.framework/DeveloperToolsSupport /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DeveloperToolsSupport.framework/DeveloperToolsSupport.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DictionaryServices.framework/Versions/A/DictionaryServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DictionaryServices.framework/Versions/A/DictionaryServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DiskArbitration.framework/DiskArbitration /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/DiskArbitration.framework/DiskArbitration.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/FSEvents.framework/Versions/A/FSEvents /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/FSEvents.framework/Versions/A/FSEvents.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Foundation.framework/Foundation /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Foundation.framework/Foundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/HIServices.framework/Versions/A/HIServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/HIServices.framework/Versions/A/HIServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/IOKit.framework/IOKit /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/IOKit.framework/IOKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/IOSurface.framework/IOSurface /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/IOSurface.framework/IOSurface.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ImageIO.framework/ImageIO /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/ImageIO.framework/ImageIO.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/LaunchServices.framework/Versions/A/LaunchServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/LaunchServices.framework/Versions/A/LaunchServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/LocalAuthentication.framework/LocalAuthentication /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/LocalAuthentication.framework/LocalAuthentication.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Metadata.framework/Versions/A/Metadata /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Metadata.framework/Versions/A/Metadata.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Metal.framework/Metal /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Metal.framework/Metal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OSLog.framework/OSLog /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OSLog.framework/OSLog.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OSServices.framework/Versions/A/OSServices /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OSServices.framework/Versions/A/OSServices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OpenGL.framework/OpenGL /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/OpenGL.framework/OpenGL.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/PrintCore.framework/Versions/A/PrintCore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/PrintCore.framework/Versions/A/PrintCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QD.framework/Versions/A/QD /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QD.framework/Versions/A/QD.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QuartzCore.framework/QuartzCore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QuartzCore.framework/QuartzCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QuickLook.framework/QuickLook /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/QuickLook.framework/QuickLook.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SearchKit.framework/Versions/A/SearchKit /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SearchKit.framework/Versions/A/SearchKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Security.framework/Security /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Security.framework/Security.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SharedFileList.framework/Versions/A/SharedFileList /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SharedFileList.framework/Versions/A/SharedFileList.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SpeechSynthesis.framework/Versions/A/SpeechSynthesis /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SpeechSynthesis.framework/Versions/A/SpeechSynthesis.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUI.framework/SwiftUI /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUI.framework/SwiftUI.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUICore.framework/SwiftUICore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUICore.framework/SwiftUICore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUICore.framework/Versions/A/SwiftUICore /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/SwiftUICore.framework/Versions/A/SwiftUICore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Symbols.framework/Symbols /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/Symbols.framework/Symbols.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UIFoundation.framework/Versions/A/UIFoundation /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UIFoundation.framework/Versions/A/UIFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UniformTypeIdentifiers.framework/UniformTypeIdentifiers /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UniformTypeIdentifiers.framework/UniformTypeIdentifiers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UserNotifications.framework/UserNotifications /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/UserNotifications.framework/UserNotifications.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libSystem.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libSystem.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libSystem.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libSystem.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcache.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcache.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcommonCrypto.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcommonCrypto.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcompiler_rt.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcompiler_rt.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcopyfile.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcopyfile.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcorecrypto.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcorecrypto.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcups.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcups.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcups.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libcups.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libdispatch.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libdispatch.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libdyld.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libdyld.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libkeymgr.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libkeymgr.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libmacho.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libmacho.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libobjc.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libobjc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libobjc.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libobjc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libquarantine.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libquarantine.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libremovefile.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libremovefile.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCore.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCore.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCore.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreFoundation.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreFoundation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreFoundation.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreImage.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreImage.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreImage.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftCoreImage.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDarwin.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDarwin.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDarwin.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDarwin.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDataDetection.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDataDetection.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDataDetection.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDataDetection.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDispatch.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDispatch.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDispatch.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftDispatch.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftFoundation.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftFoundation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftFoundation.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftFoundation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftIOKit.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftIOKit.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftIOKit.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftIOKit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftMetal.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftMetal.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftMetal.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftMetal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftOSLog.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftOSLog.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftOSLog.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftOSLog.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObjectiveC.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObjectiveC.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObjectiveC.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObjectiveC.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObservation.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObservation.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObservation.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftObservation.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftQuartzCore.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftQuartzCore.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftQuartzCore.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftQuartzCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSpatial.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSpatial.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSpatial.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSpatial.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSwiftOnoneSupport.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSwiftOnoneSupport.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSwiftOnoneSupport.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSwiftOnoneSupport.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSystem.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSystem.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSystem.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftSystem.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftUniformTypeIdentifiers.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftUniformTypeIdentifiers.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftUniformTypeIdentifiers.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftUniformTypeIdentifiers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftXPC.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftXPC.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftXPC.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftXPC.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Builtin_float.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Builtin_float.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Builtin_float.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Builtin_float.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Concurrency.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Concurrency.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Concurrency.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_Concurrency.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_StringProcessing.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_StringProcessing.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_StringProcessing.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_StringProcessing.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_errno.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_errno.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_errno.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_errno.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_math.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_math.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_math.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_math.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_signal.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_signal.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_signal.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_signal.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_stdio.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_stdio.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_stdio.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_stdio.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_time.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_time.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_time.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswift_time.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftos.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftos.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftos.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftos.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsimd.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsimd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsimd.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsimd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsys_time.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsys_time.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsys_time.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftsys_time.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftunistd.a /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftunistd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftunistd.so /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libswiftunistd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_asl.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_asl.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_blocks.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_blocks.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_c.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_c.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_collections.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_collections.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_configuration.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_configuration.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_containermanager.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_containermanager.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_coreservices.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_coreservices.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_darwin.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_darwin.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_darwindirectory.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_darwindirectory.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_dnssd.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_dnssd.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_eligibility.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_eligibility.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_featureflags.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_featureflags.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_info.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_info.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_kernel.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_kernel.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_m.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_m.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_malloc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_malloc.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_networkextension.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_networkextension.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_notify.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_notify.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_platform.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_platform.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_pthread.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_pthread.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_sandbox.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_sandbox.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_sanitizers.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_sanitizers.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_secinit.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_secinit.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_symptoms.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_symptoms.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_trace.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libsystem_trace.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libunwind.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libunwind.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libxpc.dylib /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/libxpc.tbd @/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore 