"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift":
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o"
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.d"
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftdeps"
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.dia"
"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift":
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftdeps"
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.dia"
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o"
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.d"
"/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift":
  const-values: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues"
  swift-dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftdeps"
  object: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o"
  dependencies: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.d"
  diagnostics: "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.dia"
