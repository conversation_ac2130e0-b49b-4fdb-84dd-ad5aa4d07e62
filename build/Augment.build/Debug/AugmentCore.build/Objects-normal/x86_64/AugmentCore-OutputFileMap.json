{"": {"diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-master.dia", "emit-module-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-master.swiftdeps"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl~partial.swiftmodule"}, "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift": {"const-values": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.d", "diagnostics": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.dia", "index-unit-output-path": "/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "llvm-bc": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.bc", "object": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "swift-dependencies": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftdeps", "swiftmodule": "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4~partial.swiftmodule"}}