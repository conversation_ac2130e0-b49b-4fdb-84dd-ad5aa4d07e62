<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		LsKapNGN3s9ta/8o/XzAIGLuiKw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			XZDHJQ4IwSYxuU8qM8XNpLX0gXQWpf01dkox8/0yAUk=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			P9dHg5G+VGvYsqzsvzXXvPSyKeB1aU+EiU61+qUVeSg=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			48JLmr0kgs5f4HRllzC630lPBUvYw2hm46/uN9u6UpM=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			42G26sv51ygLchesdYYyyflIMS9yI4T/DD9ng7IICv8=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			wRfYAdW6fK4YlFpqVARdpf3gjmSwSWdXU1Erg2KbBSE=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			42G26sv51ygLchesdYYyyflIMS9yI4T/DD9ng7IICv8=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			RxxnP89LfSyCBjg7ywtMgWawqbLqc3vKHzkX4dz2Dw0=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			48JLmr0kgs5f4HRllzC630lPBUvYw2hm46/uN9u6UpM=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			SxiN6RXUnG3BYWRP72jksD86eIijj+0xG/z/eonbavk=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			Phi68Nqu1MXK7kBKryHVWiJuPJdbC61wlmusDarh068=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			SxiN6RXUnG3BYWRP72jksD86eIijj+0xG/z/eonbavk=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			m7fokHuVELNZoO/xKQWUb4nax/758yXYzO1aTElxcq0=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			O/Jwon87wccAHh43E0uUujPAZbtEB5OrmhpZLnNp6Js=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
