<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		LsKapNGN3s9ta/8o/XzAIGLuiKw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			tP+ZCMMyMi+m4zanUNmPsU4scd38U7PXqXp7s/fGy9E=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			C30QCYRmvyFARWG1C58UZ5Zmj1vfX4c1F3J1LOTWyDw=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cqh8nDsavgQSfsGZBZEHxfqiFb8J5KEQoI7JcBbfrWc=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			LEr6paPc4QsaQRkJHRzWRJ58YQ65XTUK3h/DksOIgnA=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			wQAVGDWWiw/OMXhryArDB8MgTqPYWIw3SB9dutQWruA=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			LEr6paPc4QsaQRkJHRzWRJ58YQ65XTUK3h/DksOIgnA=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			H1xpvTfFxIfPVeFpuuPrEJPL63QlPvVrZcpytkNMkNM=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cqh8nDsavgQSfsGZBZEHxfqiFb8J5KEQoI7JcBbfrWc=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			ef1oMFqVK1Gcy2D/Zg7zbQnJ5W/jKuXKRLSxWjozkrs=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			APgZge2mPd512JcpP28c7xpNZ6D+OL03yKGZ99OI/XA=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			ef1oMFqVK1Gcy2D/Zg7zbQnJ5W/jKuXKRLSxWjozkrs=
			</data>
		</dict>
		<key>Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			WoqQpno5ZKQ49OsrDdCs+ABuHcwXTEtxkmx9AkdNEpk=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			O/Jwon87wccAHh43E0uUujPAZbtEB5OrmhpZLnNp6Js=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
