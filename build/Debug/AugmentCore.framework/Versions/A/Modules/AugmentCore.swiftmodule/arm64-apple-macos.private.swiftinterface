// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.1.2 effective-5.10 (swiftlang-*******.2 clang-1700.0.13.5)
// swift-module-flags: -target arm64-apple-macos15.5 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -Onone -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -module-name AugmentCore
// swift-module-flags-ignorable:  -interface-compiler-version 6.1.2
import Cocoa
import Combine
import CommonCrypto
import CryptoKit
import Darwin
import Foundation
import Darwin.Mach
import QuickLook
import Security
import Swift
import SwiftUI
import UserNotifications
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
import os.log
import os
import os.signpost
public class SearchEngine {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.SearchEngine
  public init()
  public func indexFile(filePath: Foundation.URL, version: AugmentCore.FileVersion) -> Swift.Bool
  public func search(query: Swift.String, spacePaths: [Foundation.URL]) -> [AugmentCore.SearchResult]
  @available(macOS 10.15, *)
  public func searchAsync(query: Swift.String, spacePaths: [Foundation.URL]) async -> [AugmentCore.SearchResult]
  public func indexSpace(spacePath: Foundation.URL) -> Swift.Bool
  @available(macOS 10.15, *)
  public func indexSpaceAsync(spacePath: Foundation.URL) async -> Swift.Bool
  public func removeFile(filePath: Foundation.URL) -> Swift.Bool
  public func clearIndex(spacePath: Foundation.URL) -> Swift.Bool
  public func getIndexStatistics() -> [Swift.String : Any]
  @objc deinit
}
public struct SearchResult : Swift.Identifiable, Swift.Hashable {
  public let id: Foundation.UUID
  public let filePath: Foundation.URL
  public let version: AugmentCore.FileVersion
  public let token: Swift.String
  public let context: Swift.String
  public var relevance: Swift.Double {
    get
  }
  public func hash(into hasher: inout Swift.Hasher)
  public static func == (lhs: AugmentCore.SearchResult, rhs: AugmentCore.SearchResult) -> Swift.Bool
  public typealias ID = Foundation.UUID
  public var hashValue: Swift.Int {
    get
  }
}
public struct FileItem : Swift.Identifiable, Swift.Codable, Swift.Hashable {
  public let id: Foundation.UUID
  public var name: Swift.String
  public var path: Swift.String
  public var type: AugmentCore.FileType
  public var modificationDate: Foundation.Date
  public var versionCount: Swift.Int
  public var size: Swift.Int64
  public var hasConflicts: Swift.Bool
  public var isSyncing: Swift.Bool
  public init(id: Foundation.UUID = UUID(), name: Swift.String, path: Swift.String, type: AugmentCore.FileType, modificationDate: Foundation.Date, versionCount: Swift.Int, size: Swift.Int64 = 0, hasConflicts: Swift.Bool = false, isSyncing: Swift.Bool = false)
  public var url: Foundation.URL {
    get
  }
  public var fileExtension: Swift.String {
    get
  }
  public var formattedSize: Swift.String {
    get
  }
  public var formattedModificationDate: Swift.String {
    get
  }
  public var systemIcon: Swift.String {
    get
  }
  public var typeColor: SwiftUICore.Color {
    get
  }
  public var exists: Swift.Bool {
    get
  }
  public func updatedFromDisk() -> AugmentCore.FileItem?
  public typealias ID = Foundation.UUID
  public func encode(to encoder: any Swift.Encoder) throws
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
extension AugmentCore.FileItem {
  public func hash(into hasher: inout Swift.Hasher)
  public static func == (lhs: AugmentCore.FileItem, rhs: AugmentCore.FileItem) -> Swift.Bool
}
extension AugmentCore.FileItem : Swift.Comparable {
  public static func < (lhs: AugmentCore.FileItem, rhs: AugmentCore.FileItem) -> Swift.Bool
}
@_Concurrency.MainActor @preconcurrency public struct FileItemRow : SwiftUICore.View {
  @_Concurrency.MainActor @preconcurrency public init(file: AugmentCore.FileItem)
  @_Concurrency.MainActor @preconcurrency public var body: some SwiftUICore.View {
    get
  }
  public typealias Body = @_opaqueReturnTypeOf("$s11AugmentCore11FileItemRowV4bodyQrvp", 0) __
}
@objc @_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers public class NotificationManager : ObjectiveC.NSObject, Foundation.ObservableObject {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.NotificationManager
  @Combine.Published @_projectedValueProperty($notificationsEnabled) public var notificationsEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $notificationsEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($storageNotificationsEnabled) public var storageNotificationsEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $storageNotificationsEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($cleanupNotificationsEnabled) public var cleanupNotificationsEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $cleanupNotificationsEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  public init(logger: AugmentCore.AugmentLogger)
  public func sendStorageWarning(spaceName: Swift.String, percentage: Swift.Double)
  public func sendStorageCritical(spaceName: Swift.String)
  public func sendCleanupCompletion(spaceName: Swift.String, removedVersions: Swift.Int, freedBytes: Swift.Int64)
  public func sendErrorRecoverySuccess(errorId: Swift.String)
  public func cancelAllNotifications()
  public func cancelNotifications(for spaceName: Swift.String)
  public func getAuthorizationStatus(completion: @escaping (UserNotifications.UNAuthorizationStatus) -> Swift.Void)
  public typealias ObjectWillChangePublisher = Combine.ObservableObjectPublisher
  @objc deinit
}
extension AugmentCore.NotificationManager : UserNotifications.UNUserNotificationCenterDelegate {
  @objc dynamic public func userNotificationCenter(_ center: UserNotifications.UNUserNotificationCenter, didReceive response: UserNotifications.UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Swift.Void)
  @objc dynamic public func userNotificationCenter(_ center: UserNotifications.UNUserNotificationCenter, willPresent notification: UserNotifications.UNNotification, withCompletionHandler completionHandler: @escaping (UserNotifications.UNNotificationPresentationOptions) -> Swift.Void)
}
@_hasMissingDesignatedInitializers public class DependencyContainer {
  public static let shared: AugmentCore.DependencyContainer
  public func metadataManager() -> AugmentCore.MetadataManager
  public func versionControl() -> AugmentCore.VersionControl
  public func searchEngine() -> AugmentCore.SearchEngine
  public func fileSystemMonitor() -> AugmentCore.FileSystemMonitor
  public func backupManager() -> AugmentCore.BackupManager
  public func storageManager() -> AugmentCore.StorageManager
  public func preferencesManager() -> AugmentCore.PreferencesManager
  public func errorRecoveryManager() -> AugmentCore.ErrorRecoveryManager
  public func augmentLogger() -> AugmentCore.AugmentLogger
  public func configuration() -> AugmentCore.AugmentConfiguration
  public func conflictManager() -> AugmentCore.ConflictManager
  public func notificationManager() -> AugmentCore.NotificationManager
  public func securityManager() -> AugmentCore.SecurityManager
  public func performanceMonitor() -> AugmentCore.PerformanceMonitor
  public func largeFileHandler() -> AugmentCore.LargeFileHandler
  public func memoryManager() -> AugmentCore.MemoryManager
  public func memoryOptimizationService() -> AugmentCore.MemoryOptimizationService
  public func backgroundProcessingService() -> AugmentCore.BackgroundProcessingService
  public func reset()
  public static func createTestContainer() -> AugmentCore.DependencyContainer
  public func inject(metadataManager: AugmentCore.MetadataManager)
  public func inject(versionControl: AugmentCore.VersionControl)
  public func inject(searchEngine: AugmentCore.SearchEngine)
  public func inject(fileSystemMonitor: AugmentCore.FileSystemMonitor)
  public func inject(backupManager: AugmentCore.BackupManager)
  public func inject(configuration: AugmentCore.AugmentConfiguration)
  public func inject(preferencesManager: AugmentCore.PreferencesManager)
  public func inject(conflictManager: AugmentCore.ConflictManager)
  public func inject(notificationManager: AugmentCore.NotificationManager)
  public func inject(securityManager: AugmentCore.SecurityManager)
  public func inject(largeFileHandler: AugmentCore.LargeFileHandler)
  public func inject(performanceMonitor: AugmentCore.PerformanceMonitor)
  public func inject(memoryManager: AugmentCore.MemoryManager)
  public func inject(memoryOptimizationService: AugmentCore.MemoryOptimizationService)
  public func inject(backgroundProcessingService: AugmentCore.BackgroundProcessingService)
  @objc deinit
}
public protocol DependencyInjectable {
  var dependencies: AugmentCore.DependencyContainer { get }
}
extension AugmentCore.DependencyInjectable {
  public var dependencies: AugmentCore.DependencyContainer {
    get
  }
}
@_hasMissingDesignatedInitializers public class AugmentLogger {
  public static let shared: AugmentCore.AugmentLogger
  public enum LogLevel : Swift.Int, Swift.CaseIterable {
    case debug
    case info
    case warning
    case error
    case critical
    public init?(rawValue: Swift.Int)
    public typealias AllCases = [AugmentCore.AugmentLogger.LogLevel]
    public typealias RawValue = Swift.Int
    nonisolated public static var allCases: [AugmentCore.AugmentLogger.LogLevel] {
      get
    }
    public var rawValue: Swift.Int {
      get
    }
  }
  public enum LogCategory : Swift.String, Swift.CaseIterable {
    case general
    case fileSystem
    case versionControl
    case search
    case backup
    case performance
    case security
    case network
    case ui
    case database
    case storage
    case errorRecovery
    case notifications
    case preferences
    public init?(rawValue: Swift.String)
    public typealias AllCases = [AugmentCore.AugmentLogger.LogCategory]
    public typealias RawValue = Swift.String
    nonisolated public static var allCases: [AugmentCore.AugmentLogger.LogCategory] {
      get
    }
    public var rawValue: Swift.String {
      get
    }
  }
  public init(minimumLogLevel: AugmentCore.AugmentLogger.LogLevel = .info, enableFileLogging: Swift.Bool = true)
  public func debug(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func info(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func warning(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func error(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func critical(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func error(_ error: any Swift.Error, message: Swift.String? = nil, category: AugmentCore.AugmentLogger.LogCategory = .general, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line)
  public func setMinimumLogLevel(_ level: AugmentCore.AugmentLogger.LogLevel)
  public func setConsoleLogging(enabled: Swift.Bool)
  public func getLogFileURL() -> Foundation.URL?
  public func measureTime<T>(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .performance, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line, block: () throws -> T) rethrows -> T
  @available(macOS 10.15, *)
  public func measureTimeAsync<T>(_ message: Swift.String, category: AugmentCore.AugmentLogger.LogCategory = .performance, file: Swift.String = #file, function: Swift.String = #function, line: Swift.Int = #line, block: () async throws -> T) async rethrows -> T
  @objc deinit
  public func closeLogFile()
}
public protocol Loggable {
  var logger: AugmentCore.AugmentLogger { get }
}
extension AugmentCore.Loggable {
  public var logger: AugmentCore.AugmentLogger {
    get
  }
}
public protocol SpaceFileSystemProviding {
  func getSpace(path: Foundation.URL) -> AugmentCore.AugmentSpace?
}
public class AugmentConfiguration : Foundation.ObservableObject {
  @Combine.Published @_projectedValueProperty($fileSystemMonitoring) public var fileSystemMonitoring: AugmentCore.FileSystemMonitoringConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $fileSystemMonitoring: Combine.Published<AugmentCore.FileSystemMonitoringConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($performance) public var performance: AugmentCore.PerformanceConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $performance: Combine.Published<AugmentCore.PerformanceConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($storage) public var storage: AugmentCore.StorageConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $storage: Combine.Published<AugmentCore.StorageConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($search) public var search: AugmentCore.SearchConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $search: Combine.Published<AugmentCore.SearchConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($userInterface) public var userInterface: AugmentCore.UserInterfaceConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $userInterface: Combine.Published<AugmentCore.UserInterfaceConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($network) public var network: AugmentCore.NetworkConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $network: Combine.Published<AugmentCore.NetworkConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($security) public var security: AugmentCore.SecurityConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $security: Combine.Published<AugmentCore.SecurityConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($logging) public var logging: AugmentCore.LoggingConfig {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $logging: Combine.Published<AugmentCore.LoggingConfig>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  public init()
  public func loadConfiguration()
  public func saveConfiguration()
  public func resetToDefaults()
  public func validateConfiguration() -> [AugmentCore.ConfigurationError]
  public typealias ObjectWillChangePublisher = Combine.ObservableObjectPublisher
  @objc deinit
}
public struct FileSystemMonitoringConfig : Swift.Codable {
  public var throttleInterval: Swift.Double
  public var maxMonitoredSpaces: Swift.Int
  public var fsEventsLatency: Swift.Double
  public var throttlingCleanupThreshold: Swift.Int
  public var emergencyCleanupThreshold: Swift.Int
  public var throttlingEntryExpiration: Swift.Double
  public var defaultExcludePatterns: [Swift.String]
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct PerformanceConfig : Swift.Codable {
  public var fileBatchSize: Swift.Int
  public var uiUpdatePauseInterval: Swift.Double
  public var memoryCleanupThreshold: Swift.Int
  public var emergencyMemoryCleanupThreshold: Swift.Int
  public var maxPerformanceHistorySize: Swift.Int
  public var maxCompletedOperations: Swift.Int
  public var memoryWarningThreshold: Swift.Int64
  public var cpuWarningThreshold: Swift.Double
  public var responseTimeWarningThreshold: Swift.Double
  public var monitoringInterval: Swift.Double
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct StorageConfig : Swift.Codable {
  public var defaultMaxSizeGB: Swift.Double
  public var defaultWarningThreshold: Swift.Double
  public var defaultMaxVersionAgeDays: Swift.Int
  public var defaultCleanupFrequencyHours: Swift.Int
  public var monitoringInterval: Swift.Double
  public var autoCleanupEnabledByDefault: Swift.Bool
  public var notificationsEnabledByDefault: Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct SearchConfig : Swift.Codable {
  public var maxResults: Swift.Int
  public var indexTimeout: Swift.Double
  public var minQueryLength: Swift.Int
  public var maxQueryLength: Swift.Int
  public var relevanceThreshold: Swift.Double
  public var indexRebuildInterval: Swift.Int
  public var contentIndexingEnabledByDefault: Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct UserInterfaceConfig : Swift.Codable {
  public var defaultWindowWidth: Swift.Double
  public var defaultWindowHeight: Swift.Double
  public var minWindowWidth: Swift.Double
  public var minWindowHeight: Swift.Double
  public var animationDuration: Swift.Double
  public var refreshInterval: Swift.Double
  public var maxListItems: Swift.Int
  public var animationsEnabledByDefault: Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct NetworkConfig : Swift.Codable {
  public var defaultSyncFrequencyMinutes: Swift.Int
  public var networkTimeout: Swift.Double
  public var maxRetryAttempts: Swift.Int
  public var retryDelay: Swift.Double
  public var syncEnabledByDefault: Swift.Bool
  public var maxUploadSize: Swift.Int64
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct SecurityConfig : Swift.Codable {
  public var auditLoggingEnabled: Swift.Bool
  public var securityEventRetentionDays: Swift.Int
  public var fileAccessValidationEnabled: Swift.Bool
  public var encryptionEnabled: Swift.Bool
  public var securityScanInterval: Swift.Int
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct LoggingConfig : Swift.Codable {
  public var fileLoggingEnabled: Swift.Bool
  public var consoleLoggingEnabled: Swift.Bool
  public var logRetentionDays: Swift.Int
  public var maxLogFileSize: Swift.Int64
  public var defaultLogLevel: Swift.String
  public var performanceLoggingEnabled: Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum ConfigurationError : Swift.Error, Foundation.LocalizedError {
  case invalidValue(Swift.String, Swift.String)
  case missingConfiguration(Swift.String)
  case validationFailed(Swift.String)
  public var errorDescription: Swift.String? {
    get
  }
}
extension AugmentCore.AugmentConfiguration {
  public func getValue<T>(keyPath: Swift.KeyPath<AugmentCore.AugmentConfiguration, T>) -> T
  public func exportConfiguration() -> Swift.String?
  public func importConfiguration(from jsonString: Swift.String) -> Swift.Bool
}
extension AugmentCore.AugmentConfiguration {
  public var fileMonitoringThrottleInterval: Foundation.TimeInterval {
    get
  }
  public var fileBatchProcessingSize: Swift.Int {
    get
  }
  public var defaultStorageMaxSizeBytes: Swift.Int64 {
    get
  }
  public var maxSearchResults: Swift.Int {
    get
  }
  public var defaultWindowSize: (width: Swift.Double, height: Swift.Double) {
    get
  }
  public func configureForDevelopment()
  public func configureForProduction()
  public func configureForTesting()
}
@_hasMissingDesignatedInitializers public class ErrorRecoveryManager : Foundation.ObservableObject {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.ErrorRecoveryManager
  @Combine.Published @_projectedValueProperty($activeErrors) public var activeErrors: [AugmentCore.RecoverableError] {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $activeErrors: Combine.Published<[AugmentCore.RecoverableError]>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($isShowingErrorDialog) public var isShowingErrorDialog: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $isShowingErrorDialog: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($currentError) public var currentError: AugmentCore.RecoverableError? {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $currentError: Combine.Published<AugmentCore.RecoverableError?>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($recoveryInProgress) public var recoveryInProgress: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $recoveryInProgress: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  public init(configuration: AugmentCore.AugmentConfiguration, preferencesManager: AugmentCore.PreferencesManager, logger: AugmentCore.AugmentLogger)
  public func handleError(_ error: any Swift.Error, context: Swift.String? = nil, autoRecover: Swift.Bool = true)
  public func getErrorHistory() -> [AugmentCore.ErrorHistoryEntry]
  public func dismissErrorDialog()
  public func executeManualRecovery(_ strategy: AugmentCore.RecoveryStrategy, for error: AugmentCore.RecoverableError)
  public func clearResolvedErrors()
  public typealias ObjectWillChangePublisher = Combine.ObservableObjectPublisher
  @objc deinit
}
public struct RecoverableError : Swift.Identifiable, Swift.Equatable, Swift.Codable {
  public let id: Swift.String
  public let originalError: any Swift.Error
  public let category: AugmentCore.ErrorCategory
  public let context: Swift.String?
  public let timestamp: Foundation.Date
  public let recoveryStrategies: [AugmentCore.RecoveryStrategy]
  public let userMessage: Swift.String
  public let technicalDetails: Swift.String
  public static func == (lhs: AugmentCore.RecoverableError, rhs: AugmentCore.RecoverableError) -> Swift.Bool
  public init(from decoder: any Swift.Decoder) throws
  public func encode(to encoder: any Swift.Encoder) throws
  public init(id: Swift.String, originalError: any Swift.Error, category: AugmentCore.ErrorCategory, context: Swift.String?, timestamp: Foundation.Date, recoveryStrategies: [AugmentCore.RecoveryStrategy], userMessage: Swift.String, technicalDetails: Swift.String)
  public typealias ID = Swift.String
}
public enum ErrorCategory : Swift.String, Swift.CaseIterable, Swift.Codable {
  case fileSystem
  case storage
  case permissions
  case network
  case configuration
  case unknown
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.ErrorCategory]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.ErrorCategory] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
public enum RecoveryStrategy : Swift.String, Swift.CaseIterable, Swift.Codable {
  case retryOperation
  case freeUpSpace
  case checkFilePermissions
  case requestPermissions
  case runCleanup
  case resetConfiguration
  case restoreDefaults
  case checkNetworkConnection
  case recreateFile
  case adjustStorageSettings
  case checkSystemSettings
  case runAsAdministrator
  case workOffline
  case reimportSettings
  case restartApplication
  case contactSupport
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.RecoveryStrategy]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.RecoveryStrategy] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
public struct ErrorHistoryEntry : Swift.Codable {
  public let error: AugmentCore.RecoverableError
  public let occurredAt: Foundation.Date
  public var isResolved: Swift.Bool
  public var resolvedAt: Foundation.Date?
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public class PreferencesManager : Foundation.ObservableObject {
  @Combine.Published @_projectedValueProperty($autoVersioningEnabled) public var autoVersioningEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $autoVersioningEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($storageManagementEnabled) public var storageManagementEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $storageManagementEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($maxStorageGB) public var maxStorageGB: Swift.Double {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $maxStorageGB: Combine.Published<Swift.Double>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($storageWarningThreshold) public var storageWarningThreshold: Swift.Double {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $storageWarningThreshold: Combine.Published<Swift.Double>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($autoCleanupEnabled) public var autoCleanupEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $autoCleanupEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($cleanupFrequencyHours) public var cleanupFrequencyHours: Swift.Int {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $cleanupFrequencyHours: Combine.Published<Swift.Int>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($maxVersionAgeDays) public var maxVersionAgeDays: Swift.Int {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $maxVersionAgeDays: Combine.Published<Swift.Int>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  @Combine.Published @_projectedValueProperty($storageNotificationsEnabled) public var storageNotificationsEnabled: Swift.Bool {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
  }
  public var $storageNotificationsEnabled: Combine.Published<Swift.Bool>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  public init(configuration: AugmentCore.AugmentConfiguration, storageManager: AugmentCore.StorageManager, notificationManager: AugmentCore.NotificationManager, logger: AugmentCore.AugmentLogger)
  public func loadSettings()
  public typealias ObjectWillChangePublisher = Combine.ObservableObjectPublisher
  @objc deinit
}
extension AugmentCore.PreferencesManager {
  public func resetToDefaults()
  public func exportPreferences() -> [Swift.String : Any]
  public func importPreferences(_ preferences: [Swift.String : Any])
}
@_hasMissingDesignatedInitializers public class StorageManager {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.StorageManager
  public init(logger: AugmentCore.AugmentLogger, memoryManager: AugmentCore.MemoryManager? = nil)
  public func getDiskUsage(for space: AugmentCore.AugmentSpace) -> AugmentCore.StorageInfo
  public func getDiskUsage(for spaces: [AugmentCore.AugmentSpace]) -> [Foundation.UUID : AugmentCore.StorageInfo]
  public func getStoragePolicy(for spaceId: Foundation.UUID) -> AugmentCore.StoragePolicy
  public func setStoragePolicy(_ policy: AugmentCore.StoragePolicy, for spaceId: Foundation.UUID)
  public func enforceStoragePolicy(_ policy: AugmentCore.StoragePolicy, for space: AugmentCore.AugmentSpace) -> AugmentCore.PolicyEnforcementResult
  public func cleanupOldVersions(olderThan interval: Foundation.TimeInterval, in space: AugmentCore.AugmentSpace) -> AugmentCore.CleanupResult
  public func alertUserOfStorageIssues(for space: AugmentCore.AugmentSpace, issue: AugmentCore.StorageIssue)
  public func startMonitoring(interval: Foundation.TimeInterval = 300)
  public func stopMonitoring()
  @objc deinit
}
public struct StorageInfo {
  public let totalSize: Swift.Int64
  public let versionCount: Swift.Int
  public let oldestVersion: Foundation.Date
  public let newestVersion: Foundation.Date
  public let spaceUtilization: Swift.Double
  public let spacePath: Foundation.URL
  public let augmentDirectorySize: Swift.Int64
  public let originalFilesSize: Swift.Int64
  public var summary: Swift.String {
    get
  }
}
public struct StoragePolicy : Swift.Codable {
  public let type: AugmentCore.StoragePolicy.PolicyType
  public let enabled: Swift.Bool
  public let warningThreshold: Swift.Double
  public enum PolicyType : Swift.Codable {
    case maxSize(Swift.Int64)
    case maxAge(Swift.Int)
    case maxVersions(Swift.Int)
    public func encode(to encoder: any Swift.Encoder) throws
    public init(from decoder: any Swift.Decoder) throws
  }
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum PolicyEnforcementResult {
  case compliant(Swift.String)
  case warning(Swift.String)
  case enforced(Swift.String)
  case skipped(Swift.String)
}
public enum StorageIssue {
  case approachingLimit(Swift.Double)
  case limitExceeded
  case diskSpaceLow(Swift.Int64)
}
public struct CleanupResult {
  public let removedVersions: Swift.Int
  public let freedBytes: Swift.Int64
  public let errors: [any Swift.Error]
  public var isSuccessful: Swift.Bool {
    get
  }
  public var summary: Swift.String {
    get
  }
}
@_hasMissingDesignatedInitializers public class PerformanceMonitor {
  public static let shared: AugmentCore.PerformanceMonitor
  public struct PerformanceMetrics {
    public let timestamp: Foundation.Date
    public let memoryUsage: Swift.UInt64
    public let cpuUsage: Swift.Double
    public let diskUsage: Swift.UInt64
    public let activeOperations: Swift.Int
    public let averageResponseTime: Foundation.TimeInterval
    public init(timestamp: Foundation.Date = Date(), memoryUsage: Swift.UInt64, cpuUsage: Swift.Double, diskUsage: Swift.UInt64, activeOperations: Swift.Int, averageResponseTime: Foundation.TimeInterval)
  }
  public struct OperationMetrics {
    public let operationName: Swift.String
    public let startTime: CoreFoundation.CFAbsoluteTime
    public let endTime: CoreFoundation.CFAbsoluteTime?
    public let duration: Foundation.TimeInterval?
    public let success: Swift.Bool?
    public let errorMessage: Swift.String?
    public var isCompleted: Swift.Bool {
      get
    }
    public init(operationName: Swift.String, startTime: CoreFoundation.CFAbsoluteTime = CFAbsoluteTimeGetCurrent())
  }
  public init(logger: AugmentCore.AugmentLogger = AugmentLogger.shared)
  @objc deinit
  public func performMemoryCleanup()
  public func startMonitoring(interval: Foundation.TimeInterval = 30.0)
  public func stopMonitoring()
  public func startOperation(_ operationName: Swift.String) -> Foundation.UUID
  public func endOperation(_ operationId: Foundation.UUID, success: Swift.Bool = true, errorMessage: Swift.String? = nil)
  public func measure<T>(_ operationName: Swift.String, block: () throws -> T) rethrows -> T
  @available(macOS 10.15, *)
  public func measureAsync<T>(_ operationName: Swift.String, block: () async throws -> T) async rethrows -> T
  public func getCurrentMetrics() -> AugmentCore.PerformanceMonitor.PerformanceMetrics?
  public func getPerformanceHistory(limit: Swift.Int? = nil) -> [AugmentCore.PerformanceMonitor.PerformanceMetrics]
  public func getActiveOperations() -> [AugmentCore.PerformanceMonitor.OperationMetrics]
  public func getCompletedOperations(limit: Swift.Int? = nil) -> [AugmentCore.PerformanceMonitor.OperationMetrics]
  public func reset()
}
public class NetworkSync {
  public init(fileSystem: any AugmentCore.SpaceFileSystemProviding, conflictManager: AugmentCore.ConflictManager = DependencyContainer.shared.conflictManager())
  public func addConfiguration(_ configuration: AugmentCore.SyncConfiguration) -> Swift.Bool
  public func removeConfiguration(spacePath: Foundation.URL) -> Swift.Bool
  public func getConfiguration(spacePath: Foundation.URL) -> AugmentCore.SyncConfiguration?
  public func getConfigurations() -> [AugmentCore.SyncConfiguration]
  public func syncSpace(spacePath: Foundation.URL) -> Swift.Bool
  public func syncAllSpaces() -> Swift.Bool
  public func performSync(configuration: AugmentCore.SyncConfiguration)
  @objc deinit
}
public struct SyncConfiguration : Swift.Identifiable, Swift.Codable, Swift.Hashable {
  public let id: Foundation.UUID
  public let spacePath: Foundation.URL
  public let remotePath: Foundation.URL
  public let direction: AugmentCore.SyncDirection
  public let frequency: AugmentCore.SyncFrequency
  public var isEnabled: Swift.Bool
  public var lastSyncTimestamp: Foundation.Date?
  public init(id: Foundation.UUID, spacePath: Foundation.URL, remotePath: Foundation.URL, direction: AugmentCore.SyncDirection, frequency: AugmentCore.SyncFrequency, isEnabled: Swift.Bool = true, lastSyncTimestamp: Foundation.Date? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public static func == (a: AugmentCore.SyncConfiguration, b: AugmentCore.SyncConfiguration) -> Swift.Bool
  public typealias ID = Foundation.UUID
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum SyncDirection : Swift.String, Swift.Codable {
  case localToRemote
  case remoteToLocal
  case bidirectional
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum SyncFrequency : Swift.String, Swift.Codable {
  case manual
  case hourly
  case daily
  case weekly
  public var interval: Foundation.TimeInterval {
    get
  }
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public struct AugmentSpace : Swift.Identifiable, Swift.Codable, Swift.Hashable {
  public let id: Foundation.UUID
  public var name: Swift.String
  public var path: Foundation.URL
  public let createdDate: Foundation.Date
  public var lastAccessedDate: Foundation.Date
  public var isMonitoring: Swift.Bool
  public var settings: AugmentCore.SpaceSettings
  public init(id: Foundation.UUID = UUID(), name: Swift.String, path: Foundation.URL, createdDate: Foundation.Date = Date(), isMonitoring: Swift.Bool = true)
  public mutating func updateLastAccessed()
  public func relativePath(for filePath: Foundation.URL) -> Swift.String?
  public func contains(filePath: Foundation.URL) -> Swift.Bool
  public typealias ID = Foundation.UUID
  public func encode(to encoder: any Swift.Encoder) throws
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
public struct SpaceSettings : Swift.Codable, Swift.Hashable {
  public var autoVersioning: Swift.Bool
  public var maxVersionsPerFile: Swift.Int
  public var monitorSubdirectories: Swift.Bool
  public var excludePatterns: [Swift.String]
  public var autoSnapshots: Swift.Bool
  public var snapshotFrequencyHours: Swift.Int
  public var networkSyncEnabled: Swift.Bool
  public var syncConfiguration: AugmentCore.SimpleSyncConfiguration
  public var storageManagementEnabled: Swift.Bool
  public var maxStorageBytes: Swift.Int64
  public var maxVersionAgeDays: Swift.Int
  public var storageWarningThreshold: Swift.Double
  public var autoCleanupEnabled: Swift.Bool
  public var cleanupFrequencyHours: Swift.Int
  public var storageNotificationsEnabled: Swift.Bool
  public var versioningMode: AugmentCore.VersioningMode
  public init(autoVersioning: Swift.Bool = true, maxVersionsPerFile: Swift.Int = 50, monitorSubdirectories: Swift.Bool = true, excludePatterns: [Swift.String] = [
            "*.tmp", "*.temp", "~$*", ".DS_Store", "Thumbs.db",
        ], autoSnapshots: Swift.Bool = false, snapshotFrequencyHours: Swift.Int = 24, networkSyncEnabled: Swift.Bool = false, syncConfiguration: AugmentCore.SimpleSyncConfiguration = SimpleSyncConfiguration(), storageManagementEnabled: Swift.Bool = true, maxStorageBytes: Swift.Int64 = 10 * 1024 * 1024 * 1024, maxVersionAgeDays: Swift.Int = 365, storageWarningThreshold: Swift.Double = 0.8, autoCleanupEnabled: Swift.Bool = true, cleanupFrequencyHours: Swift.Int = 24, storageNotificationsEnabled: Swift.Bool = true, versioningMode: AugmentCore.VersioningMode = .manual)
  public init()
  public static func == (a: AugmentCore.SpaceSettings, b: AugmentCore.SpaceSettings) -> Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
public struct SimpleSyncConfiguration : Swift.Codable, Swift.Hashable {
  public var serverURL: Swift.String
  public var authToken: Swift.String
  public var autoSync: Swift.Bool
  public var syncFrequencyMinutes: Swift.Int
  public init()
  public static func == (a: AugmentCore.SimpleSyncConfiguration, b: AugmentCore.SimpleSyncConfiguration) -> Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
  public init(from decoder: any Swift.Decoder) throws
}
public enum VersioningMode : Swift.String, Swift.Codable {
  case manual
  case auto
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
extension AugmentCore.AugmentSpace {
  public func hash(into hasher: inout Swift.Hasher)
  public static func == (lhs: AugmentCore.AugmentSpace, rhs: AugmentCore.AugmentSpace) -> Swift.Bool
}
public enum FileType : Swift.String, Swift.Codable, Swift.CaseIterable {
  case text
  case image
  case document
  case code
  case other
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.FileType]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.FileType] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
extension AugmentCore.FileType {
  public static func from(url: Foundation.URL) -> AugmentCore.FileType
  public static func from(extension fileExtension: Swift.String) -> AugmentCore.FileType
}
@_hasMissingDesignatedInitializers public class SnapshotManager {
  public static let shared: AugmentCore.SnapshotManager
  public func createSnapshot(spacePath: Foundation.URL, name: Swift.String, description: Swift.String? = nil) -> AugmentCore.Snapshot?
  public func getSnapshots(spacePath: Foundation.URL) -> [AugmentCore.Snapshot]
  public func restoreSnapshot(snapshot: AugmentCore.Snapshot) -> Swift.Bool
  public func deleteSnapshot(snapshot: AugmentCore.Snapshot) -> Swift.Bool
  public func scheduleSnapshots(spacePath: Foundation.URL, schedule: AugmentCore.SnapshotSchedule) -> Swift.Bool
  public func getSchedule(spacePath: Foundation.URL) -> AugmentCore.SnapshotSchedule?
  public func removeSchedule(spacePath: Foundation.URL) -> Swift.Bool
  @objc deinit
}
public struct Snapshot : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let spacePath: Foundation.URL
  public let name: Swift.String
  public let description: Swift.String?
  public let timestamp: Foundation.Date
  public var files: [AugmentCore.SnapshotFile]
  public init(id: Foundation.UUID, spacePath: Foundation.URL, name: Swift.String, description: Swift.String?, timestamp: Foundation.Date, files: [AugmentCore.SnapshotFile])
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public typealias ID = Foundation.UUID
}
public struct SnapshotFile : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let filePath: Foundation.URL
  public let relativePath: Swift.String
  public let size: Swift.UInt64
  public let modificationDate: Foundation.Date
  public init(id: Foundation.UUID, filePath: Foundation.URL, relativePath: Swift.String, size: Swift.UInt64, modificationDate: Foundation.Date)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public typealias ID = Foundation.UUID
}
public struct SnapshotSchedule : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let spacePath: Foundation.URL
  public let frequency: AugmentCore.SnapshotFrequency
  public let retention: AugmentCore.SnapshotRetention
  public var isEnabled: Swift.Bool
  public var lastSnapshotTimestamp: Foundation.Date?
  public init(id: Foundation.UUID, spacePath: Foundation.URL, frequency: AugmentCore.SnapshotFrequency, retention: AugmentCore.SnapshotRetention, isEnabled: Swift.Bool = true, lastSnapshotTimestamp: Foundation.Date? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public typealias ID = Foundation.UUID
}
public enum SnapshotFrequency : Swift.String, Swift.Codable {
  case hourly
  case daily
  case weekly
  case monthly
  public var interval: Foundation.TimeInterval {
    get
  }
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum SnapshotRetention : Swift.Codable {
  case keepAll
  case keepLimited(Swift.Int)
  case keepForTime(Foundation.TimeInterval)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum VersionControlError : Swift.Error {
  case invalidPath(Swift.String)
  case invalidVersion(Swift.String)
  case restorationFailed(Swift.String)
  case rollbackFailed(Swift.String)
  case metadataCorrupted(Swift.String)
}
public struct FileVersion : Swift.Identifiable, Swift.Codable, Swift.Hashable {
  public let id: Foundation.UUID
  public let filePath: Foundation.URL
  public let timestamp: Foundation.Date
  public let size: Swift.UInt64
  public let comment: Swift.String?
  public let contentHash: Swift.String
  public let storagePath: Foundation.URL
  public init(id: Foundation.UUID, filePath: Foundation.URL, timestamp: Foundation.Date, size: Swift.UInt64, comment: Swift.String?, contentHash: Swift.String, storagePath: Foundation.URL)
  public func hash(into hasher: inout Swift.Hasher)
  public static func == (lhs: AugmentCore.FileVersion, rhs: AugmentCore.FileVersion) -> Swift.Bool
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public typealias ID = Foundation.UUID
  public var hashValue: Swift.Int {
    get
  }
}
public enum DiffType : Swift.String, Swift.Codable {
  case text
  case image
  case binary
  case none
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public struct FileDiff {
  public let fromVersion: AugmentCore.FileVersion
  public let toVersion: AugmentCore.FileVersion
  public let diffType: AugmentCore.DiffType
  public let diffData: Foundation.Data
  public init(fromVersion: AugmentCore.FileVersion, toVersion: AugmentCore.FileVersion, diffType: AugmentCore.DiffType, diffData: Foundation.Data)
}
public class VersionControl {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.VersionControl
  public init(metadataManager: AugmentCore.MetadataManager = MetadataManager(logger: AugmentLogger.shared))
  public func initializeVersionControl(folderPath: Foundation.URL) -> Swift.Bool
  public func createVersion(folderPath: Foundation.URL, comment: Swift.String? = nil) -> AugmentCore.FolderVersion?
  public func getVersions(folderPath: Foundation.URL) -> [AugmentCore.FolderVersion]
  public func createFileVersion(filePath: Foundation.URL, comment: Swift.String? = nil) -> AugmentCore.FileVersion?
  @available(macOS 10.15, *)
  public func createFileVersionAsync(filePath: Foundation.URL, comment: Swift.String? = nil) async -> AugmentCore.FileVersion?
  public func getVersions(filePath: Foundation.URL) -> [AugmentCore.FileVersion]
  public func restoreVersion(filePath: Foundation.URL, version: AugmentCore.FileVersion, comment: Swift.String? = nil) -> Swift.Bool
  public func restoreVersion(folderPath: Foundation.URL, version: AugmentCore.FolderVersion) -> Swift.Bool
  @objc deinit
}
public struct FolderVersion : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let folderPath: Foundation.URL
  public let timestamp: Foundation.Date
  public let comment: Swift.String?
  public let storagePath: Foundation.URL
  public typealias ID = Foundation.UUID
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@_hasMissingDesignatedInitializers public class MetadataManager {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.MetadataManager
  public init(logger: AugmentCore.AugmentLogger, encryptionEnabled: Swift.Bool = true)
  public func saveVersionMetadata(version: AugmentCore.FolderVersion, spacePath: Foundation.URL) -> Swift.Bool
  public func loadVersionMetadata(folderPath: Foundation.URL, spacePath: Foundation.URL) -> [AugmentCore.FolderVersion]
  public func deleteVersionMetadata(version: AugmentCore.FolderVersion, spacePath: Foundation.URL) -> Swift.Bool
  public func saveFileVersionMetadata(version: AugmentCore.FileVersion, spacePath: Foundation.URL) -> Swift.Bool
  public func loadFileVersionMetadata(filePath: Foundation.URL, spacePath: Foundation.URL) -> [AugmentCore.FileVersion]
  public func deleteFileVersionMetadata(version: AugmentCore.FileVersion, spacePath: Foundation.URL) -> Swift.Bool
  @objc deinit
}
public struct FileConflict : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let filePath: Foundation.URL
  public let conflictType: AugmentCore.ConflictType
  public let localVersion: AugmentCore.FileVersion
  public let remoteVersion: AugmentCore.FileVersion
  public let timestamp: Foundation.Date
  public var localModificationDate: Foundation.Date {
    get
  }
  public var remoteModificationDate: Foundation.Date {
    get
  }
  public init(filePath: Foundation.URL, conflictType: AugmentCore.ConflictType, localVersion: AugmentCore.FileVersion, remoteVersion: AugmentCore.FileVersion)
  public typealias ID = Foundation.UUID
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum ConflictType : Swift.String, Swift.Codable, Swift.CaseIterable {
  case contentConflict
  case deleteConflict
  case renameConflict
  case permissionConflict
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.ConflictType]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.ConflictType] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
public class ConflictManager : Foundation.ObservableObject {
  @Combine.Published @_projectedValueProperty($activeConflicts) public var activeConflicts: [AugmentCore.FileConflict] {
    get
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    set
    @available(iOS 13.0, tvOS 13.0, watchOS 6.0, macOS 10.15, *)
    _modify
  }
  public var $activeConflicts: Combine.Published<[AugmentCore.FileConflict]>.Publisher {
    get
    @available(iOS 14.0, tvOS 14.0, watchOS 7.0, macOS 11.0, *)
    set
  }
  public init()
  public func detectConflicts(for filePath: Foundation.URL) -> [AugmentCore.FileConflict]
  public func resolveConflict(_ conflict: AugmentCore.FileConflict, resolution: AugmentCore.ConflictResolution) -> Swift.Bool
  public func getActiveConflicts() -> [AugmentCore.FileConflict]
  public func getActiveConflicts(spacePath: Foundation.URL) -> [AugmentCore.FileConflict]
  public func addConflict(_ conflict: AugmentCore.FileConflict)
  public typealias ObjectWillChangePublisher = Combine.ObservableObjectPublisher
  @objc deinit
}
public enum ConflictResolution : Swift.String, Swift.CaseIterable {
  case useLocal
  case useRemote
  case merge
  case createCopy
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.ConflictResolution]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.ConflictResolution] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
@_hasMissingDesignatedInitializers public class ConflictResolutionUtilities {
  public static func hasPotentialConflicts(filePath: Foundation.URL, spacePath: Foundation.URL) -> Swift.Bool
  @objc deinit
}
public enum ConflictResolutionType {
  case keepLocal
  case keepRemote
  case keepBoth
  case custom
  public static func == (a: AugmentCore.ConflictResolutionType, b: AugmentCore.ConflictResolutionType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
@_hasMissingDesignatedInitializers public class PreviewEngine {
  public static let shared: AugmentCore.PreviewEngine
  public func generatePreview(filePath: Foundation.URL, size: CoreFoundation.CGSize) -> AppKit.NSImage?
  @objc deinit
}
public enum PreviewFileType {
  case text
  case image
  case pdf
  case other
  public static func == (a: AugmentCore.PreviewFileType, b: AugmentCore.PreviewFileType) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public struct DiffOperation : Swift.Codable {
  public let type: AugmentCore.DiffOperation.OperationType
  public let content: Swift.String
  public let lineNumber: Swift.Int?
  public init(type: AugmentCore.DiffOperation.OperationType, content: Swift.String, lineNumber: Swift.Int? = nil)
  public enum OperationType : Swift.String, Swift.Codable {
    case unchanged
    case added
    case removed
    public init?(rawValue: Swift.String)
    public typealias RawValue = Swift.String
    public var rawValue: Swift.String {
      get
    }
  }
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@_hasMissingDesignatedInitializers public class DiffEngine {
  public static let shared: AugmentCore.DiffEngine
  public func generateDiff(fromVersion: AugmentCore.FileVersion, toVersion: AugmentCore.FileVersion) -> AugmentCore.FileDiff
  @objc deinit
}
public class BackupManager {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.BackupManager
  public init()
  public func addConfiguration(_ configuration: AugmentCore.BackupConfiguration) -> Swift.Bool
  public func removeConfiguration(spacePath: Foundation.URL) -> Swift.Bool
  public func getConfiguration(spacePath: Foundation.URL) -> AugmentCore.BackupConfiguration?
  public func getConfigurations() -> [AugmentCore.BackupConfiguration]
  public func createBackup(spacePath: Foundation.URL) -> Swift.Bool
  public func createAllBackups() -> Swift.Bool
  public func restoreBackup(backupPath: Foundation.URL, spacePath: Foundation.URL, password: Swift.String? = nil) -> Swift.Bool
  public func restoreBackup(backupPath: Foundation.URL, spacePath: Foundation.URL, password: Swift.String? = nil, completion: @escaping (Swift.Bool) -> Swift.Void)
  @objc deinit
}
public struct BackupConfiguration : Swift.Identifiable, Swift.Codable {
  public let id: Foundation.UUID
  public let spacePath: Foundation.URL
  public let backupPath: Foundation.URL
  public let frequency: AugmentCore.BackupFrequency
  public let retention: AugmentCore.BackupRetention
  public let isEncrypted: Swift.Bool
  public let password: Swift.String?
  public var isEnabled: Swift.Bool
  public var lastBackupTimestamp: Foundation.Date?
  public init(id: Foundation.UUID, spacePath: Foundation.URL, backupPath: Foundation.URL, frequency: AugmentCore.BackupFrequency, retention: AugmentCore.BackupRetention, isEncrypted: Swift.Bool = false, password: Swift.String? = nil, isEnabled: Swift.Bool = true, lastBackupTimestamp: Foundation.Date? = nil)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
  public typealias ID = Foundation.UUID
}
public enum BackupFrequency : Swift.String, Swift.Codable {
  case manual
  case hourly
  case daily
  case weekly
  public var interval: Foundation.TimeInterval {
    get
  }
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public enum BackupRetention : Swift.Codable {
  case keepAll
  case keepLimited(Swift.Int)
  case keepForTime(Foundation.TimeInterval)
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@_hasMissingDesignatedInitializers public class FileSystemMonitor {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.FileSystemMonitor
  final public let logger: AugmentCore.AugmentLogger
  final public let errorRecoveryManager: AugmentCore.ErrorRecoveryManager
  final public let memoryManager: AugmentCore.MemoryManager
  public struct SaveDetectionConfig {
  }
  public enum SaveDetectionMethod {
    case keyboardShortcut
    case fileMetadata
    case contentHash
    case applicationFocus
    public static func == (a: AugmentCore.FileSystemMonitor.SaveDetectionMethod, b: AugmentCore.FileSystemMonitor.SaveDetectionMethod) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public var saveDetectionConfig: AugmentCore.FileSystemMonitor.SaveDetectionConfig
  public var debounceInterval: Swift.Double
  public init(logger: AugmentCore.AugmentLogger, errorRecoveryManager: AugmentCore.ErrorRecoveryManager, memoryManager: AugmentCore.MemoryManager)
  @objc deinit
  public func suppressAutoVersioning(for filePath: Foundation.URL)
  public func unsuppressAutoVersioning(for filePath: Foundation.URL)
  public func isAutoVersioningSuppressed(for filePath: Foundation.URL) -> Swift.Bool
  public func suppressAutoVersioningForSpace(spacePath: Foundation.URL)
  public func unsuppressAutoVersioningForSpace(spacePath: Foundation.URL)
  public func startMonitoring(spacePath: Foundation.URL, callback: @escaping (Foundation.URL, AugmentCore.FileSystemEvent) -> Swift.Void) -> Swift.Bool
  @available(macOS 10.15, *)
  public func startMonitoringAsync(spacePath: Foundation.URL, callback: @escaping (Foundation.URL, AugmentCore.FileSystemEvent) async -> Swift.Void) -> Swift.Bool
  public func stopMonitoring()
  public func performMaintenance()
  public func configureSaveDetection(_ config: AugmentCore.FileSystemMonitor.SaveDetectionConfig)
  public func setDebounceInterval(_ interval: Foundation.TimeInterval)
}
public enum FileSystemEvent {
  case created
  case modified
  case deleted
  case renamed
  case unknown
  public static func == (a: AugmentCore.FileSystemEvent, b: AugmentCore.FileSystemEvent) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public enum FileSystemMonitorError : Swift.Error {
  case invalidPath(Swift.String)
  case memoryAccessError(Swift.String)
  case eventProcessingError(Swift.String)
}
extension AugmentCore.FileSystemMonitor {
  public func enableCommandSSaveDetection()
  public func enableComprehensiveSaveDetection()
  public func enableLightweightSaveDetection()
}
extension AugmentCore.FileSystemMonitor {
  public func addSpace(name: Swift.String, path: Swift.String, mode: Swift.String)
  public func removeSpace(name: Swift.String)
}
@_hasMissingDesignatedInitializers public class SecurityManager {
  @available(*, deprecated, message: "Use dependency injection instead of singleton pattern")
  public static let shared: AugmentCore.SecurityManager
  public init(logger: AugmentCore.AugmentLogger)
  public func createSecurityScopedBookmark(for url: Foundation.URL) -> Swift.Bool
  public func resolveSecurityScopedBookmark(for url: Foundation.URL) -> Swift.Bool
  public func stopAccessingSecurityScopedResource(_ url: Foundation.URL)
  public func removeSecurityScopedBookmark(for url: Foundation.URL)
  public func validateFileAccess(_ url: Foundation.URL, for operation: AugmentCore.FileOperation) -> Swift.Bool
  public func getAuditLog() -> [AugmentCore.SecurityAuditEntry]
  @objc deinit
}
public enum FileOperation : Swift.String, Swift.CaseIterable {
  case read
  case write
  case delete
  case version
  case backup
  case monitor
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.FileOperation]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.FileOperation] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
public enum SecurityEvent {
  case bookmarkCreated(path: Swift.String)
  case bookmarkCreationFailed(path: Swift.String, error: Swift.String)
  case bookmarkResolutionFailed(path: Swift.String, error: Swift.String)
  case bookmarkRemoved(path: Swift.String)
  case resourceAccessStarted(path: Swift.String)
  case resourceAccessStopped(path: Swift.String)
  case resourceAccessFailed(path: Swift.String)
  case fileAccessGranted(path: Swift.String, operation: Swift.String)
  case fileAccessDenied(path: Swift.String, operation: Swift.String, reason: Swift.String)
  case securityPolicyViolation(path: Swift.String, operation: Swift.String, reason: Swift.String)
  case securityHealthCheck(staleBookmarks: Swift.Int)
}
public struct SecurityAuditEntry : Swift.Codable {
  public let timestamp: Swift.String
  public let event: Swift.String
  public let userId: Swift.String
  public let processId: Swift.Int32
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
@_hasMissingDesignatedInitializers public class DataEncryption {
  public enum EncryptionError : Swift.Error, Foundation.LocalizedError {
    case keyGenerationFailed
    case keyRetrievalFailed
    case keyStorageFailed
    case encryptionFailed
    case decryptionFailed
    case invalidData
    case keychainError(Darwin.OSStatus)
    public var errorDescription: Swift.String? {
      get
    }
  }
  public static func encrypt(_ data: Foundation.Data) throws -> Foundation.Data
  public static func decrypt(_ encryptedData: Foundation.Data) throws -> Foundation.Data
  public static func encrypt(_ string: Swift.String) throws -> Foundation.Data
  public static func decryptToString(_ encryptedData: Foundation.Data) throws -> Swift.String
  public static func encrypt<T>(_ object: T) throws -> Foundation.Data where T : Swift.Decodable, T : Swift.Encodable
  public static func decrypt<T>(_ encryptedData: Foundation.Data, as type: T.Type) throws -> T where T : Swift.Decodable, T : Swift.Encodable
  public static func rotateMasterKey() throws
  public static func deleteMasterKey() throws
  public static func generateSalt(size: Swift.Int = 32) -> Foundation.Data
  public static func deriveKey(from password: Swift.String, salt: Foundation.Data, iterations: Swift.Int = 100_000) -> CryptoKit.SymmetricKey
  public static func secureCompare(_ lhs: Foundation.Data, _ rhs: Foundation.Data) -> Swift.Bool
  @objc deinit
}
public enum AugmentError : Swift.Error, Foundation.LocalizedError, Swift.CustomStringConvertible {
  case fileSystemMonitoringFailed(underlying: any Swift.Error)
  case fileAccessDenied(path: Swift.String)
  case fileCorrupted(path: Swift.String, reason: Swift.String? = nil)
  case fileNotFound(path: Swift.String)
  case fileTooLarge(path: Swift.String, size: Swift.Int64, limit: Swift.Int64)
  case directoryCreationFailed(path: Swift.String, underlying: any Swift.Error)
  case fileOperationFailed(operation: Swift.String, path: Swift.String, underlying: any Swift.Error)
  case versionCreationFailed(filePath: Swift.String, reason: Swift.String)
  case versionRestoreFailed(filePath: Swift.String, version: Swift.String, reason: Swift.String)
  case metadataCorrupted(path: Swift.String, details: Swift.String? = nil)
  case versionNotFound(filePath: Swift.String, version: Swift.String)
  case invalidVersionData(path: Swift.String, reason: Swift.String)
  case rollbackFailed(filePath: Swift.String, reason: Swift.String)
  case insufficientStorage(required: Swift.Int64, available: Swift.Int64)
  case storageQuotaExceeded(limit: Swift.Int64, current: Swift.Int64)
  case storageCleanupFailed(reason: Swift.String)
  case diskAccessError(path: Swift.String, underlying: any Swift.Error)
  case indexingFailed(spacePath: Swift.String, reason: Swift.String)
  case searchTimeout(query: Swift.String, timeoutSeconds: Foundation.TimeInterval)
  case indexCorrupted(spacePath: Swift.String)
  case searchEngineUnavailable(reason: Swift.String)
  case invalidConfiguration(key: Swift.String, value: Any, reason: Swift.String)
  case configurationMissing(key: Swift.String)
  case configurationValidationFailed(errors: [Swift.String])
  case configurationLoadFailed(underlying: any Swift.Error)
  case configurationSaveFailed(underlying: any Swift.Error)
  case networkUnavailable
  case syncFailed(reason: Swift.String, underlying: (any Swift.Error)? = nil)
  case authenticationFailed(service: Swift.String)
  case serverError(statusCode: Swift.Int, message: Swift.String)
  case conflictResolutionFailed(filePath: Swift.String, reason: Swift.String)
  case permissionDenied(resource: Swift.String, requiredPermission: Swift.String)
  case sandboxViolation(attemptedPath: Swift.String)
  case securityScopedBookmarkFailed(path: Swift.String)
  case memoryPressure(operation: Swift.String)
  case operationTimeout(operation: Swift.String, timeoutSeconds: Foundation.TimeInterval)
  case resourceExhausted(resource: Swift.String, limit: Swift.String)
  case performanceDegradation(operation: Swift.String, expectedTime: Foundation.TimeInterval, actualTime: Foundation.TimeInterval)
  case fsEventsFailure(reason: Swift.String)
  case notificationDeliveryFailed(notification: Swift.String)
  case backgroundTaskFailed(task: Swift.String, reason: Swift.String)
  case checksumMismatch(filePath: Swift.String, expected: Swift.String, actual: Swift.String)
  case dataCorruption(component: Swift.String, details: Swift.String)
  case backupVerificationFailed(backupPath: Swift.String, reason: Swift.String)
  case viewStateInconsistent(view: Swift.String, reason: Swift.String)
  case userInputValidationFailed(field: Swift.String, value: Swift.String, reason: Swift.String)
  case unknown(underlying: any Swift.Error, context: Swift.String? = nil)
  case internalError(component: Swift.String, reason: Swift.String)
  case preconditionFailed(condition: Swift.String, context: Swift.String)
  public var errorDescription: Swift.String? {
    get
  }
  public var failureReason: Swift.String? {
    get
  }
  public var recoverySuggestion: Swift.String? {
    get
  }
  public var description: Swift.String {
    get
  }
  public var category: AugmentCore.ErrorCategory {
    get
  }
  public var isRecoverable: Swift.Bool {
    get
  }
  public var severity: AugmentCore.ErrorSeverity {
    get
  }
}
public enum ErrorSeverity : Swift.String, Swift.CaseIterable {
  case low
  case medium
  case high
  case critical
  public var priority: Swift.Int {
    get
  }
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.ErrorSeverity]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.ErrorSeverity] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
extension AugmentCore.AugmentError {
  public static func from(_ error: any Swift.Error, context: Swift.String? = nil) -> AugmentCore.AugmentError
  public static func fileSystemError(operation: Swift.String, path: Swift.String, underlying: any Swift.Error) -> AugmentCore.AugmentError
}
public protocol ErrorHandling {
  var logger: AugmentCore.AugmentLogger { get }
  var errorRecoveryManager: AugmentCore.ErrorRecoveryManager { get }
  func handleError(_ error: any Swift.Error, context: Swift.String?, autoRecover: Swift.Bool)
  func reportError(_ error: any Swift.Error, context: Swift.String?)
  func logError(_ error: any Swift.Error, context: Swift.String?)
}
extension AugmentCore.ErrorHandling {
  public func handleError(_ error: any Swift.Error, context: Swift.String? = nil, autoRecover: Swift.Bool = true)
  public func reportError(_ error: any Swift.Error, context: Swift.String? = nil)
  public func logError(_ error: any Swift.Error, context: Swift.String? = nil)
}
extension Swift.Result where Failure == any Swift.Error {
  public func handleError(with handler: any AugmentCore.ErrorHandling, context: Swift.String? = nil, autoRecover: Swift.Bool = true)
  public func mapToAugmentError(context: Swift.String? = nil) -> Swift.Result<Success, AugmentCore.AugmentError>
}
@_hasMissingDesignatedInitializers final public class ErrorHandlingUtils {
  public static func execute<T>(operation: () throws -> T, context: Swift.String, handler: any AugmentCore.ErrorHandling, autoRecover: Swift.Bool = true) -> T?
  public static func executeAsync<T>(operation: () async throws -> T, context: Swift.String, handler: any AugmentCore.ErrorHandling, autoRecover: Swift.Bool = true) async -> T?
  public static func handleResult<T>(_ result: Swift.Result<T, any Swift.Error>, context: Swift.String, handler: any AugmentCore.ErrorHandling, autoRecover: Swift.Bool = true) -> T?
  public static func createContext(component: Swift.String, operation: Swift.String, additionalInfo: [Swift.String : Any] = [:]) -> Swift.String
  @objc deinit
}
public func withErrorHandling<T>(context: Swift.String, handler: any AugmentCore.ErrorHandling, autoRecover: Swift.Bool = true, operation: () throws -> T) -> T?
public func withAsyncErrorHandling<T>(context: Swift.String, handler: any AugmentCore.ErrorHandling, autoRecover: Swift.Bool = true, operation: () async throws -> T) async -> T?
@_hasMissingDesignatedInitializers final public class ErrorValidation {
  public static func require(_ condition: Swift.Bool, _ message: Swift.String, context: Swift.String, file: Swift.String = #file, line: Swift.Int = #line) throws
  public static func requireNotNil<T>(_ value: T?, _ message: Swift.String, context: Swift.String) throws -> T
  public static func validateFilePath(_ path: Foundation.URL, operation: Swift.String, requiresWrite: Swift.Bool = false) throws
  public static func validateStorageAvailability(path: Foundation.URL, requiredBytes: Swift.Int64) throws
  @objc deinit
}
@_hasMissingDesignatedInitializers final public class ErrorMetrics {
  public static let shared: AugmentCore.ErrorMetrics
  final public func recordError(_ error: AugmentCore.AugmentError, context: Swift.String? = nil)
  final public func getErrorStatistics() -> [Swift.String : Swift.Int]
  final public func getRecentErrors(limit: Swift.Int = 10) -> [(Foundation.Date, AugmentCore.AugmentError)]
  final public func clearMetrics()
  @objc deinit
}
public class SecurityAuditReporter {
  public init(securityManager: AugmentCore.SecurityManager, logger: AugmentCore.AugmentLogger)
  public func generateAuditReport(startDate: Foundation.Date? = nil, endDate: Foundation.Date? = nil) -> AugmentCore.SecurityAuditReport
  public func exportReport(_ report: AugmentCore.SecurityAuditReport, format: AugmentCore.ExportFormat, to outputURL: Foundation.URL) -> Swift.Bool
  @objc deinit
}
public enum ExportFormat : Swift.String, Swift.CaseIterable {
  case json
  case csv
  case html
  public init?(rawValue: Swift.String)
  public typealias AllCases = [AugmentCore.ExportFormat]
  public typealias RawValue = Swift.String
  nonisolated public static var allCases: [AugmentCore.ExportFormat] {
    get
  }
  public var rawValue: Swift.String {
    get
  }
}
public struct SecurityAuditReport : Swift.Codable {
  public let generatedAt: Foundation.Date
  public let startDate: Foundation.Date?
  public let endDate: Foundation.Date?
  public let totalEvents: Swift.Int
  public let eventsByType: [Swift.String : Swift.Int]
  public let securityViolations: [AugmentCore.SecurityViolation]
  public let fileAccessSummary: AugmentCore.FileAccessSummary
  public let riskAssessment: AugmentCore.RiskAssessment
  public let recommendations: [AugmentCore.SecurityRecommendation]
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct SecurityViolation : Swift.Codable {
  public let timestamp: Swift.String
  public let type: Swift.String
  public let description: Swift.String
  public let userId: Swift.String
  public let severity: AugmentCore.SecurityViolation.Severity
  public enum Severity : Swift.String, Swift.Codable {
    case low
    case medium
    case high
    public init?(rawValue: Swift.String)
    public typealias RawValue = Swift.String
    public var rawValue: Swift.String {
      get
    }
  }
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct FileAccessSummary : Swift.Codable {
  public let totalFileAccess: Swift.Int
  public let deniedFileAccess: Swift.Int
  public let uniqueFilesAccessed: Swift.Int
  public let accessSuccessRate: Swift.Double
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public struct RiskAssessment : Swift.Codable {
  public let overallRisk: AugmentCore.RiskLevel
  public let highRiskEvents: Swift.Int
  public let mediumRiskEvents: Swift.Int
  public let lowRiskEvents: Swift.Int
  public let riskFactors: [Swift.String]
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum RiskLevel : Swift.String, Swift.Codable {
  case minimal
  case low
  case medium
  case high
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public struct SecurityRecommendation : Swift.Codable {
  public let priority: AugmentCore.SecurityRecommendation.Priority
  public let category: Swift.String
  public let title: Swift.String
  public let description: Swift.String
  public let action: Swift.String
  public enum Priority : Swift.String, Swift.Codable {
    case low
    case medium
    case high
    public init?(rawValue: Swift.String)
    public typealias RawValue = Swift.String
    public var rawValue: Swift.String {
      get
    }
  }
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public class LargeFileHandler {
  public static let largeFileThreshold: Swift.Int64
  public static let maximumFileSize: Swift.Int64
  public typealias ProgressHandler = (Swift.Double, Swift.String) -> Swift.Void
  public typealias CompletionHandler = (Swift.Result<AugmentCore.LargeFileOperationResult, AugmentCore.AugmentError>) -> Swift.Void
  public init(logger: AugmentCore.AugmentLogger, performanceMonitor: AugmentCore.PerformanceMonitor)
  public func isLargeFile(_ url: Foundation.URL) -> Swift.Bool
  public func getFileSize(_ url: Foundation.URL) -> Swift.Int64?
  public func copyLargeFile(from sourceURL: Foundation.URL, to destinationURL: Foundation.URL, progressHandler: @escaping AugmentCore.LargeFileHandler.ProgressHandler, completionHandler: @escaping AugmentCore.LargeFileHandler.CompletionHandler)
  public func calculateFileHash(for url: Foundation.URL, algorithm: AugmentCore.HashAlgorithm = .sha256, progressHandler: @escaping AugmentCore.LargeFileHandler.ProgressHandler, completionHandler: @escaping (Swift.Result<Swift.String, AugmentCore.AugmentError>) -> Swift.Void)
  public func readLargeFileInChunks(from url: Foundation.URL, chunkHandler: @escaping (Foundation.Data, Swift.Int64, Swift.Int64) -> Swift.Bool, completionHandler: @escaping AugmentCore.LargeFileHandler.CompletionHandler)
  @objc deinit
}
public enum HashAlgorithm {
  case sha256
  public static func == (a: AugmentCore.HashAlgorithm, b: AugmentCore.HashAlgorithm) -> Swift.Bool
  public func hash(into hasher: inout Swift.Hasher)
  public var hashValue: Swift.Int {
    get
  }
}
public protocol FileHasher {
  mutating func update(data: Foundation.Data)
  func finalize() -> Swift.String
}
public struct SHA256FileHasher : AugmentCore.FileHasher {
  public mutating func update(data: Foundation.Data)
  public func finalize() -> Swift.String
}
public struct LargeFileOperationResult {
  public let operation: AugmentCore.LargeFileOperation
  public let fileSize: Swift.Int64
  public let duration: Foundation.TimeInterval
  public let throughput: Swift.Double
  public let success: Swift.Bool
  public var formattedSummary: Swift.String {
    get
  }
}
public enum LargeFileOperation : Swift.String {
  case copy
  case hash
  case read
  case write
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public class MemoryManager {
  public init(logger: AugmentCore.AugmentLogger, performanceMonitor: AugmentCore.PerformanceMonitor)
  @objc deinit
  public func startMonitoring()
  public func stopMonitoring()
  public func getCurrentMemoryUsage() -> AugmentCore.MemoryUsageSnapshot
  public func getMemoryUsageHistory() -> [AugmentCore.MemoryUsageSnapshot]
  public func trackObject(_ object: Swift.AnyObject)
  public func getTrackedObjectCount() -> Swift.Int
  public func registerCleanupHandler(identifier: Swift.String, handler: @escaping () -> Swift.Void)
  public func unregisterCleanupHandler(identifier: Swift.String)
  public func performCleanup()
  public func forceGarbageCollection()
  public func optimizeMemoryUsage()
}
public struct MemoryUsageSnapshot : Swift.Codable {
  public let timestamp: Foundation.Date
  public let physicalMemoryUsed: Swift.UInt64
  public let physicalMemoryTotal: Swift.UInt64
  public let virtualMemoryUsed: Swift.UInt64
  public let memoryPressure: AugmentCore.MemoryPressureLevel
  public let trackedObjectCount: Swift.Int
  public var memoryUsagePercentage: Swift.Double {
    get
  }
  public var formattedMemoryUsage: Swift.String {
    get
  }
  public func encode(to encoder: any Swift.Encoder) throws
  public init(from decoder: any Swift.Decoder) throws
}
public enum MemoryPressureLevel : Swift.String, Swift.Codable {
  case normal
  case warning
  case critical
  public init?(rawValue: Swift.String)
  public typealias RawValue = Swift.String
  public var rawValue: Swift.String {
    get
  }
}
public class MemoryOptimizationService {
  public enum OptimizationStrategy {
    case conservative
    case balanced
    case aggressive
    public static func == (a: AugmentCore.MemoryOptimizationService.OptimizationStrategy, b: AugmentCore.MemoryOptimizationService.OptimizationStrategy) -> Swift.Bool
    public func hash(into hasher: inout Swift.Hasher)
    public var hashValue: Swift.Int {
      get
    }
  }
  public struct OptimizationConfig {
    public let strategy: AugmentCore.MemoryOptimizationService.OptimizationStrategy
    public let enableAutomaticOptimization: Swift.Bool
    public let optimizationInterval: Foundation.TimeInterval
    public let memoryPressureThreshold: Swift.Double
    public init(strategy: AugmentCore.MemoryOptimizationService.OptimizationStrategy = .balanced, enableAutomaticOptimization: Swift.Bool = true, optimizationInterval: Foundation.TimeInterval = 600, memoryPressureThreshold: Swift.Double = 0.75)
  }
  public struct OptimizationStats {
    public let timestamp: Foundation.Date
    public let strategy: AugmentCore.MemoryOptimizationService.OptimizationStrategy
    public let memoryBeforeOptimization: Swift.UInt64
    public let memoryAfterOptimization: Swift.UInt64
    public let duration: Foundation.TimeInterval
    public let handlersExecuted: Swift.Int
    public var memoryRecovered: Swift.UInt64 {
      get
    }
    public var memoryRecoveredPercentage: Swift.Double {
      get
    }
  }
  public init(memoryManager: AugmentCore.MemoryManager, logger: AugmentCore.AugmentLogger, performanceMonitor: AugmentCore.PerformanceMonitor, config: AugmentCore.MemoryOptimizationService.OptimizationConfig = OptimizationConfig())
  @objc deinit
  public func updateConfiguration(_ newConfig: AugmentCore.MemoryOptimizationService.OptimizationConfig)
  public func getCurrentConfiguration() -> AugmentCore.MemoryOptimizationService.OptimizationConfig
  public func registerOptimizationHandler(identifier: Swift.String, handler: @escaping (AugmentCore.MemoryOptimizationService.OptimizationStrategy) -> Swift.Void)
  public func unregisterOptimizationHandler(identifier: Swift.String)
  @discardableResult
  public func performOptimization(strategy: AugmentCore.MemoryOptimizationService.OptimizationStrategy? = nil) -> AugmentCore.MemoryOptimizationService.OptimizationStats
  public func isOptimizationNeeded() -> Swift.Bool
  public func getOptimizationHistory() -> [AugmentCore.MemoryOptimizationService.OptimizationStats]
  public func startAutomaticOptimization()
  public func stopAutomaticOptimization()
}
public class BackgroundProcessingService {
  public enum TaskPriority : Swift.Int, Swift.CaseIterable {
    case low
    case normal
    case high
    case critical
    public init?(rawValue: Swift.Int)
    public typealias AllCases = [AugmentCore.BackgroundProcessingService.TaskPriority]
    public typealias RawValue = Swift.Int
    nonisolated public static var allCases: [AugmentCore.BackgroundProcessingService.TaskPriority] {
      get
    }
    public var rawValue: Swift.Int {
      get
    }
  }
  public enum TaskType : Swift.String, Swift.CaseIterable {
    case fileOperation
    case indexing
    case backup
    case cleanup
    case sync
    case analysis
    case compression
    case encryption
    public init?(rawValue: Swift.String)
    public typealias AllCases = [AugmentCore.BackgroundProcessingService.TaskType]
    public typealias RawValue = Swift.String
    nonisolated public static var allCases: [AugmentCore.BackgroundProcessingService.TaskType] {
      get
    }
    public var rawValue: Swift.String {
      get
    }
  }
  @_hasMissingDesignatedInitializers public class BackgroundTask {
    final public let id: Foundation.UUID
    final public let name: Swift.String
    final public let type: AugmentCore.BackgroundProcessingService.TaskType
    final public let priority: AugmentCore.BackgroundProcessingService.TaskPriority
    final public let createdAt: Foundation.Date
    public var startedAt: Foundation.Date? {
      get
    }
    public var completedAt: Foundation.Date? {
      get
    }
    public var status: AugmentCore.BackgroundProcessingService.BackgroundTask.TaskStatus {
      get
    }
    public var progress: Swift.Double {
      get
    }
    public var error: (any Swift.Error)? {
      get
    }
    public enum TaskStatus : Swift.String {
      case pending
      case running
      case completed
      case failed
      case cancelled
      public init?(rawValue: Swift.String)
      public typealias RawValue = Swift.String
      public var rawValue: Swift.String {
        get
      }
    }
    public var duration: Foundation.TimeInterval? {
      get
    }
    public var isCompleted: Swift.Bool {
      get
    }
    @objc deinit
  }
  public struct ProcessingConfig {
    public let maxConcurrentTasks: [AugmentCore.BackgroundProcessingService.TaskPriority : Swift.Int]
    public let taskTimeout: Foundation.TimeInterval
    public let enableTaskHistory: Swift.Bool
    public let maxHistorySize: Swift.Int
    public let enableProgressTracking: Swift.Bool
    public init(maxConcurrentTasks: [AugmentCore.BackgroundProcessingService.TaskPriority : Swift.Int] = [
                .critical: 2,
                .high: 3,
                .normal: 4,
                .low: 2,
            ], taskTimeout: Foundation.TimeInterval = 300, enableTaskHistory: Swift.Bool = true, maxHistorySize: Swift.Int = 1000, enableProgressTracking: Swift.Bool = true)
  }
  public struct ProcessingStats {
    public let totalTasksProcessed: Swift.Int
    public let tasksInQueue: Swift.Int
    public let runningTasks: Swift.Int
    public let averageProcessingTime: Foundation.TimeInterval
    public let successRate: Swift.Double
    public let tasksByType: [AugmentCore.BackgroundProcessingService.TaskType : Swift.Int]
    public let tasksByPriority: [AugmentCore.BackgroundProcessingService.TaskPriority : Swift.Int]
  }
  public init(logger: AugmentCore.AugmentLogger, performanceMonitor: AugmentCore.PerformanceMonitor, memoryManager: AugmentCore.MemoryManager? = nil, config: AugmentCore.BackgroundProcessingService.ProcessingConfig = ProcessingConfig())
  @objc deinit
  @discardableResult
  public func submitTask(name: Swift.String, type: AugmentCore.BackgroundProcessingService.TaskType, priority: AugmentCore.BackgroundProcessingService.TaskPriority? = nil, workItem: @escaping () throws -> Swift.Void, progressHandler: ((Swift.Double) -> Swift.Void)? = nil, completionHandler: ((Swift.Result<Swift.Void, any Swift.Error>) -> Swift.Void)? = nil) -> AugmentCore.BackgroundProcessingService.BackgroundTask
  @available(macOS 10.15, *)
  public func submitTask<T>(name: Swift.String, type: AugmentCore.BackgroundProcessingService.TaskType, priority: AugmentCore.BackgroundProcessingService.TaskPriority? = nil, workItem: @escaping () async throws -> T) async throws -> T
  @discardableResult
  public func cancelTask(taskId: Foundation.UUID) -> Swift.Bool
  @discardableResult
  public func cancelTasks(ofType type: AugmentCore.BackgroundProcessingService.TaskType) -> Swift.Int
  public func getProcessingStats() -> AugmentCore.BackgroundProcessingService.ProcessingStats
}
public struct CancellationError : Swift.Error, Foundation.LocalizedError {
  public var errorDescription: Swift.String? {
    get
  }
}
extension AugmentCore.FileItemRow : Swift.Sendable {}
extension AugmentCore.AugmentLogger.LogLevel : Swift.Equatable {}
extension AugmentCore.AugmentLogger.LogLevel : Swift.Hashable {}
extension AugmentCore.AugmentLogger.LogLevel : Swift.RawRepresentable {}
extension AugmentCore.AugmentLogger.LogCategory : Swift.Equatable {}
extension AugmentCore.AugmentLogger.LogCategory : Swift.Hashable {}
extension AugmentCore.AugmentLogger.LogCategory : Swift.RawRepresentable {}
extension AugmentCore.ErrorCategory : Swift.Equatable {}
extension AugmentCore.ErrorCategory : Swift.Hashable {}
extension AugmentCore.ErrorCategory : Swift.RawRepresentable {}
extension AugmentCore.RecoveryStrategy : Swift.Equatable {}
extension AugmentCore.RecoveryStrategy : Swift.Hashable {}
extension AugmentCore.RecoveryStrategy : Swift.RawRepresentable {}
extension AugmentCore.SyncDirection : Swift.Equatable {}
extension AugmentCore.SyncDirection : Swift.Hashable {}
extension AugmentCore.SyncDirection : Swift.RawRepresentable {}
extension AugmentCore.SyncFrequency : Swift.Equatable {}
extension AugmentCore.SyncFrequency : Swift.Hashable {}
extension AugmentCore.SyncFrequency : Swift.RawRepresentable {}
extension AugmentCore.VersioningMode : Swift.Equatable {}
extension AugmentCore.VersioningMode : Swift.Hashable {}
extension AugmentCore.VersioningMode : Swift.RawRepresentable {}
extension AugmentCore.FileType : Swift.Equatable {}
extension AugmentCore.FileType : Swift.Hashable {}
extension AugmentCore.FileType : Swift.RawRepresentable {}
extension AugmentCore.SnapshotFrequency : Swift.Equatable {}
extension AugmentCore.SnapshotFrequency : Swift.Hashable {}
extension AugmentCore.SnapshotFrequency : Swift.RawRepresentable {}
extension AugmentCore.DiffType : Swift.Equatable {}
extension AugmentCore.DiffType : Swift.Hashable {}
extension AugmentCore.DiffType : Swift.RawRepresentable {}
extension AugmentCore.ConflictType : Swift.Equatable {}
extension AugmentCore.ConflictType : Swift.Hashable {}
extension AugmentCore.ConflictType : Swift.RawRepresentable {}
extension AugmentCore.ConflictResolution : Swift.Equatable {}
extension AugmentCore.ConflictResolution : Swift.Hashable {}
extension AugmentCore.ConflictResolution : Swift.RawRepresentable {}
extension AugmentCore.ConflictResolutionType : Swift.Equatable {}
extension AugmentCore.ConflictResolutionType : Swift.Hashable {}
extension AugmentCore.PreviewFileType : Swift.Equatable {}
extension AugmentCore.PreviewFileType : Swift.Hashable {}
extension AugmentCore.DiffOperation.OperationType : Swift.Equatable {}
extension AugmentCore.DiffOperation.OperationType : Swift.Hashable {}
extension AugmentCore.DiffOperation.OperationType : Swift.RawRepresentable {}
extension AugmentCore.BackupFrequency : Swift.Equatable {}
extension AugmentCore.BackupFrequency : Swift.Hashable {}
extension AugmentCore.BackupFrequency : Swift.RawRepresentable {}
extension AugmentCore.FileSystemMonitor.SaveDetectionMethod : Swift.Equatable {}
extension AugmentCore.FileSystemMonitor.SaveDetectionMethod : Swift.Hashable {}
extension AugmentCore.FileSystemEvent : Swift.Equatable {}
extension AugmentCore.FileSystemEvent : Swift.Hashable {}
extension AugmentCore.FileOperation : Swift.Equatable {}
extension AugmentCore.FileOperation : Swift.Hashable {}
extension AugmentCore.FileOperation : Swift.RawRepresentable {}
extension AugmentCore.ErrorSeverity : Swift.Equatable {}
extension AugmentCore.ErrorSeverity : Swift.Hashable {}
extension AugmentCore.ErrorSeverity : Swift.RawRepresentable {}
extension AugmentCore.ExportFormat : Swift.Equatable {}
extension AugmentCore.ExportFormat : Swift.Hashable {}
extension AugmentCore.ExportFormat : Swift.RawRepresentable {}
extension AugmentCore.SecurityViolation.Severity : Swift.Equatable {}
extension AugmentCore.SecurityViolation.Severity : Swift.Hashable {}
extension AugmentCore.SecurityViolation.Severity : Swift.RawRepresentable {}
extension AugmentCore.RiskLevel : Swift.Equatable {}
extension AugmentCore.RiskLevel : Swift.Hashable {}
extension AugmentCore.RiskLevel : Swift.RawRepresentable {}
extension AugmentCore.SecurityRecommendation.Priority : Swift.Equatable {}
extension AugmentCore.SecurityRecommendation.Priority : Swift.Hashable {}
extension AugmentCore.SecurityRecommendation.Priority : Swift.RawRepresentable {}
extension AugmentCore.HashAlgorithm : Swift.Equatable {}
extension AugmentCore.HashAlgorithm : Swift.Hashable {}
extension AugmentCore.LargeFileOperation : Swift.Equatable {}
extension AugmentCore.LargeFileOperation : Swift.Hashable {}
extension AugmentCore.LargeFileOperation : Swift.RawRepresentable {}
extension AugmentCore.MemoryPressureLevel : Swift.Equatable {}
extension AugmentCore.MemoryPressureLevel : Swift.Hashable {}
extension AugmentCore.MemoryPressureLevel : Swift.RawRepresentable {}
extension AugmentCore.MemoryOptimizationService.OptimizationStrategy : Swift.Equatable {}
extension AugmentCore.MemoryOptimizationService.OptimizationStrategy : Swift.Hashable {}
extension AugmentCore.BackgroundProcessingService.TaskPriority : Swift.Equatable {}
extension AugmentCore.BackgroundProcessingService.TaskPriority : Swift.Hashable {}
extension AugmentCore.BackgroundProcessingService.TaskPriority : Swift.RawRepresentable {}
extension AugmentCore.BackgroundProcessingService.TaskType : Swift.Equatable {}
extension AugmentCore.BackgroundProcessingService.TaskType : Swift.Hashable {}
extension AugmentCore.BackgroundProcessingService.TaskType : Swift.RawRepresentable {}
extension AugmentCore.BackgroundProcessingService.BackgroundTask.TaskStatus : Swift.Equatable {}
extension AugmentCore.BackgroundProcessingService.BackgroundTask.TaskStatus : Swift.Hashable {}
extension AugmentCore.BackgroundProcessingService.BackgroundTask.TaskStatus : Swift.RawRepresentable {}
