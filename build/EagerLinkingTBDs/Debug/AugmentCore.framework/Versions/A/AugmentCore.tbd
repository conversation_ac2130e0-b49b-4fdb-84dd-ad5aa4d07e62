{"main_library": {"exported_symbols": [{"data": {"global": ["_$s11AugmentCore12SnapshotFileV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore13DiffOperationV0D4TypeO8rawValueSSvpMV", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabledSbvs", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvsTq", "_$s11AugmentCore19DependencyContainerC6inject25memoryOptimizationServiceyAA06MemorygH0C_tFTj", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4pathSSvg", "_$s11AugmentCore30DefaultVersionCreationDelegateCfd", "_$s11AugmentCore15PreviewFileTypeO2eeoiySbAC_ACtFZ", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8durationSdSgvgTq", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvsTj", "_$s11AugmentCore14DataEncryptionC0D5ErrorO07invalidC0yA2EmFWC", "_$s11AugmentCore18PreferencesManagerC12loadSettingsyyFTq", "_$s11AugmentCore22SecurityRecommendationVMa", "_$s11AugmentCore12SearchConfigV14maxQueryLengthSivpMV", "_$s11AugmentCore17FileAccessSummaryV05totalcD006deniedcD019uniqueFilesAccessed17accessSuccessRateACSi_S2iSdtcfC", "_$s11AugmentCore22SecurityRecommendationV8PriorityOMa", "_$s11AugmentCore19DependencyContainerC6inject15securityManageryAA08SecurityG0C_tFTj", "_$s11AugmentCore13BackupManagerC6sharedACvgZ", "_$s11AugmentCore19DependencyContainerC19notificationManagerAA012NotificationF0CyFTq", "_$s11AugmentCore0A5ErrorO25userInputValidationFailedyACSS_S2StcACmFWC", "_$s11AugmentCore17PerformanceConfigVSEAAMc", "_$s11AugmentCore16LargeFileHandlerC04readcD8InChunks4from05chunkE0010completionE0y10Foundation3URLV_SbAH4DataV_s5Int64VANtcys6ResultOyAA0cd9OperationP0VAA0A5ErrorOGctFTq", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore0A6LoggerC11LogCategoryO8allCasesSayAEGvpZMV", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvMTj", "_$s11AugmentCore17SnapshotFrequencyOSeAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC14updateProgressyySdFTj", "_$s11AugmentCore14StorageManagerC15startMonitoring8intervalySd_tFTq", "_$s11AugmentCore8FileItemV9isSyncingSbvM", "_$s11AugmentCore19DependencyContainerC17fileSystemMonitorAA04FilefG0CyFTq", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvpMV", "_$s11AugmentCore12SnapshotFileVs12IdentifiableAAMc", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvsTj", "_$s11AugmentCore15SnapshotManagerCfd", "_$s11AugmentCore13SpaceSettingsV18autoCleanupEnabledSbvpMV", "_$s11AugmentCore17FileAccessSummaryV06deniedcD0SivpMV", "_$s11AugmentCore22SecurityRecommendationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13ErrorCategoryO13configurationyA2CmFWC", "_$s11AugmentCore8FileTypeO5color7SwiftUI5ColorVvg", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvgTq", "_$s11AugmentCore13MemoryManagerCMn", "_$s11AugmentCore8FileTypeOSeAAMc", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOSHAAMc", "_$s11AugmentCore17CancellationErrorV16errorDescriptionSSSgvg", "_$s11AugmentCore8FileDiffV8diffTypeAA0dF0OvpMV", "_$s11AugmentCore19MemoryUsageSnapshotV14memoryPressureAA0cG5LevelOvpMV", "_$s11AugmentCore17FileAccessSummaryV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore13SpaceSettingsV14versioningModeAA010VersioningF0Ovg", "_$s11AugmentCore13SpaceSettingsV18autoCleanupEnabledSbvs", "_$s11AugmentCore15SecurityManagerC6sharedACvgZ", "_$s11AugmentCore22SecurityRecommendationV8PriorityON", "_$s11AugmentCore14StorageManagerC12getDiskUsage3forAA0C4InfoVAA0A5SpaceV_tFTj", "_$s11AugmentCore13SpaceSettingsV18maxVersionsPerFileSivpMV", "_$s11AugmentCore13ErrorCategoryO8rawValueSSvg", "_$s11AugmentCore13NetworkConfigV10retryDelaySdvpMV", "_$s11AugmentCore18ConfigurationErrorOs0D0AAMc", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvsTj", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvsTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO7pendingyA2GmFWC", "_$s11AugmentCore0A5ErrorO20invalidConfigurationyACSS_ypSStcACmFWC", "_$s11AugmentCore13FolderVersionV2id10folderPath9timestamp7comment07storageG0AC10Foundation4UUIDV_AI3URLVAI4DateVSSSgAMtcfC", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvpMV", "_$s11AugmentCore0A6LoggerC8LogLevelOMn", "_$s11AugmentCore18ConflictResolutionO8useLocalyA2CmFWC", "_$s11AugmentCore11FileVersionV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore16RecoverableErrorV7contextSSSgvg", "_$s11AugmentCore18ConflictResolutionOMn", "_$s11AugmentCore13FileOperationO8allCasesSayACGvgZ", "_$s11AugmentCore0A5SpaceV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore13StorageConfigV29notificationsEnabledByDefaultSbvpMV", "_$s11AugmentCore27ConflictResolutionUtilitiesCfd", "_$s11AugmentCore13BackupManagerC17getConfigurationsSayAA0C13ConfigurationVGyFTq", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA2_", "_$s11AugmentCore11FileVersionVSHAAMc", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvsTq", "_$s11AugmentCore14SecurityConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore30DefaultVersionCreationDelegateCAA0deF0AAWP", "_$s11AugmentCore15ErrorValidationCACycfC", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvMTq", "_$s11AugmentCore17SyncConfigurationV04lastC9Timestamp10Foundation4DateVSgvM", "_$s11AugmentCore15ConflictManagerC18getActiveConflicts9spacePathSayAA04FileC0VG10Foundation3URLV_tFTq", "_$s11AugmentCore19VersionControlErrorO11invalidPathyACSScACmFWC", "_$s11AugmentCore11FileVersionVN", "_$s11AugmentCore13SpaceSettingsV14autoVersioning18maxVersionsPerFile21monitorSubdirectories15excludePatterns0E9Snapshots22snapshotFrequencyHours18networkSyncEnabled17syncConfiguration017storageManagementU00G12StorageBytes0G14VersionAgeDays0X16WarningThreshold0e7CleanupU007cleanupqR00x13NotificationsU014versioningModeACSb_SiSbSaySSGSbSiSbAA06SimpletW0VSbs5Int64VSiSdSbSiSbAA0F4ModeOtcfC", "_$s11AugmentCore8SnapshotV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore15SnapshotManagerCMn", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivgTj", "_$s11AugmentCore20ErrorRecoveryManagerC13configuration011preferencesE06loggerAcA0A13ConfigurationC_AA011PreferencesE0CAA0A6LoggerCtcfc", "_$s11AugmentCore17SnapshotSchedulerCMn", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvMTj", "_$s11AugmentCore30DefaultVersionCreationDelegateC010createFileD08filePath7commentSb10Foundation3URLV_SSSgtFTq", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV7endTimeSdSgvg", "_$s11AugmentCore19BackupConfigurationV10CodingKeysON", "_$s11AugmentCore19BackupConfigurationVs12IdentifiableAAMc", "_$s11AugmentCore0A5SpaceV18updateLastAccessedyyF", "_$s11AugmentCore0A6LoggerC8LogLevelO8rawValueSivpMV", "_$s11AugmentCore0A13ConfigurationC23fileBatchProcessingSizeSivpMV", "_$s11AugmentCore11StorageInfoV13newestVersion10Foundation4DateVvpMV", "_$s11AugmentCore12ConflictTypeO06renameC0yA2CmFWC", "_$s11AugmentCore8SnapshotV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore11StorageInfoV13oldestVersion10Foundation4DateVvg", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA2_", "_$s11AugmentCore14DataEncryptionCACycfc", "_$s11AugmentCore8DiffTypeO5imageyA2CmFWC", "_$s11AugmentCore17SyncConfigurationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore19UserInterfaceConfigV14minWindowWidthSdvM", "_$s11AugmentCore12SearchConfigV31contentIndexingEnabledByDefaultSbvpMV", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvpMV", "_$s11AugmentCore16RecoveryStrategyO11isAutomaticSbvg", "_$s11AugmentCore8SnapshotV4nameSSvg", "_$s11AugmentCore11StorageInfoV9spacePath10Foundation3URLVvpMV", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvpMV", "_$s11AugmentCore13DiffOperationV0D4TypeON", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore0A6LoggerC11LogCategoryO11preferencesyA2EmFWC", "_$s11AugmentCore15FileSystemEventOMn", "_$s11AugmentCore13SpaceSettingsV18networkSyncEnabledSbvpMV", "_$s11AugmentCore12FileConflictV8filePath10Foundation3URLVvg", "_$s11AugmentCore12FileConflictV12conflictTypeAA0dF0Ovg", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvsTj", "_$s11AugmentCore19MemoryUsageSnapshotV9timestamp08physicalC4Used0gC5Total07virtualcH014memoryPressure18trackedObjectCountAC10Foundation4DateV_s6UInt64VA2nA0cL5LevelOSitcfC", "_$s11AugmentCore8FileDiffV11fromVersion02toF08diffType0H4DataAcA0cF0V_AiA0dI0O10Foundation0J0VtcfC", "_$s11AugmentCore19DependencyContainerC6inject15conflictManageryAA08ConflictG0C_tFTj", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvMTq", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvpMV", "_$s11AugmentCore0A5ErrorO7unknownyACs0C0_p_SSSgtcACmFWC", "_$s11AugmentCore0A6LoggerC8LogLevelOs12CaseIterableAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4typeAC0F4TypeOvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC06handleC0_7context11autoRecoverys0C0_p_SSSgSbtFTq", "_$s11AugmentCore12SearchConfigVSeAAMc", "_$s11AugmentCore13FileOperationO4readyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV07averageD4TimeSdvg", "_$s11AugmentCore0A6LoggerCN", "_$s11AugmentCore15MetadataManagerCMa", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvMTq", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivgTq", "_$s11AugmentCore14VersioningModeO8rawValueSSvpMV", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvgTj", "_$s11AugmentCore13StoragePolicyVN", "_$s11AugmentCore13ErrorCategoryON", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO4syncyA2EmFWC", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore15FileSystemEventON", "_$s11AugmentCore0A6LoggerC11LogCategoryOMa", "_$s11AugmentCore13CleanupResultV7summarySSvg", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvpMV", "_$s11AugmentCore18PerformanceMonitorC19getActiveOperationsSayAC16OperationMetricsVGyFTq", "_$s11AugmentCore8FileItemV4nameSSvg", "_$s11AugmentCore17FileSystemMonitorCMm", "_$s11AugmentCore14StorageManagerC03getC6Policy3forAA0cF0V10Foundation4UUIDV_tFTq", "_$s11AugmentCore27BackgroundProcessingServiceC11cancelTasks6ofTypeSiAC04TaskI0O_tFTj", "_$s11AugmentCore14RiskAssessmentVSEAAMc", "_$s11AugmentCore13StoragePolicyVMa", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusOSYAAMc", "_$s11AugmentCore0A13ConfigurationC15resetToDefaultsyyFTj", "_$s11AugmentCore8FileItemV12hasConflictsSbvs", "_$s11AugmentCore13StoragePolicyV4typeAC0D4TypeOvg", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfC", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV22enableContentHashCheckSbvg", "_$s11AugmentCore17FileSystemMonitorC30suppressAutoVersioningForSpace9spacePathy10Foundation3URLV_tFTq", "_$s11AugmentCore0A5ErrorO10Foundation09LocalizedC0AAMc", "_$s11AugmentCore8FileItemV12hasConflictsSbvpMV", "_$s11AugmentCore18ConflictResolutionOMa", "_$s11AugmentCore11StorageInfoV16spaceUtilizationSdvg", "_$s11AugmentCore26FileSystemMonitoringConfigVMn", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvpMV", "_$s11AugmentCore16SnapshotScheduleVMn", "_$s11AugmentCore18PerformanceMonitorC17getCurrentMetricsAC0cG0VSgyFTq", "_$s11AugmentCore17ErrorHistoryEntryV5errorAA011RecoverableC0VvpMV", "_$s11AugmentCore18PerformanceMonitorC12measureAsync_5blockxSS_xyYaKXEtYaKlFTjTu", "_$s11AugmentCore16LargeFileHandlerC03getD4Sizeys5Int64VSg10Foundation3URLVFTq", "_$s11AugmentCore17SnapshotFrequencyO7monthlyyA2CmFWC", "_$s11AugmentCore17FileSystemMonitorC14stopMonitoringyyFTj", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore16LargeFileHandlerC05largeD9Thresholds5Int64VvgZ", "_$s11AugmentCore0A6LoggerC8LogLevelO4infoyA2EmFWC", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvMTj", "_$s11AugmentCore13DiffOperationV4type7content10lineNumberA2C0D4TypeO_SSSiSgtcfC", "_$s11AugmentCore17FileSystemMonitorC11removeSpace4nameySS_tF", "_$s11AugmentCore14StorageManagerCMa", "_$s11AugmentCore17SyncConfigurationV10remotePath10Foundation3URLVvg", "_$s11AugmentCore13StorageConfigVMn", "_$s11AugmentCore12ExportFormatO8rawValueACSgSS_tcfC", "_$s11AugmentCore8SnapshotV2id9spacePath4name11description9timestamp5filesAC10Foundation4UUIDV_AJ3URLVS2SSgAJ4DateVSayAA0C4FileVGtcfC", "_$s11AugmentCore0A5ErrorOMa", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvMTq", "_$s11AugmentCore15MetadataManagerCfD", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore13SyncDirectionO8rawValueSSvg", "_$s11AugmentCore22SecurityRecommendationV8PriorityOSEAAMc", "_$s11AugmentCore17PerformanceConfigV03maxC11HistorySizeSivs", "_$s11AugmentCore0A13ConfigurationCfd", "_$s11AugmentCore17SnapshotSchedulerC11getSchedule9spacePathAA0cF0VSg10Foundation3URLV_tFTj", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsVN", "_$s11AugmentCore12SearchConfigV14minQueryLengthSivs", "_$s11AugmentCore13MemoryManagerC11trackObjectyyyXlFTj", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsVMa", "_$s11AugmentCore15ErrorValidationC7require__7context4file4lineySb_S3SSitKFZ", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV12runningTasksSivg", "_$s11AugmentCore19NotificationManagerC6loggerAcA0A6LoggerC_tcfC", "_$s11AugmentCore0A13ConfigurationC23configureForDevelopmentyyF", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvgTj", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV7isValidSbvpMV", "_$s11AugmentCore17FileSystemMonitorC26isAutoVersioningSuppressed3forSb10Foundation3URLV_tFTj", "_$s11AugmentCore15BackupFrequencyO6hourlyyA2CmFWC", "_$s11AugmentCore17SyncConfigurationVSQAAMc", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV07minimumC10SizeChanges5Int64Vvs", "_$s11AugmentCore0A5SpaceVSEAAMc", "_$s11AugmentCore12ExportFormatO8allCasesSayACGvpZMV", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvgTj", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvMTj", "_$s11AugmentCore15MetadataManagerCN", "_$s11AugmentCore16RecoverableErrorV18recoveryStrategiesSayAA16RecoveryStrategyOGvpMV", "_$s11AugmentCore15SecurityManagerC07resolveC14ScopedBookmark3forSb10Foundation3URLV_tFTq", "_$s11AugmentCore21SecurityAuditReporterC08generateD6Report9startDate03endI0AA0cdG0V10Foundation0I0VSg_ALtFTj", "_$s11AugmentCore17SecurityViolationVSeAAMc", "_$s11AugmentCore18LargeFileOperationOSHAAMc", "_$s11AugmentCore16LargeFileHandlerC03getD4Sizeys5Int64VSg10Foundation3URLVFTj", "_$s11AugmentCore12FileConflictV12conflictTypeAA0dF0OvpMV", "_$s11AugmentCore17SnapshotFrequencyOMa", "_$s11AugmentCore15ConflictManagerC03addC0yyAA04FileC0VFTj", "_$s11AugmentCore17PerformanceConfigV13fileBatchSizeSivs", "_$s11AugmentCore24LargeFileOperationResultV8durationSdvg", "_$s11AugmentCore0A5ErrorO18recoverySuggestionSSSgvg", "_$s11AugmentCore14DataEncryptionCMa", "_$s11AugmentCore12ExportFormatOSHAAMc", "_$s11AugmentCore19MemoryUsageSnapshotV9timestamp10Foundation4DateVvg", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvsTq", "_$s11AugmentCore12SearchConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore19BackupConfigurationV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore19DependencyContainerC6inject13memoryManageryAA06MemoryG0C_tFTj", "_$s11AugmentCore12SearchEngineC9indexFile8filePath7versionSb10Foundation3URLV_AA0F7VersionVtFTj", "_$s11AugmentCore22ConflictResolutionTypeO4hash4intoys6HasherVz_tF", "_$s11AugmentCore15PreviewFileTypeOSHAAMc", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvsTj", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvsTj", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvpMV", "_$s11AugmentCore18PerformanceMonitorC7measure_5blockxSS_xyKXEtKlFTj", "_$s11AugmentCore19UserInterfaceConfigV26animationsEnabledByDefaultSbvM", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvMTj", "_$s11AugmentCore8FileItemV4hash4intoys6HasherVz_tF", "_$s11AugmentCore14StorageManagerCfd", "_$s11AugmentCore0A13ConfigurationC16maxSearchResultsSivpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC5errors5Error_pSgvgTj", "_$s11AugmentCore13SpaceSettingsV17syncConfigurationAA010SimpleSyncF0VvM", "_$s11AugmentCore13PreviewEngineCfd", "_$s11AugmentCore19BackupConfigurationV8passwordSSSgvpMV", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOs0E3KeyAAMc", "_$s11AugmentCore19BackupConfigurationV9spacePath10Foundation3URLVvpMV", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore17ErrorHistoryEntryVMa", "_$s11AugmentCore13ErrorCategoryO7networkyA2CmFWC", "_$s11AugmentCore0A5ErrorO23configurationSaveFailedyACs0C0_p_tcACmFWC", "_$s11AugmentCore8SnapshotV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore0A5SpaceV16lastAccessedDate10Foundation0F0VvpMV", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore14DataEncryptionCMu", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigVMa", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV14maxHistorySizeSivpMV", "_$s11AugmentCore8FileDiffV9toVersionAA0cF0Vvg", "_$s11AugmentCore13LoggingConfigVN", "_$s11AugmentCore13SpaceSettingsV15excludePatternsSaySSGvg", "_$s11AugmentCore13NetworkConfigVMn", "_$s11AugmentCore15MetadataManagerC6logger17encryptionEnabledAcA0A6LoggerC_SbtcfCTj", "_$s11AugmentCore14DataEncryptionCACycfCTq", "_$s11AugmentCore15SnapshotManagerC14removeSchedule9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodOMa", "_$s11AugmentCore0A5SpaceV8contains8filePathSb10Foundation3URLV_tF", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore9RiskLevelOSHAAMc", "_$s11AugmentCore23SimpleSyncConfigurationV9serverURLSSvg", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO3qos8Dispatch0I3QoSV0J6SClassOvpMV", "_$s11AugmentCore8SnapshotV9spacePath10Foundation3URLVvg", "_$s11AugmentCore27ConflictResolutionUtilitiesCACycfC", "_$s11AugmentCore8FileDiffVN", "_$s11AugmentCore19UserInterfaceConfigV15minWindowHeightSdvM", "_$s11AugmentCore19BackupConfigurationV9isEnabledSbvs", "_$s11AugmentCore17SecurityViolationVMn", "_$s11AugmentCore19BackupConfigurationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore15MetadataManagerC013deleteVersionC07version9spacePathSbAA06FolderF0V_10Foundation3URLVtFTq", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvMTq", "_$s11AugmentCore13StoragePolicyVSEAAMc", "_$s11AugmentCore20ErrorRecoveryManagerCMm", "_$s11AugmentCore14StorageManagerC011alertUserOfC6Issues3for5issueyAA0A5SpaceV_AA0C5IssueOtFTq", "_$s11AugmentCore16RecoveryStrategyO8rawValueSSvg", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvMTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusOMn", "_$s11AugmentCore17SnapshotFrequencyO8intervalSdvpMV", "_$s11AugmentCore11NetworkSyncC10fileSystem15conflictManagerAcA09SpaceFileF9Providing_p_AA08ConflictH0Ctcfc", "_$s11AugmentCore13DiffOperationVN", "_$s11AugmentCore12SnapshotFileV10CodingKeysOMa", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOMa", "_$s11AugmentCore13LoggingConfigV15defaultLogLevelSSvpMV", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvMTj", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvpMV", "_$s11AugmentCore19DependencyContainerC6inject15metadataManageryAA08MetadataG0C_tFTj", "_$s11AugmentCore13BackupManagerCMm", "_$s11AugmentCore13MemoryManagerCMo", "_$s11AugmentCore12FileConflictV9timestamp10Foundation4DateVvg", "_$s11AugmentCore15SecurityManagerC06createC14ScopedBookmark3forSb10Foundation3URLV_tFTq", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvgTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV18commandSTimeWindowSdvpMV", "_$s11AugmentCore22FileSystemMonitorErrorOMn", "_$s11AugmentCore16RecoveryStrategyOSYAAMc", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvgTj", "_$s11AugmentCore13StorageConfigV27autoCleanupEnabledByDefaultSbvg", "_$s11AugmentCore0A5SpaceV4nameSSvg", "_$s11AugmentCore12FileConflictVs12IdentifiableAAMc", "_$s11AugmentCore18SecurityAuditEntryVMa", "_$s11AugmentCore17SyncConfigurationV9spacePath10Foundation3URLVvg", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8rawValueAESgSS_tcfC", "_$s11AugmentCore27ConflictResolutionUtilitiesCN", "_$s11AugmentCore8FileDiffVMn", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA5_", "_$s11AugmentCore19MemoryPressureLevelOSeAAMc", "_$s11AugmentCore15FileSystemEventO9hashValueSivg", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV18commandSTimeWindowSdvM", "_$s11AugmentCore18LargeFileOperationOSYAAMc", "_$s11AugmentCore18ConflictResolutionOSYAAMc", "_$s11AugmentCore17ErrorHistoryEntryVN", "_$s11AugmentCore17FileSystemMonitorCMn", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfcfA1_", "_$s11AugmentCore12SearchConfigV14maxQueryLengthSivg", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore12SearchEngineCMn", "_$s11AugmentCore12FileConflictV22remoteModificationDate10Foundation0G0VvpMV", "_$s11AugmentCore0A6LoggerCfd", "_$s11AugmentCore0A13ConfigurationC17defaultWindowSizeSd5width_Sd6heighttvg", "_$s11AugmentCore13FolderVersionV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA4_", "_$s11AugmentCore13ErrorSeverityON", "_$s11AugmentCore13NetworkConfigV20syncEnabledByDefaultSbvpMV", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOSQAAMc", "_$s11AugmentCore13NetworkConfigV14networkTimeoutSdvs", "_$s11AugmentCore19DependencyContainerC19notificationManagerAA012NotificationF0CyFTj", "_$s11AugmentCore18PerformanceMonitorCfd", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvgTq", "_$s11AugmentCore13SpaceSettingsV18maxVersionsPerFileSivg", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfcfA2_", "_$s11AugmentCore13DiffOperationV10lineNumberSiSgvpMV", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore17FileSystemMonitorC24unsuppressAutoVersioning3fory10Foundation3URLV_tFTj", "_$s11AugmentCore8FileTypeO5otheryA2CmFWC", "_$s11AugmentCore12SearchEngineC11searchAsync5query10spacePathsSayAA0C6ResultVGSS_Say10Foundation3URLVGtYaFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4name4type8priority8workItem15progressHandler010completionM0AESS_AC0F4TypeOAC0F8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtcfCTj", "_$s11AugmentCore13LoggingConfigV07consoleC7EnabledSbvpMV", "_$s11AugmentCore14StorageManagerC011alertUserOfC6Issues3for5issueyAA0A5SpaceV_AA0C5IssueOtFTj", "_$s11AugmentCore12SearchResultVN", "_$s11AugmentCore17FileSystemMonitorCMu", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvgTq", "_$s11AugmentCore27BackgroundProcessingServiceC6logger18performanceMonitor13memoryManager6configAcA0A6LoggerC_AA011PerformanceH0CAA06MemoryJ0CSgAC0D6ConfigVtcfCTj", "_$s11AugmentCore18LargeFileOperationO4copyyA2CmFWC", "_$s11AugmentCore13BackupManagerCfD", "_$s11AugmentCore14SecurityConfigVSEAAMc", "_$s11AugmentCore0A5ErrorO17resourceExhaustedyACSS_SStcACmFWC", "_$s11AugmentCore15MetadataManagerCMm", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOSYAAMc", "_$s11AugmentCore17SnapshotRetentionOSEAAMc", "_$s11AugmentCore13StorageConfigVSeAAMc", "_$s11AugmentCore13SecurityEventO17fileAccessGrantedyACSS_SStcACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOMa", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvMTq", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA_", "_$s11AugmentCore15BackupRetentionO11keepLimitedyACSicACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC6logger18performanceMonitor13memoryManager6configAcA0A6LoggerC_AA011PerformanceH0CAA06MemoryJ0CSgAC0D6ConfigVtcfC", "_$s11AugmentCore17FileSystemMonitorC8addSpace4name4path4modeySS_S2StF", "_$s11AugmentCore14VersionControlC06createC010folderPath7commentAA06FolderC0VSg10Foundation3URLV_SSSgtFTq", "_$s11AugmentCore13SpaceSettingsV21monitorSubdirectoriesSbvg", "_$s11AugmentCore16RecoverableErrorV08originalD0s0D0_pvpMV", "_$s11AugmentCore19DependencyContainerC15securityManagerAA08SecurityF0CyFTq", "_$s11AugmentCore14VersionControlCMa", "_$s11AugmentCore19UserInterfaceConfigVSEAAMc", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV7endTimeSdSgvpMV", "_$s11AugmentCore13NetworkConfigV14networkTimeoutSdvpMV", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV11memoryUsages6UInt64VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8priorityAC0F8PriorityOvg", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvgTq", "_$s11AugmentCore0A6LoggerC8LogLevelO11descriptionSSvg", "_$s11AugmentCore16RecoverableErrorV18recoveryStrategiesSayAA16RecoveryStrategyOGvg", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutesSivpMV", "_$s11AugmentCore16RecoveryStrategyON", "_$s11AugmentCore12SearchResultV7contextSSvpMV", "_$s11AugmentCore13LoggingConfigV15defaultLogLevelSSvg", "_$s11AugmentCore14RiskAssessmentV06mediumC6EventsSivpMV", "_$s11AugmentCore8FileItemV4pathSSvM", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsVN", "_$s11AugmentCore13SpaceSettingsV24storageManagementEnabledSbvM", "_$s11AugmentCore0A5ErrorO8severityAA0C8SeverityOvpMV", "_$s11AugmentCore14VersioningModeO4autoyA2CmFWC", "_$s11AugmentCore0A13ConfigurationCMu", "_$s11AugmentCore15ConflictManagerCfD", "_$s11AugmentCore17FileSystemMonitorC6logger20errorRecoveryManager06memoryI0AcA0A6LoggerC_AA05ErrorhI0CAA06MemoryI0CtcfC", "_$s11AugmentCore14DataEncryptionC15deleteMasterKeyyyKFZ", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO4hash4intoys6HasherVz_tF", "_$s11AugmentCore19NotificationManagerC22getAuthorizationStatus10completionyySo015UNAuthorizationG0Vc_tFTj", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvsTq", "_$s11AugmentCore20ErrorRecoveryManagerCN", "_$s11AugmentCore18PreferencesManagerC13configuration07storageD0012notificationD06loggerAcA0A13ConfigurationC_AA07StorageD0CAA012NotificationD0CAA0A6LoggerCtcfC", "_$s11AugmentCore17FileSystemMonitorC6loggerAA0A6LoggerCvpMV", "_$s11AugmentCore19NotificationManagerC6loggerAcA0A6LoggerC_tcfCTq", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigVN", "_$s11AugmentCore13ErrorCategoryOMn", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV011memoryAfterD0s6UInt64Vvg", "_$s11AugmentCore17SecurityViolationV8SeverityOSQAAMc", "_$s11AugmentCore12SearchConfigV12indexTimeoutSdvg", "_$s11AugmentCore13NetworkConfigV14networkTimeoutSdvg", "_$s11AugmentCore25MemoryOptimizationServiceC07performD08strategyAC0D5StatsVAC0D8StrategyOSg_tFTj", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8allCasesSayAEGvgZ", "_$s11AugmentCore14DataEncryptionC7decrypt_2asx10Foundation0C0V_xmtKSeRzSERzlFZ", "_$s11AugmentCore14SecurityConfigV27fileAccessValidationEnabledSbvpMV", "_$s11AugmentCore14VersioningModeON", "_$s11AugmentCore19DependencyContainerC6inject13configurationyAA0A13ConfigurationC_tFTj", "_$s11AugmentCore0A6LoggerC7warning_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTj", "_$s11AugmentCore8FileItemV4pathSSvs", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV8durationSdvg", "_$s11AugmentCore13ErrorCategoryO10fileSystemyA2CmFWC", "_$s11AugmentCore13DiffOperationV7contentSSvpMV", "_$s11AugmentCore0A6LoggerC8LogLevelO5debugyA2EmFWC", "_$s11AugmentCore13MemoryManagerC22registerCleanupHandler10identifier7handlerySS_yyctFTq", "_$s11AugmentCore12SearchEngineCACycfc", "_$s11AugmentCore21SecurityAuditReporterCN", "_$s11AugmentCore19DependencyContainerC6inject14versionControlyAA07VersionG0C_tFTq", "_$s11AugmentCore15PreviewFileTypeOSQAAMc", "_$s11AugmentCore17SnapshotFrequencyON", "_$s11AugmentCore12SearchResultV7contextSSvg", "_$s11AugmentCore13HashAlgorithmOSHAAMc", "_$s11AugmentCore13SyncFrequencyO8rawValueSSvg", "_$s11AugmentCore17PerformanceConfigV03maxC11HistorySizeSivpMV", "_$s11AugmentCore16RecoveryStrategyO19checkSystemSettingsyA2CmFWC", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysON", "_$s11AugmentCore12ErrorMetricsC05clearD0yyF", "_$s11AugmentCore12SearchResultV7versionAA11FileVersionVvpMV", "_$s11AugmentCore12SearchConfigV14minQueryLengthSivpMV", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvgTq", "_$s11AugmentCore15ErrorValidationCACycfCTj", "_$s11AugmentCore14VersionControlC07restoreC08filePath7version7commentSb10Foundation3URLV_AA04FileC0VSSSgtFTq", "_$s11AugmentCore24LargeFileOperationResultV8fileSizes5Int64Vvg", "_$s11AugmentCore12ErrorMetricsCMo", "_$s11AugmentCore17SnapshotSchedulerC14removeSchedule9spacePathy10Foundation3URLV_tF", "_$s11AugmentCore19BackupConfigurationV8passwordSSSgvg", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO6normalyA2EmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOSQAAMc", "_$s11AugmentCore15ConflictManagerCACycfc", "_$s11AugmentCore15BackupFrequencyO6weeklyyA2CmFWC", "_$s11AugmentCore30DefaultVersionCreationDelegateCMo", "_$s11AugmentCore0A5ErrorO4from_7contextACs0C0_p_SSSgtFZ", "_$s11AugmentCore17FileSystemMonitorC30suppressAutoVersioningForSpace9spacePathy10Foundation3URLV_tFTj", "_$s11AugmentCore13FileOperationO8rawValueSSvpMV", "_$s11AugmentCore13LoggingConfigV011performanceC7EnabledSbvg", "_$s11AugmentCore26FileSystemMonitoringConfigV22defaultExcludePatternsSaySSGvpMV", "_$s11AugmentCore17FileSystemMonitorC22suppressAutoVersioning3fory10Foundation3URLV_tFTq", "_$s11AugmentCore19NotificationManagerC24sendErrorRecoverySuccess7errorIdySS_tFTq", "_$s11AugmentCore19BackupConfigurationV9frequencyAA0C9FrequencyOvpMV", "_$s11AugmentCore19MemoryUsageSnapshotV09formattedcD0SSvg", "_$s11AugmentCore18PerformanceMonitorC6loggerAcA0A6LoggerC_tcfC", "_$s11AugmentCore12SearchEngineC10clearIndex9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore19DependencyContainerC15metadataManagerAA08MetadataF0CyFTq", "_$s11AugmentCore22ConflictResolutionTypeO6customyA2CmFWC", "_$s11AugmentCore12SearchConfigV31contentIndexingEnabledByDefaultSbvs", "_$s11AugmentCore17FileAccessSummaryVN", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO11compressionyA2EmFWC", "_$s11AugmentCore8FileTypeO4from3urlAC10Foundation3URLV_tFZ", "_$s11AugmentCore13SyncFrequencyOMa", "_$s11AugmentCore15FileSystemEventO7deletedyA2CmFWC", "_$s11AugmentCore23PolicyEnforcementResultOMn", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvgTq", "_$s11AugmentCore12SnapshotFileVMn", "_$s11AugmentCore0A5ErrorO13searchTimeoutyACSS_SdtcACmFWC", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOMa", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8allCasesSayAEGvpZMV", "_$s11AugmentCore16RecoveryStrategyOSEAAMc", "_$s11AugmentCore0A6LoggerC11LogCategoryO8rawValueAESgSS_tcfC", "_$s11AugmentCore8FileItemV4sizes5Int64Vvg", "_$s11AugmentCore22ConflictResolutionTypeOSHAAMc", "_$s11AugmentCore12SearchResultV5tokenSSvg", "_$s11AugmentCore15PreviewFileTypeO4textyA2CmFWC", "_$s11AugmentCore8SnapshotV2id10Foundation4UUIDVvg", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA7_", "_$s11AugmentCore17withErrorHandling7context7handler11autoRecover9operationxSgSS_AA0dE0_pSbxyKXEtlF", "_$s11AugmentCore21SecurityAuditReporterCMo", "_$s11AugmentCore17SyncConfigurationV10CodingKeysON", "_$s11AugmentCore13ErrorSeverityO4highyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC03getD5StatsAC0dG0VyFTj", "_$s11AugmentCore26FileSystemMonitoringConfigV22defaultExcludePatternsSaySSGvs", "_$s11AugmentCore13BackupManagerC19removeConfiguration9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore13FileOperationO6deleteyA2CmFWC", "_$s11AugmentCore12SnapshotFileV12relativePathSSvg", "_$s11AugmentCore0A5SpaceV8settingsAA0C8SettingsVvs", "_$s11AugmentCore15ConflictManagerC07resolveC0_10resolutionSbAA04FileC0V_AA0C10ResolutionOtFTj", "_$s11AugmentCore0A6LoggerC5error_7message8category4file8function4lineys5Error_p_SSSgAC11LogCategoryOS2SSitFTj", "_$s11AugmentCore11FileItemRowVMn", "_$s11AugmentCore19BackupConfigurationV9isEnabledSbvM", "_$s11AugmentCore18PerformanceMonitorCMa", "_$s11AugmentCore14DataEncryptionC0D5ErrorO10Foundation09LocalizedE0AAMc", "_$s11AugmentCore17SnapshotSchedulerC5timer33_7E7566F25733DFECB0CF9BB3A350FCB8LLSo7NSTimerCSgvM", "_$s11AugmentCore15ConflictManagerCMa", "_$s11AugmentCore0A6LoggerC13getLogFileURL10Foundation0G0VSgyFTq", "_$s11AugmentCore16SnapshotScheduleVMa", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvgTq", "_$s11AugmentCore0A13ConfigurationCMo", "_$s11AugmentCore13DiffOperationV10lineNumberSiSgvg", "_$s11AugmentCore13HashAlgorithmON", "_$s11AugmentCore19MemoryPressureLevelO8rawValueACSgSS_tcfC", "_$s11AugmentCore12FileConflictV2id10Foundation4UUIDVvg", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore8FileItemVN", "_$s11AugmentCore13ErrorCategoryOSYAAMc", "_$s11AugmentCore13StoragePolicyV0D4TypeO6maxAgeyAESicAEmFWC", "_$s11AugmentCore22SecurityRecommendationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore15MetadataManagerC017deleteFileVersionC07version9spacePathSbAA0fG0V_10Foundation3URLVtFTq", "_$s11AugmentCore15SecurityManagerCMm", "_$s11AugmentCore18ErrorHandlingUtilsCACycfCTq", "_$s11AugmentCore15ConflictManagerCMu", "_$s11AugmentCore0A13ConfigurationC08validateC0SayAA0C5ErrorOGyFTj", "_$s11AugmentCore17SyncConfigurationV9hashValueSivg", "_$s11AugmentCore19MemoryUsageSnapshotV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvpMV", "_$s11AugmentCore0A5SpaceV12relativePath3forSSSg10Foundation3URLV_tF", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfC", "_$s11AugmentCore26FileSystemMonitoringConfigV26throttlingCleanupThresholdSivpMV", "_$s11AugmentCore17SyncConfigurationV9frequencyAA0C9FrequencyOvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvMTj", "_$s11AugmentCore13LoggingConfigV16logRetentionDaysSivs", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvgTq", "_$s11AugmentCore17SecurityViolationV6userIdSSvpMV", "_$s11AugmentCore18PerformanceMonitorC03getC7History5limitSayAC0C7MetricsVGSiSg_tFTj", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore21SecurityAuditReporterC15securityManager6loggerAcA0cG0C_AA0A6LoggerCtcfCTq", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8analysisyA2EmFWC", "_$s11AugmentCore12FileConflictV8filePath10Foundation3URLVvpMV", "_$s11AugmentCore16RecoverableErrorVMn", "_$s11AugmentCore12SearchResultVSQAAMc", "_$s11AugmentCore13SpaceSettingsV17syncConfigurationAA010SimpleSyncF0Vvs", "_$s11AugmentCore19MemoryUsageSnapshotV18trackedObjectCountSivg", "_$s11AugmentCore13StorageConfigV23defaultWarningThresholdSdvM", "_$s11AugmentCore19MemoryUsageSnapshotV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore26FileSystemMonitoringConfigV25throttlingEntryExpirationSdvpMV", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvsTq", "_$s11AugmentCore8FileItemV15updatedFromDiskACSgyF", "_$s11AugmentCore13SpaceSettingsV18autoCleanupEnabledSbvg", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvpMV", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore17SnapshotRetentionO7keepAllyA2CmFWC", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvMTq", "_$s11AugmentCore17FileSystemMonitorC20errorRecoveryManagerAA05ErrorgH0Cvg", "_$s11AugmentCore11NetworkSyncC9syncSpace9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore0A13ConfigurationCACycfCTj", "_$s11AugmentCore0A6LoggerC8LogLevelO11descriptionSSvpMV", "_$s11AugmentCore12SearchConfigVMn", "_$s11AugmentCore17SnapshotFrequencyOSHAAMc", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvMTq", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore15SecurityManagerC07resolveC14ScopedBookmark3forSb10Foundation3URLV_tFTj", "_$s11AugmentCore19VersionControlErrorO14rollbackFailedyACSScACmFWC", "_$s11AugmentCore22FileSystemMonitorErrorO20localizedDescriptionSSvg", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigVMn", "_$s11AugmentCore8FileTypeOSEAAMc", "_$s11AugmentCore13CleanupResultV6errorsSays5Error_pGvg", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvgTj", "_$s11AugmentCore21SecurityAuditReporterC08generateD6Report9startDate03endI0AA0cdG0V10Foundation0I0VSg_ALtFTq", "_$s11AugmentCore13LoggingConfigVACycfC", "_$s11AugmentCore0A5SpaceV11createdDate10Foundation0E0Vvg", "_$s11AugmentCore20DependencyInjectableTL", "_$s11AugmentCore14SecurityConfigV20securityScanIntervalSivpMV", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA0_", "_$s11AugmentCore18ErrorHandlingUtilsCMo", "_$s11AugmentCore11StorageInfoVMa", "_$s11AugmentCore13NetworkConfigVSEAAMc", "_$s11AugmentCore14DataEncryptionCMn", "_$s11AugmentCore19UserInterfaceConfigV17animationDurationSdvg", "_$s11AugmentCore13LoggingConfigV14maxLogFileSizes5Int64VvM", "_$s11AugmentCore11NetworkSyncC10fileSystem15conflictManagerAcA09SpaceFileF9Providing_p_AA08ConflictH0CtcfCTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO8rawValueSSvg", "_$s11AugmentCore18PerformanceMonitorC19getActiveOperationsSayAC16OperationMetricsVGyFTj", "_$s11AugmentCore19VersionControlErrorO17metadataCorruptedyACSScACmFWC", "_$s11AugmentCore18PerformanceMonitorCfD", "_$s11AugmentCore16LargeFileHandlerC04readcD8InChunks4from05chunkE0010completionE0y10Foundation3URLV_SbAH4DataV_s5Int64VANtcys6ResultOyAA0cd9OperationP0VAA0A5ErrorOGctFTj", "_$s11AugmentCore0A13ConfigurationC04saveC0yyFTq", "_$s11AugmentCore17SyncConfigurationV9isEnabledSbvM", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8progressSdvgTq", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV11successRateSdvg", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA3_", "_$s11AugmentCore19DependencyContainerC12searchEngineAA06SearchF0CyFTj", "_$s11AugmentCore24SpaceFileSystemProvidingP03getC04pathAA0aC0VSg10Foundation3URLV_tFTj", "_$s11AugmentCore15SnapshotManagerC06deleteC08snapshotSbAA0C0V_tFTq", "_$s11AugmentCore11FileVersionV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13DiffOperationV7contentSSvg", "_$s11AugmentCore18ErrorHandlingUtilsCACycfC", "_$s11AugmentCore23SimpleSyncConfigurationV04autoD0Sbvs", "_$s11AugmentCore8DiffTypeOMn", "_$s11AugmentCore12SnapshotFileV2id10Foundation4UUIDVvg", "_$s11AugmentCore18SecurityAuditEntryVSEAAMc", "_$s11AugmentCore11StorageInfoV7summarySSvpMV", "_$s11AugmentCore13BackupManagerCN", "_$s11AugmentCore22FileSystemMonitorErrorO015eventProcessingF0yACSScACmFWC", "_$s11AugmentCore11FileVersionV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore17PerformanceConfigV19cpuWarningThresholdSdvM", "_$s11AugmentCore18SecurityAuditEntryV9timestampSSvg", "_$s11AugmentCore0A5SpaceV2id10Foundation4UUIDVvg", "_$s11AugmentCore16LargeFileHandlerC02iscD0ySb10Foundation3URLVFTq", "_$s11AugmentCore11NetworkSyncC07performD013configurationyAA0D13ConfigurationV_tFTj", "_$s11AugmentCore14VersionControlCMu", "_$s11AugmentCore22SecurityRecommendationV8PriorityOSQAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV11taskTimeoutSdvg", "_$s11AugmentCore17SecurityViolationV8SeverityOSEAAMc", "_$s11AugmentCore10FileHasherP8finalizeSSyFTj", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOs0E3KeyAAMc", "_$s11AugmentCore16RecoveryStrategyO22checkNetworkConnectionyA2CmFWC", "_$s11AugmentCore16SHA256FileHasherVAA0dE0AAWP", "_$s11AugmentCore13FileOperationO8rawValueACSgSS_tcfC", "_$s11AugmentCore19UserInterfaceConfigV12maxListItemsSivs", "_$s11AugmentCore19SecurityAuditReportV11totalEventsSivpMV", "_$s11AugmentCore11FileItemRowV4fileAcA0cD0V_tcfC", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA1_", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO3qos8Dispatch0I3QoSV0J6SClassOvg", "_$s11AugmentCore8LoggableP6loggerAA0A6LoggerCvgTq", "_$s11AugmentCore11StorageInfoV12versionCountSivg", "_$s11AugmentCore17ErrorHistoryEntryV5error10occurredAt10isResolved08resolvedH0AcA011RecoverableC0V_10Foundation4DateVSbALSgtcfC", "_$s11AugmentCore8FileTypeO4from9extensionACSS_tFZ", "_$s11AugmentCore11FileVersionV7commentSSSgvpMV", "_$s11AugmentCore17SyncConfigurationV9directionAA0C9DirectionOvpMV", "_$s11AugmentCore12ErrorMetricsC6sharedACvgZ", "_$s11AugmentCore0A13ConfigurationC26defaultStorageMaxSizeBytess5Int64Vvg", "_$s11AugmentCore8SnapshotV10CodingKeysOSYAAMc", "_$s11AugmentCore13StorageConfigV24defaultMaxVersionAgeDaysSivM", "_$s11AugmentCore17SnapshotRetentionO11keepForTimeyACSdcACmFWC", "_$s11AugmentCore13BackupManagerC16createAllBackupsSbyFTq", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvsTj", "_$s11AugmentCore23VersionCreationDelegateMp", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvMTq", "_$s11AugmentCore0A5SpaceV9hashValueSivg", "_$s11AugmentCore18PreferencesManagerC13configuration07storageD0012notificationD06loggerAcA0A13ConfigurationC_AA07StorageD0CAA012NotificationD0CAA0A6LoggerCtcfCTq", "_$s11AugmentCore15FileSystemEventO4hash4intoys6HasherVz_tF", "_$s11AugmentCore17SyncConfigurationV9isEnabledSbvs", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvgTq", "_$s11AugmentCore13SecurityEventOMn", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvsTq", "_$s11AugmentCore13SyncDirectionOSeAAMc", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvMTj", "_$s11AugmentCore0A13ConfigurationC04loadC0yyFTq", "_$s11AugmentCore17ErrorHistoryEntryV10occurredAt10Foundation4DateVvpMV", "_$s11AugmentCore0A6LoggerC11LogCategoryO2uiyA2EmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO8rawValueSSvpMV", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore13CleanupResultVN", "_$s11AugmentCore17FileSystemMonitorC6logger20errorRecoveryManager06memoryI0AcA0A6LoggerC_AA05ErrorhI0CAA06MemoryI0Ctcfc", "_$s11AugmentCore19BackupConfigurationV04lastC9Timestamp10Foundation4DateVSgvg", "_$s11AugmentCore18ErrorHandlingUtilsC7execute9operation7context7handler11autoRecoverxSgxyKXE_SSAA0cD0_pSbtlFZ", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA6_", "_$s11AugmentCore0A5SpaceV16lastAccessedDate10Foundation0F0VvM", "_$s11AugmentCore13ErrorSeverityO8rawValueSSvpMV", "_$s11AugmentCore14StorageManagerC03setC6Policy_3foryAA0cF0V_10Foundation4UUIDVtFTq", "_$s11AugmentCore17ErrorHistoryEntryVSEAAMc", "_$s11AugmentCore19DependencyContainerC6inject18performanceMonitoryAA011PerformanceG0C_tFTj", "_$s11AugmentCore15BackupFrequencyOMn", "_$s11AugmentCore24SpaceFileSystemProvidingP03getC04pathAA0aC0VSg10Foundation3URLV_tFTq", "_$s11AugmentCore16SnapshotScheduleV9isEnabledSbvg", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvgTj", "_$s11AugmentCore11FileVersionVSEAAMc", "_$s11AugmentCore11FileItemRowV4fileAA0cD0Vvg", "_$s11AugmentCore13SpaceSettingsV18networkSyncEnabledSbvs", "_$s11AugmentCore15ErrorValidationCfd", "_$s11AugmentCore25MemoryOptimizationServiceCMn", "_$s11AugmentCore17SnapshotSchedulerCACycfCTq", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvpMV", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGBSdvg", "_$s11AugmentCore13MemoryManagerC14performCleanupyyFTq", "_$s11AugmentCore18PerformanceMonitorC14startOperationy10Foundation4UUIDVSSFTq", "_$s11AugmentCore18SecurityAuditEntryV6userIdSSvg", "_$s11AugmentCore13SyncDirectionO8rawValueACSgSS_tcfC", "_$s11AugmentCore18ConflictResolutionO10createCopyyA2CmFWC", "_$s11AugmentCore8LoggablePAAE6loggerAA0A6LoggerCvpMV", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore16SnapshotScheduleV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4modeSSvpMV", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA_", "_$s11AugmentCore13ErrorSeverityOSHAAMc", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV12errorMessageSSSgvpMV", "_$s11AugmentCore13HashAlgorithmO6sha256yA2CmFWC", "_$s11AugmentCore8FileItemV9isSyncingSbvg", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodON", "_$s11AugmentCore19DependencyContainerC6sharedACvgZ", "_$s11AugmentCore12ConflictTypeOSeAAMc", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore26FileSystemMonitoringConfigV18maxMonitoredSpacesSivs", "_$s11AugmentCore25MemoryOptimizationServiceC010unregisterD7Handler10identifierySS_tFTj", "_$s11AugmentCore12SnapshotFileVSEAAMc", "_$s11AugmentCore17PerformanceConfigV13fileBatchSizeSivg", "_$s11AugmentCore11FileVersionV9timestamp10Foundation4DateVvg", "_$s11AugmentCore18PerformanceMonitorC12measureAsync_5blockxSS_xyYaKXEtYaKlFTq", "_$s11AugmentCore15FileSystemEventO7createdyA2CmFWC", "_$s11AugmentCore17ErrorHistoryEntryV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore0A5SpaceV4path10Foundation3URLVvs", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvpMV", "_$s11AugmentCore15ConflictManagerCACycfC", "_$s11AugmentCore30DefaultVersionCreationDelegateCACycfCTj", "_$s11AugmentCore14StorageManagerC07enforceC6Policy_3forAA0F17EnforcementResultOAA0cF0V_AA0A5SpaceVtFTq", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvpMV", "_$s11AugmentCore19NotificationManagerCMa", "_$s11AugmentCore0A13ConfigurationCMm", "_$s11AugmentCore13MemoryManagerC24unregisterCleanupHandler10identifierySS_tFTj", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA1_", "_$s11AugmentCore16SnapshotScheduleV9spacePath10Foundation3URLVvg", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO4highyA2EmFWC", "_$s11AugmentCore18ConfigurationErrorOMn", "_$s11AugmentCore13SpaceSettingsV14autoVersioningSbvs", "_$s11AugmentCore13MemoryManagerC14performCleanupyyFTj", "_$s11AugmentCore13BackupManagerC07restoreC010backupPath05spaceG08passwordSb10Foundation3URLV_AJSSSgtFTj", "_$s11AugmentCore17FileAccessSummaryVSEAAMc", "_$s11AugmentCore13FolderVersionV11storagePath10Foundation3URLVvg", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvMTq", "_$s11AugmentCore19MemoryUsageSnapshotVN", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOSHAAMc", "_$s11AugmentCore0A6LoggerC8LogLevelOSQAAMc", "_$s11AugmentCore13FolderVersionV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore15SecurityManagerC11getAuditLogSayAA0cF5EntryVGyFTq", "_$s11AugmentCore15FileSystemEventO7unknownyA2CmFWC", "_$s11AugmentCore14VersionControlC07restoreC010folderPath7versionSb10Foundation3URLV_AA06FolderC0VtFTj", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvMTq", "_$s11AugmentCore19VersionControlErrorOMn", "_$s11AugmentCore12ErrorMetricsCN", "_$s11AugmentCore21SecurityAuditReporterCMn", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC5errors5Error_pSgvpMV", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvpMV", "_$s11AugmentCore17PerformanceConfigV22memoryWarningThresholds5Int64VvM", "_$s11AugmentCore18PreferencesManagerC7Combine16ObservableObjectAAMc", "_$s11AugmentCore0A5SpaceV16lastAccessedDate10Foundation0F0Vvg", "_$s11AugmentCore0A13ConfigurationC06importC04fromSbSS_tF", "_$s11AugmentCore16SnapshotScheduleV2id10Foundation4UUIDVvg", "_$s11AugmentCore16LargeFileHandlerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceH0CtcfC", "_$s11AugmentCore17FileAccessSummaryV19uniqueFilesAccessedSivg", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyOSHAAMc", "_$s11AugmentCore0A5ErrorO24conflictResolutionFailedyACSS_SStcACmFWC", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvg", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvgTj", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvsTq", "_$s11AugmentCore19VersionControlErrorO20localizedDescriptionSSvpMV", "_$s11AugmentCore14DataEncryptionC12generateSalt4size10Foundation0C0VSi_tFZ", "_$s11AugmentCore19SecurityAuditReportV7endDate10Foundation0G0VSgvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOSHAAMc", "_$s11AugmentCore21SecurityAuditReporterCMa", "_$s11AugmentCore13FileOperationO7monitoryA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOMn", "_$s11AugmentCore11StorageInfoV20augmentDirectorySizes5Int64Vvg", "_$s11AugmentCore23SimpleSyncConfigurationV4hash4intoys6HasherVz_tF", "_$s11AugmentCore13SpaceSettingsV21monitorSubdirectoriesSbvpMV", "_$s11AugmentCore12SnapshotFileV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV8strategyAC0D8StrategyOvg", "_$s11AugmentCore0A13ConfigurationCMn", "_$s11AugmentCore0A6LoggerC11LogCategoryO13notificationsyA2EmFWC", "_$s11AugmentCore17SyncConfigurationV9isEnabledSbvg", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvsTj", "_$s11AugmentCore0A6LoggerC4info_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTq", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGBSdvM", "_$s11AugmentCore12ExportFormatOSYAAMc", "_$s11AugmentCore13StorageConfigV23defaultWarningThresholdSdvpMV", "_$s11AugmentCore13StorageConfigV29notificationsEnabledByDefaultSbvM", "_$s11AugmentCore17PerformanceConfigV03maxC11HistorySizeSivg", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGBSdvs", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV8cpuUsageSdvg", "_$s11AugmentCore12FileConflictV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore11NetworkSyncC16getConfiguration9spacePathAA0dF0VSg10Foundation3URLV_tFTj", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA4_", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvgTq", "_$s11AugmentCore10FileHasherP6update4datay10Foundation4DataV_tFTj", "_$s11AugmentCore12StorageIssueO16approachingLimityACSdcACmFWC", "_$s11AugmentCore12SnapshotFileV8filePath10Foundation3URLVvpMV", "_$s11AugmentCore18ConfigurationErrorON", "_$s11AugmentCore13LoggingConfigV16logRetentionDaysSivM", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV11memoryUsages6UInt64Vvg", "_$s11AugmentCore15ConflictManagerC15detectConflicts3forSayAA04FileC0VG10Foundation3URLV_tFTj", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4name4path4mode7isValidAESS_S2SSbtcfC", "_$s11AugmentCore8FileTypeO10systemIconSSvpMV", "_$s11AugmentCore19BackupConfigurationV9spacePath10Foundation3URLVvg", "_$s11AugmentCore12SearchEngineC6sharedACvgZ", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV16handlersExecutedSivpMV", "_$s11AugmentCore16SnapshotScheduleV9retentionAA0C9RetentionOvg", "_$s11AugmentCore17ErrorHistoryEntryV10occurredAt10Foundation4DateVvg", "_$s11AugmentCore17SnapshotFrequencyO6hourlyyA2CmFWC", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvgTq", "_$s11AugmentCore13CleanupResultV15removedVersions10freedBytes6errorsACSi_s5Int64VSays5Error_pGtcfC", "_$s11AugmentCore14DataEncryptionC0D5ErrorO19keyGenerationFailedyA2EmFWC", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutesSivs", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4name4type8priority8workItem15progressHandler010completionM0AESS_AC0F4TypeOAC0F8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtcfc", "_$s11AugmentCore20ErrorRecoveryManagerC07dismissC6DialogyyFTq", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvMTj", "_$s11AugmentCore0A5ErrorO14memoryPressureyACSS_tcACmFWC", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvgTq", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvgTj", "_$s11AugmentCore15FileSystemEventO2eeoiySbAC_ACtFZ", "_$s11AugmentCore17FileSystemMonitorC14stopMonitoringyyFTq", "_$s11AugmentCore26FileSystemMonitoringConfigV25emergencyCleanupThresholdSivM", "_$s11AugmentCore8FileItemV12versionCountSivM", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvMTj", "_$s11AugmentCore13SpaceSettingsV23storageWarningThresholdSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV22enableProgressTrackingSbvg", "_$s11AugmentCore11FileVersionV11contentHashSSvpMV", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore13LoggingConfigVMa", "_$s11AugmentCore13FolderVersionVMn", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC2id10Foundation4UUIDVvg", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsVMa", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvpMV", "_$s11AugmentCore17SnapshotSchedulerCMu", "_$s11AugmentCore8SnapshotV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore20ErrorRecoveryManagerC013executeManualD0_3foryAA0D8StrategyO_AA011RecoverableC0VtFTj", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvMTj", "_$s11AugmentCore12FileConflictV13remoteVersionAA0cF0VvpMV", "_$s11AugmentCore17PerformanceConfigV22maxCompletedOperationsSivM", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore18ConflictResolutionO9useRemoteyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC03getD5StatsAC0dG0VyFTq", "_$s11AugmentCore17FileSystemMonitorC27enableCommandSSaveDetectionyyF", "_$s11AugmentCore13MemoryManagerC11trackObjectyyyXlFTq", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV9startTimeSdvpMV", "_$s11AugmentCore8FileItemV4nameSSvM", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvpMV", "_$s11AugmentCore0A5ErrorO08internalC0yACSS_SStcACmFWC", "_$s11AugmentCore12ConflictTypeO8rawValueACSgSS_tcfC", "_$s11AugmentCore12SnapshotFileV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore12ConflictTypeOSHAAMc", "_$s11AugmentCore18PerformanceMonitorC14stopMonitoringyyFTj", "_$s11AugmentCore15SnapshotManagerCMo", "_$s11AugmentCore12FileConflictVN", "_$s11AugmentCore16RecoverableErrorV2eeoiySbAC_ACtFZ", "_$s11AugmentCore14RiskAssessmentV07overallC0AA0C5LevelOvg", "_$s11AugmentCore0A5SpaceV8settingsAA0C8SettingsVvM", "_$s11AugmentCore17ErrorHistoryEntryV10resolvedAt10Foundation4DateVSgvpMV", "_$s11AugmentCore13StorageConfigV28defaultCleanupFrequencyHoursSivM", "_$s11AugmentCore17FileSystemMonitorC22configureSaveDetectionyyAC0gH6ConfigVFTq", "_$s11AugmentCore27ConflictResolutionUtilitiesCACycfc", "_$s11AugmentCore0A6LoggerC11LogCategoryO6searchyA2EmFWC", "_$s11AugmentCore17FileSystemMonitorC19setDebounceIntervalyySdFTj", "_$s11AugmentCore15BackupFrequencyO6manualyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCMa", "_$s11AugmentCore12SearchConfigV12indexTimeoutSdvpMV", "_$s11AugmentCore13FileOperationOSYAAMc", "_$s11AugmentCore14SecurityConfigV20securityScanIntervalSivg", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvsTq", "_$s11AugmentCore11FileVersionV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore12SearchConfigV12indexTimeoutSdvs", "_$s11AugmentCore15PreviewFileTypeO3pdfyA2CmFWC", "_$s11AugmentCore19DependencyContainerCN", "_$s11AugmentCore14SecurityConfigV26securityEventRetentionDaysSivg", "_$s11AugmentCore20ErrorRecoveryManagerC13configuration011preferencesE06loggerAcA0A13ConfigurationC_AA011PreferencesE0CAA0A6LoggerCtcfCTq", "_$s11AugmentCore8FileItemV12hasConflictsSbvM", "_$s11AugmentCore14StorageManagerCfD", "_$s11AugmentCore14SecurityConfigV20securityScanIntervalSivs", "_$s11AugmentCore19NotificationManagerC19cancelNotifications3forySS_tFTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyON", "_$s11AugmentCore15BackupRetentionOMn", "_$s11AugmentCore16RecoveryStrategyO8allCasesSayACGvgZ", "_$s11AugmentCore19MemoryUsageSnapshotVMn", "_$s11AugmentCore18PerformanceMonitorCMo", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV23memoryPressureThresholdSdvg", "_$s11AugmentCore12ConflictTypeO8allCasesSayACGvpZMV", "_$s11AugmentCore17PerformanceConfigV22maxCompletedOperationsSivg", "_$s11AugmentCore15BackupRetentionO7keepAllyA2CmFWC", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvgTj", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvsTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO10aggressiveyA2EmFWC", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore19DependencyContainerC13augmentLoggerAA0aF0CyFTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO8balancedyA2EmFWC", "_$s11AugmentCore19MemoryPressureLevelO8criticalyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC10submitTask4name4type8priority8workItem15progressHandler010completionN0AC0cG0CSS_AC0G4TypeOAC0G8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtFTq", "_$s11AugmentCore17FileSystemMonitorCfD", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV15memoryRecovereds6UInt64Vvg", "_$s11AugmentCore13ErrorHandlingP6loggerAA0A6LoggerCvgTj", "_$s11AugmentCore12SnapshotFileV16modificationDate10Foundation0F0VvpMV", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvsTj", "_$s11AugmentCore13SpaceSettingsV17maxVersionAgeDaysSivM", "_$s11AugmentCore23SimpleSyncConfigurationV20syncFrequencyMinutesSivpMV", "_$s11AugmentCore14VersioningModeOMa", "_$s11AugmentCore18PreferencesManagerCMu", "_$s11AugmentCore26FileSystemMonitoringConfigV25throttlingEntryExpirationSdvs", "_$s11AugmentCore17PerformanceConfigV31emergencyMemoryCleanupThresholdSivM", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOMn", "_$s11AugmentCore8FileItemV12hasConflictsSbvg", "_$s11AugmentCore17FileSystemMonitorC32unsuppressAutoVersioningForSpace9spacePathy10Foundation3URLV_tFTq", "_$s11AugmentCore19UserInterfaceConfigV12maxListItemsSivpMV", "_$s11AugmentCore26FileSystemMonitoringConfigV25throttlingEntryExpirationSdvg", "_$s11AugmentCore15ErrorValidationCMn", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV11isCompletedSbvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO16applicationFocusyA2EmFWC", "_$s11AugmentCore19DependencyContainerC6inject17fileSystemMonitoryAA04FilegH0C_tFTj", "_$s11AugmentCore8FileItemV13formattedSizeSSvg", "_$s11AugmentCore8FileDiffV11fromVersionAA0cF0Vvg", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvsTq", "_$s11AugmentCore0A6LoggerC15minimumLogLevel17enableFileLoggingA2C0eF0O_SbtcfCTj", "_$s11AugmentCore0A5ErrorO18preconditionFailedyACSS_SStcACmFWC", "_$s11AugmentCore17ErrorHistoryEntryV10isResolvedSbvM", "_$s11AugmentCore19BackupConfigurationV9isEnabledSbvg", "_$s11AugmentCore8FileItemV9isSyncingSbvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityON", "_$s11AugmentCore15SnapshotManagerC12getSnapshots9spacePathSayAA0C0VG10Foundation3URLV_tFTj", "_$s11AugmentCore13ErrorSeverityO8criticalyA2CmFWC", "_$s11AugmentCore0A6LoggerC18setMinimumLogLevelyyAC0fG0OFTq", "_$s11AugmentCore13LoggingConfigV011performanceC7EnabledSbvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvMTq", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA2_", "_$s11AugmentCore19VersionControlErrorOMa", "_$s11AugmentCore26FileSystemMonitoringConfigV15fsEventsLatencySdvs", "_$s11AugmentCore13ErrorCategoryO8allCasesSayACGvpZMV", "_$s11AugmentCore19DependencyContainerC13configurationAA0A13ConfigurationCyFTj", "_$s11AugmentCore13DiffOperationV0D4TypeOMa", "_$s11AugmentCore0A5ErrorO19fileOperationFailedyACSS_SSs0C0_ptcACmFWC", "_$s11AugmentCore19BackupConfigurationV2id10Foundation4UUIDVvg", "_$s11AugmentCore13SpaceSettingsV9hashValueSivg", "_$s11AugmentCore13StoragePolicyV0D4TypeO11maxVersionsyAESicAEmFWC", "_$s11AugmentCore15MetadataManagerC011loadVersionC010folderPath05spaceH0SayAA06FolderF0VG10Foundation3URLV_ALtFTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsVMn", "_$s11AugmentCore15SecurityManagerC013stopAccessingC14ScopedResourceyy10Foundation3URLVFTj", "_$s11AugmentCore14RiskAssessmentVMa", "_$s11AugmentCore0A5ErrorO20storageCleanupFailedyACSS_tcACmFWC", "_$s11AugmentCore12SnapshotFileV10CodingKeysOSHAAMc", "_$s11AugmentCore13LoggingConfigV14maxLogFileSizes5Int64Vvs", "_$s11AugmentCore16RecoveryStrategyO16reimportSettingsyA2CmFWC", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvMTj", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore11NetworkSyncC13syncAllSpacesSbyFTj", "_$s11AugmentCore18ConflictResolutionON", "_$s11AugmentCore14SecurityConfigV17encryptionEnabledSbvg", "_$s11AugmentCore19DependencyContainerC13augmentLoggerAA0aF0CyFTj", "_$s11AugmentCore13MemoryManagerC24unregisterCleanupHandler10identifierySS_tFTq", "_$s11AugmentCore23SimpleSyncConfigurationVMa", "_$s11AugmentCore25MemoryOptimizationServiceC02isD6NeededSbyFTq", "_$s11AugmentCore0A6LoggerC11LogCategoryO11performanceyA2EmFWC", "_$s11AugmentCore0A6LoggerC8LogLevelO8criticalyA2EmFWC", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOSYAAMc", "_$s11AugmentCore11FileItemRowVN", "_$s11AugmentCore14SecurityConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore0A6LoggerC11LogCategoryOSYAAMc", "_$s11AugmentCore24LargeFileOperationResultV9operationAA0cdE0Ovg", "_$s11AugmentCore0A5SpaceV12isMonitoringSbvg", "_$s11AugmentCore14DataEncryptionC7encrypty10Foundation0C0VAGKFZ", "_$s11AugmentCore0A5ErrorO23directoryCreationFailedyACSS_s0C0_ptcACmFWC", "_$s11AugmentCore9RiskLevelO8rawValueACSgSS_tcfC", "_$s11AugmentCore11FileVersionV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore0A5ErrorO23searchEngineUnavailableyACSS_tcACmFWC", "_$s11AugmentCore18ErrorHandlingUtilsC12executeAsync9operation7context7handler11autoRecoverxSgxyYaKXE_SSAA0cD0_pSbtYalFZTu", "_$s11AugmentCore13SyncFrequencyO8rawValueSSvpMV", "_$s11AugmentCore25MemoryOptimizationServiceC19updateConfigurationyyAC0D6ConfigVFTq", "_$s11AugmentCore0A6LoggerC11LogCategoryO6backupyA2EmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV15tasksByPrioritySDyAC04TaskI0OSiGvg", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvgTj", "_$s11AugmentCore17SnapshotRetentionON", "_$s11AugmentCore16SnapshotScheduleV9spacePath10Foundation3URLVvpMV", "_$s11AugmentCore13LoggingConfigV011performanceC7EnabledSbvM", "_$s11AugmentCore15SnapshotManagerC6sharedACvgZ", "_$s11AugmentCore16SnapshotScheduleV04lastC9Timestamp10Foundation4DateVSgvg", "_$s11AugmentCore0A5ErrorO18recoverySuggestionSSSgvpMV", "_$s11AugmentCore19UserInterfaceConfigV14minWindowWidthSdvpMV", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvMTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV8strategyAC0D8StrategyOvg", "_$s11AugmentCore19DependencyContainerC18preferencesManagerAA011PreferencesF0CyFTq", "_$s11AugmentCore0A5ErrorO16errorDescriptionSSSgvpMV", "_$s11AugmentCore16LargeFileHandlerCMm", "_$s11AugmentCore13LoggingConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore13NetworkConfigV14networkTimeoutSdvM", "_$s11AugmentCore14VersionControlCfd", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvMTj", "_$s11AugmentCore15SnapshotManagerC07restoreC08snapshotSbAA0C0V_tFTq", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore15SecurityManagerC06removeC14ScopedBookmark3fory10Foundation3URLV_tFTj", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO13fileOperationyA2EmFWC", "_$s11AugmentCore13MemoryManagerC15startMonitoringyyFTq", "_$s11AugmentCore13DiffOperationVMn", "_$s11AugmentCore30DefaultVersionCreationDelegateCACycfCTq", "_$s11AugmentCore12ExportFormatOs12CaseIterableAAMc", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore8FileItemV13formattedSizeSSvpMV", "_$s11AugmentCore13SecurityEventO21resourceAccessStartedyACSS_tcACmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO12conservativeyA2EmFWC", "_$s11AugmentCore0A6LoggerC7warning_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTq", "_$s11AugmentCore25MemoryOptimizationServiceCfD", "_$s11AugmentCore22SecurityRecommendationV5titleSSvpMV", "_$s11AugmentCore8FileItemV16modificationDate10Foundation0F0VvM", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvsTq", "_$s11AugmentCore13NetworkConfigV13maxUploadSizes5Int64Vvg", "_$s11AugmentCore18SecurityAuditEntryV6userIdSSvpMV", "_$s11AugmentCore13StorageConfigV18monitoringIntervalSdvM", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvgTq", "_$s11AugmentCore19DependencyContainerC6inject13memoryManageryAA06MemoryG0C_tFTq", "_$s11AugmentCore0A5ErrorO17metadataCorruptedyACSS_SSSgtcACmFWC", "_$s11AugmentCore13SecurityEventO24bookmarkResolutionFailedyACSS_SStcACmFWC", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvpMV", "_$s11AugmentCore19DependencyContainerCMu", "_$s11AugmentCore10DiffEngineC08generateC011fromVersion02toG0AA04FileC0VAA0iG0V_AJtFTq", "_$s11AugmentCore20DependencyInjectablePAAE12dependenciesAA0C9ContainerCvg", "_$s11AugmentCore8FileItemVMn", "_$s11AugmentCore13SpaceSettingsVACycfC", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidthSdvM", "_$s11AugmentCore15MetadataManagerC6sharedACvpZMV", "_$s11AugmentCore13ErrorCategoryOs12CaseIterableAAMc", "_$s11AugmentCore13NetworkConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore17SnapshotSchedulerCMm", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusOSHAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC9startedAt10Foundation4DateVSgvpMV", "_$s11AugmentCore19SecurityAuditReportVSeAAMc", "_$s11AugmentCore27BackgroundProcessingServiceCN", "_$s11AugmentCore15MetadataManagerC011loadVersionC010folderPath05spaceH0SayAA06FolderF0VG10Foundation3URLV_ALtFTq", "_$s11AugmentCore0A5ErrorO06serverC0yACSi_SStcACmFWC", "_$s11AugmentCore17SnapshotSchedulerC25executeScheduledSnapshots33_7E7566F25733DFECB0CF9BB3A350FCB8LLyyF", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvpMV", "_$s11AugmentCore17SnapshotFrequencyO8intervalSdvg", "_$s11AugmentCore11StorageInfoV13oldestVersion10Foundation4DateVvpMV", "_$s11AugmentCore17FileSystemMonitorCfd", "_$s11AugmentCore8FileItemV12versionCountSivg", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore11FileVersionVMa", "_$s11AugmentCore18LargeFileOperationO8rawValueACSgSS_tcfC", "_$s11AugmentCore13CleanupResultV15removedVersionsSivg", "_$s11AugmentCore15MetadataManagerC015saveFileVersionC07version9spacePathSbAA0fG0V_10Foundation3URLVtFTq", "_$s11AugmentCore15SnapshotManagerC12getSnapshots9spacePathSayAA0C0VG10Foundation3URLV_tFTq", "_$s11AugmentCore11NetworkSyncCMm", "_$s11AugmentCore11FileVersionV2id8filePath9timestamp4size7comment11contentHash07storageG0AC10Foundation4UUIDV_AK3URLVAK4DateVs6UInt64VSSSgSSAOtcfC", "_$s11AugmentCore17SyncConfigurationVs12IdentifiableAAMc", "_$s11AugmentCore24SpaceFileSystemProvidingMp", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvpMV", "_$s11AugmentCore17PerformanceConfigV19cpuWarningThresholdSdvpMV", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvMTj", "_$s11AugmentCore21SecurityAuditReporterC15securityManager6loggerAcA0cG0C_AA0A6LoggerCtcfc", "_$s11AugmentCore0A5ErrorO18invalidVersionDatayACSS_SStcACmFWC", "_$s11AugmentCore13SecurityEventO19securityHealthCheckyACSi_tcACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO6failedyA2GmFWC", "_$s11AugmentCore8FileTypeO5imageyA2CmFWC", "_$s11AugmentCore17SecurityViolationV4typeSSvpMV", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOMn", "_$s11AugmentCore22SecurityRecommendationVSeAAMc", "_$s11AugmentCore25MemoryOptimizationServiceCMm", "_$s11AugmentCore0A6LoggerC8LogLevelO8rawValueSivg", "_$s11AugmentCore16RecoverableErrorV2idSSvg", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV12runningTasksSivpMV", "_$s11AugmentCore18PerformanceMonitorCMn", "_$s11AugmentCore23SimpleSyncConfigurationV9authTokenSSvs", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO9hashValueSivg", "_$s11AugmentCore15BackupFrequencyOSHAAMc", "_$s11AugmentCore19UserInterfaceConfigV14minWindowWidthSdvs", "_$s11AugmentCore19BackupConfigurationV04lastC9Timestamp10Foundation4DateVSgvs", "_$s11AugmentCore19NotificationManagerC6sharedACvpZMV", "_$s11AugmentCore21SecurityAuditReporterCMm", "_$s11AugmentCore18PreferencesManagerC15resetToDefaultsyyF", "_$s11AugmentCore13SyncFrequencyO6hourlyyA2CmFWC", "_$s11AugmentCore13BackupManagerC16getConfiguration9spacePathAA0cF0VSg10Foundation3URLV_tFTq", "_$s11AugmentCore14DataEncryptionC7encrypty10Foundation0C0VSSKFZ", "_$s11AugmentCore12SearchConfigVSEAAMc", "_$s11AugmentCore17SecurityViolationVN", "_$s11AugmentCore12StorageIssueOMn", "_$s11AugmentCore18ConfigurationErrorO16errorDescriptionSSSgvpMV", "_$s11AugmentCore12SearchResultV9relevanceSdvg", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO15defaultPriorityAC0fI0Ovg", "_$s11AugmentCore15ErrorValidationCfD", "_$s11AugmentCore8FileTypeO4codeyA2CmFWC", "_$ss6ResultO11AugmentCores5Error_pRs_rlE05mapTobD07contextAByxAC0bD0OGSSSg_tF", "_$s11AugmentCore13FileOperationO13requiresWriteSbvpMV", "_$s11AugmentCore24LargeFileOperationResultV10throughputSdvg", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeON", "_$s11AugmentCore15FileSystemEventOMa", "_$s11AugmentCore12ExportFormatO4htmlyA2CmFWC", "_$s11AugmentCore13NetworkConfigV20syncEnabledByDefaultSbvM", "_$s11AugmentCore19NotificationManagerCMu", "_$s11AugmentCore13HashAlgorithmO2eeoiySbAC_ACtFZ", "_$s11AugmentCore16RecoveryStrategyO18runAsAdministratoryA2CmFWC", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore13PreviewEngineC08generateC08filePath4sizeSo7NSImageCSg10Foundation3URLV_So6CGSizeVtFTq", "_$s11AugmentCore23VersionCreationDelegateP010createFileC08filePath7commentSb10Foundation3URLV_SSSgtFTq", "_$s11AugmentCore15BackupRetentionO4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore18ConfigurationErrorO12invalidValueyACSS_SStcACmFWC", "_$s11AugmentCore18PreferencesManagerCN", "_$s11AugmentCore22FileSystemMonitorErrorOs0F0AAMc", "_$s11AugmentCore0A5ErrorO14rollbackFailedyACSS_SStcACmFWC", "_$s11AugmentCore16RecoverableErrorV11userMessageSSvg", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvgTq", "_$s11AugmentCore12SearchResultV2id10Foundation4UUIDVvg", "_$s11AugmentCore8SnapshotVSeAAMc", "_$s11AugmentCore14VersionControlCN", "_$s11AugmentCore17SnapshotSchedulerC5timer33_7E7566F25733DFECB0CF9BB3A350FCB8LLSo7NSTimerCSgvs", "_$s11AugmentCore24LargeFileOperationResultV16formattedSummarySSvpMV", "_$s11AugmentCore19BackupConfigurationV10backupPath10Foundation3URLVvg", "_$s11AugmentCore0A5SpaceV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore17FileAccessSummaryVMn", "_$s11AugmentCore18PerformanceMonitorC20performMemoryCleanupyyFTq", "_$s11AugmentCore19MemoryPressureLevelO6normalyA2CmFWC", "_$s11AugmentCore14DataEncryptionC0D5ErrorO18keyRetrievalFailedyA2EmFWC", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV18commandSTimeWindowSdvs", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvsTq", "_$s11AugmentCore23PolicyEnforcementResultON", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvgTj", "_$s11AugmentCore12SearchConfigV14maxQueryLengthSivM", "_$s11AugmentCore15SecurityManagerCfD", "_$s11AugmentCore16LargeFileHandlerC05largeD9Thresholds5Int64VvpZMV", "_$s11AugmentCore19NotificationManagerC19sendStorageCritical9spaceNameySS_tFTq", "_$s11AugmentCore17ErrorHistoryEntryV10isResolvedSbvg", "_$s11AugmentCore8FileTypeO8rawValueACSgSS_tcfC", "_$s11AugmentCore8SnapshotV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore18ErrorHandlingUtilsCACycfCTj", "_$s11AugmentCore13FolderVersionV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore0A5ErrorO14indexCorruptedyACSS_tcACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11isCompletedSbvgTq", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvpMV", "_$s11AugmentCore0A6LoggerC11LogCategoryOSHAAMc", "_$s11AugmentCore15BackupFrequencyOSYAAMc", "_$s11AugmentCore17SnapshotRetentionOMa", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA4_", "_$s11AugmentCore13BackupManagerC16addConfigurationySbAA0cF0VFTq", "_$s11AugmentCore24LargeFileOperationResultV8durationSdvpMV", "_$s11AugmentCore17SyncConfigurationV04lastC9Timestamp10Foundation4DateVSgvg", "_$s11AugmentCore26FileSystemMonitoringConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore14RiskAssessmentV06mediumC6EventsSivg", "_$s11AugmentCore13StoragePolicyV0D4TypeOSEAAMc", "_$s11AugmentCore17SyncConfigurationV9hashValueSivpMV", "_$s11AugmentCore18ConflictResolutionO8rawValueSSvg", "_$s11AugmentCore19SecurityAuditReportV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore12SearchEngineCACycfCTj", "_$s11AugmentCore22ConflictResolutionTypeOMa", "_$s11AugmentCore25MemoryOptimizationServiceC13memoryManager6logger18performanceMonitor6configAcA0cG0C_AA0A6LoggerCAA011PerformanceJ0CAC0D6ConfigVtcfc", "_$s11AugmentCore0A6LoggerC8LogLevelOSHAAMc", "_$s11AugmentCore17SyncConfigurationVMa", "_$s11AugmentCore17SnapshotFrequencyOSQAAMc", "_$s11AugmentCore12SnapshotFileV10CodingKeysOSYAAMc", "_$s11AugmentCore13SyncDirectionO13bidirectionalyA2CmFWC", "_$s11AugmentCore17PerformanceConfigV31emergencyMemoryCleanupThresholdSivg", "_$s11AugmentCore16LargeFileHandlerC09calculateD4Hash3for9algorithm08progressE0010completionE0y10Foundation3URLV_AA0G9AlgorithmOySd_SStcys6ResultOySSAA0A5ErrorOGctFTq", "_$s11AugmentCore22FileSystemMonitorErrorO11invalidPathyACSScACmFWC", "_$s11AugmentCore13StoragePolicyVSeAAMc", "_$s11AugmentCore15ConflictManagerCMo", "_$s11AugmentCore19DependencyContainerC6inject13backupManageryAA06BackupG0C_tFTj", "_$s11AugmentCore22ConflictResolutionTypeO9hashValueSivpMV", "_$s11AugmentCore17SyncConfigurationV2id9spacePath06remoteG09direction9frequency9isEnabled04lastC9TimestampAC10Foundation4UUIDV_AK3URLVAoA0C9DirectionOAA0C9FrequencyOSbAK4DateVSgtcfC", "_$s11AugmentCore8FileItemV9hashValueSivpMV", "_$s11AugmentCore12ConflictTypeOSEAAMc", "_$s11AugmentCore14SecurityConfigV26securityEventRetentionDaysSivs", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvgTq", "_$s11AugmentCore19DependencyContainerC15conflictManagerAA08ConflictF0CyFTq", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvsTj", "_$s11AugmentCore0A13ConfigurationC8getValue7keyPathxs03KeyG0CyACxG_tlF", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore13SyncFrequencyO8rawValueACSgSS_tcfC", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvgTq", "_$s11AugmentCore14VersionControlCfD", "_$s11AugmentCore14RiskAssessmentV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivpMV", "_$s11AugmentCore11NetworkSyncCfD", "_$s11AugmentCore15BackupRetentionO6encode2toys7Encoder_p_tKF", "_$s11AugmentCore16RecoverableErrorV8categoryAA0D8CategoryOvg", "_$s11AugmentCore13SpaceSettingsVSeAAMc", "_$s11AugmentCore11NetworkSyncCMn", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigVMn", "_$s11AugmentCore8FileItemV4sizes5Int64Vvs", "_$s11AugmentCore17SnapshotFrequencyOMn", "_$s11AugmentCore8FileItemV4typeAA0C4TypeOvM", "_$s11AugmentCore0A5SpaceV4nameSSvpMV", "_$s11AugmentCore17FileSystemMonitorC18performMaintenanceyyFTj", "_$s11AugmentCore17PerformanceConfigV22memoryWarningThresholds5Int64Vvg", "_$s11AugmentCore12SearchConfigV18relevanceThresholdSdvpMV", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvMTq", "_$s11AugmentCore0A5SpaceV16lastAccessedDate10Foundation0F0Vvs", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO3lowyA2EmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC9startedAt10Foundation4DateVSgvgTq", "_$s11AugmentCore12StorageIssueO12diskSpaceLowyACs5Int64VcACmFWC", "_$s11AugmentCore13StoragePolicyV7enabledSbvpMV", "_$s11AugmentCore8FileTypeOSHAAMc", "_$s11AugmentCore0A5SpaceV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabledSbvg", "_$s11AugmentCore15ErrorValidationCMm", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoringSbvg", "_$s11AugmentCore8SnapshotVMn", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV9startTimeSdvg", "_$s11AugmentCore12SearchConfigV14minQueryLengthSivM", "_$s11AugmentCore18PerformanceMonitorC12endOperation_7success12errorMessagey10Foundation4UUIDV_SbSSSgtFTj", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivsTj", "_$s11AugmentCore8DiffTypeO4textyA2CmFWC", "_$s11AugmentCore16RecoverableErrorVN", "_$s11AugmentCore23SimpleSyncConfigurationV04autoD0SbvM", "_$s11AugmentCore8DiffTypeOSYAAMc", "_$s11AugmentCore14DataEncryptionC0D5ErrorOMn", "_$s11AugmentCore14VersioningModeOSHAAMc", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA_", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV13operationNameSSvpMV", "_$s11AugmentCore9RiskLevelOSQAAMc", "_$s11AugmentCore23VersionCreationDelegateTL", "_$s11AugmentCore13StorageConfigV18monitoringIntervalSdvg", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvgTq", "_$s11AugmentCore8FileItemV4sizes5Int64VvpMV", "_$s11AugmentCore13FolderVersionVMa", "_$s11AugmentCore18PreferencesManagerCMm", "_$s11AugmentCore13HashAlgorithmO9hashValueSivg", "_$s11AugmentCore12SearchResultV2eeoiySbAC_ACtFZ", "_$s11AugmentCore10DiffEngineCMn", "_$s11AugmentCore15ConflictManagerCACycfCTj", "_$s11AugmentCore19UserInterfaceConfigV19defaultWindowHeightSdvg", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV12errorMessageSSSgvg", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvMTq", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA1_", "_$s11AugmentCore19DependencyContainerC6inject27backgroundProcessingServiceyAA010BackgroundgH0C_tFTj", "_$s11AugmentCore9RiskLevelOSYAAMc", "_$s11AugmentCore18PerformanceMonitorC14startOperationy10Foundation4UUIDVSSFTj", "_$s11AugmentCore17SecurityViolationVSEAAMc", "_$s11AugmentCore13FileOperationON", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA3_", "_$s11AugmentCore8FileTypeO8iconNameSSvpMV", "_$s11AugmentCore19SecurityAuditReportV15recommendationsSayAA0C14RecommendationVGvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigVSEAAMc", "_$s11AugmentCore13SpaceSettingsV15excludePatternsSaySSGvpMV", "_$s11AugmentCore20DependencyInjectableP12dependenciesAA0C9ContainerCvgTj", "_$s11AugmentCore13SecurityEventO22bookmarkCreationFailedyACSS_SStcACmFWC", "_$s11AugmentCore13StorageConfigV27autoCleanupEnabledByDefaultSbvpMV", "_$s11AugmentCore10DiffEngineC08generateC011fromVersion02toG0AA04FileC0VAA0iG0V_AJtFTj", "_$s11AugmentCore21SecurityAuditReporterCMu", "_$s11AugmentCore14StorageManagerC6logger06memoryD0AcA0A6LoggerC_AA06MemoryD0CSgtcfC", "_$s11AugmentCore13SyncDirectionON", "_$s11AugmentCore16LargeFileHandlerCMa", "_$s11AugmentCore13BackupManagerC19removeConfiguration9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore19BackupConfigurationV04lastC9Timestamp10Foundation4DateVSgvM", "_$s11AugmentCore13SpaceSettingsV14versioningModeAA010VersioningF0OvpMV", "_$s11AugmentCore12ErrorMetricsCfD", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV7successSbSgvg", "_$s11AugmentCore12ErrorMetricsC06recordC0_7contextyAA0aC0O_SSSgtF", "_$s11AugmentCore12SearchEngineC15indexSpaceAsync9spacePathSb10Foundation3URLV_tYaFTq", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvgTq", "_$s11AugmentCore17PerformanceConfigVN", "_$s11AugmentCore19DependencyContainerC6inject18preferencesManageryAA011PreferencesG0C_tFTj", "_$s11AugmentCore27BackgroundProcessingServiceCMa", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvgTj", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvsTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyOSQAAMc", "_$s11AugmentCore0A5ErrorO16sandboxViolationyACSS_tcACmFWC", "_$s11AugmentCore9RiskLevelON", "_$s11AugmentCore0A5SpaceV4path10Foundation3URLVvM", "_$s11AugmentCore18LargeFileOperationO4hashyA2CmFWC", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvMTq", "_$s11AugmentCore19DependencyContainerC6inject14versionControlyAA07VersionG0C_tFTj", "_$s11AugmentCore18ErrorHandlingUtilsC12executeAsync9operation7context7handler11autoRecoverxSgxyYaKXE_SSAA0cD0_pSbtYalFZ", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA5_", "_$s11AugmentCore24LargeFileOperationResultVMa", "_$s11AugmentCore0A6LoggerC17setConsoleLogging7enabledySb_tFTq", "_$s11AugmentCore19MemoryPressureLevelOSYAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC6logger18performanceMonitor13memoryManager6configAcA0A6LoggerC_AA011PerformanceH0CAA06MemoryJ0CSgAC0D6ConfigVtcfCTq", "_$s11AugmentCore15SnapshotManagerCMu", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOMa", "_$s11AugmentCore12SearchEngineC10removeFile8filePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfcfA_", "_$s11AugmentCore21SecurityAuditReporterCfd", "_$s11AugmentCore19DependencyContainerC13memoryManagerAA06MemoryF0CyFTj", "_$s11AugmentCore8FileItemV1loiySbAC_ACtFZ", "_$s11AugmentCore25MemoryOptimizationServiceCMa", "_$s11AugmentCore13ErrorHandlingP06reportC0_7contextys0C0_p_SSSgtFTq", "_$s11AugmentCore15PreviewFileTypeON", "_$s11AugmentCore13StorageConfigV24defaultMaxVersionAgeDaysSivs", "_$s11AugmentCore15MetadataManagerC017deleteFileVersionC07version9spacePathSbAA0fG0V_10Foundation3URLVtFTj", "_$s11AugmentCore15SecurityManagerC06removeC14ScopedBookmark3fory10Foundation3URLV_tFTq", "_$s11AugmentCore13LoggingConfigV04fileC7EnabledSbvM", "_$s11AugmentCore19DependencyContainerC20errorRecoveryManagerAA05ErrorfG0CyFTq", "_$s11AugmentCore17FileSystemMonitorC20startMonitoringAsync9spacePath8callbackSb10Foundation3URLV_yAI_AA0cD5EventOtYactFTq", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfcfA_", "_$s11AugmentCore0A5ErrorO13isRecoverableSbvg", "_$s11AugmentCore18LargeFileOperationO8rawValueSSvg", "_$s11AugmentCore11FileVersionV10CodingKeysOMa", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvgTj", "_$s11AugmentCore13NetworkConfigV13maxUploadSizes5Int64VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusON", "_$s11AugmentCore17FileSystemMonitorC26isAutoVersioningSuppressed3forSb10Foundation3URLV_tFTq", "_$s11AugmentCore13MemoryManagerC22forceGarbageCollectionyyFTq", "_$s11AugmentCore26FileSystemMonitoringConfigV26throttlingCleanupThresholdSivM", "_$s11AugmentCore16SnapshotScheduleV04lastC9Timestamp10Foundation4DateVSgvs", "_$s11AugmentCore11FileVersionVs12IdentifiableAAMc", "_$s11AugmentCore12ExportFormatO4jsonyA2CmFWC", "_$s11AugmentCore16RecoveryStrategyOSeAAMc", "_$s11AugmentCore18SecurityAuditEntryV9processIds5Int32VvpMV", "_$s11AugmentCore13NetworkConfigV10retryDelaySdvg", "_$s11AugmentCore12ConflictTypeO06deleteC0yA2CmFWC", "_$s11AugmentCore18ErrorHandlingUtilsCMu", "_$s11AugmentCore10DiffEngineCfD", "_$s11AugmentCore23PolicyEnforcementResultO7skippedyACSScACmFWC", "_$s11AugmentCore13SyncFrequencyON", "_$s11AugmentCore13DiffOperationV0D4TypeOSHAAMc", "_$s11AugmentCore13MemoryManagerC08optimizeC5UsageyyFTq", "_$s11AugmentCore14SecurityConfigV17encryptionEnabledSbvs", "_$s11AugmentCore12FileConflictVMn", "_$s11AugmentCore12SearchResultV7versionAA11FileVersionVvg", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA4_", "_$s11AugmentCore12SearchConfigV10maxResultsSivM", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8rawValueSSvg", "_$s11AugmentCore11FileVersionVSeAAMc", "_$s11AugmentCore19BackupConfigurationV11isEncryptedSbvg", "_$s11AugmentCore0A6LoggerC11LogCategoryOMn", "_$s11AugmentCore11FileVersionV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8rawValueSSvpMV", "_$s11AugmentCore25MemoryOptimizationServiceC03getD7HistorySayAC0D5StatsVGyFTj", "_$s11AugmentCore13StoragePolicyV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvsTq", "_$s11AugmentCore19UserInterfaceConfigVACycfC", "_$s11AugmentCore14StorageManagerC03setC6Policy_3foryAA0cF0V_10Foundation4UUIDVtFTj", "_$s11AugmentCore12ErrorMetricsCMu", "_$s11AugmentCore13StoragePolicyV4typeAC0D4TypeOvpMV", "_$s11AugmentCore27BackgroundProcessingServiceCMo", "_$s11AugmentCore17SecurityViolationV8SeverityO8rawValueSSvg", "_$s11AugmentCore19DependencyContainerC20errorRecoveryManagerAA05ErrorfG0CyFTj", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvpMV", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvgTq", "_$s11AugmentCore13SpaceSettingsV24storageManagementEnabledSbvs", "_$s11AugmentCore13BackupManagerCMn", "_$s11AugmentCore8SnapshotV5filesSayAA0C4FileVGvs", "_$s11AugmentCore17PerformanceConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore14DataEncryptionC15decryptToStringySS10Foundation0C0VKFZ", "_$s11AugmentCore19DependencyContainerC6inject16largeFileHandleryAA05LargegH0C_tFTj", "_$s11AugmentCore15FileSystemEventOSQAAMc", "_$s11AugmentCore18ErrorHandlingUtilsCMa", "_$s11AugmentCore23SimpleSyncConfigurationV9authTokenSSvpMV", "_$s11AugmentCore22SecurityRecommendationV8PriorityO6mediumyA2EmFWC", "_$s11AugmentCore16RecoveryStrategyO18requestPermissionsyA2CmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV012memoryBeforeD0s6UInt64Vvg", "_$s11AugmentCore17FileSystemMonitorC6sharedACvpZMV", "_$s11AugmentCore19UserInterfaceConfigVN", "_$s11AugmentCore30DefaultVersionCreationDelegateCN", "_$s11AugmentCore13StorageConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore10DiffEngineCfd", "_$s11AugmentCore12SearchEngineC6sharedACvpZMV", "_$s11AugmentCore8SnapshotV10CodingKeysOs0D3KeyAAMc", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvgTj", "_$s11AugmentCore13StorageConfigV23defaultWarningThresholdSdvg", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8indexingyA2EmFWC", "_$s11AugmentCore13SyncFrequencyO6manualyA2CmFWC", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvgTj", "_$s11AugmentCore13HashAlgorithmOSQAAMc", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvMTq", "_$s11AugmentCore26FileSystemMonitoringConfigV15fsEventsLatencySdvg", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvsTj", "_$s11AugmentCore8FileDiffV8diffData10Foundation0F0Vvg", "_$s11AugmentCore8FileItemV9isSyncingSbvs", "_$s11AugmentCore14StorageManagerC6logger06memoryD0AcA0A6LoggerC_AA06MemoryD0CSgtcfCTq", "_$s11AugmentCore13FileOperationOs12CaseIterableAAMc", "_$s11AugmentCore18PerformanceMonitorCMu", "_$s11AugmentCore17PerformanceConfigV28responseTimeWarningThresholdSdvs", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvsTj", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOSYAAMc", "_$s11AugmentCore15BackupFrequencyOSeAAMc", "_$s11AugmentCore8DiffTypeO8rawValueSSvpMV", "_$s11AugmentCore13SpaceSettingsV13autoSnapshotsSbvpMV", "_$s11AugmentCore13CleanupResultV12isSuccessfulSbvg", "_$s11AugmentCore11StorageInfoV7summarySSvg", "_$s11AugmentCore12ErrorMetricsCMm", "_$s11AugmentCore13StoragePolicyV0D4TypeON", "_$s11AugmentCore17SecurityViolationV8SeverityOSYAAMc", "_$s11AugmentCore19SecurityAuditReportV18securityViolationsSayAA0C9ViolationVGvg", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8rawValueSivg", "_$s11AugmentCore19MemoryUsageSnapshotV08physicalC5Totals6UInt64VvpMV", "_$s11AugmentCore8DiffTypeOSHAAMc", "_$s11AugmentCore12FileConflictV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore13SpaceSettingsV22snapshotFrequencyHoursSivg", "_$s11AugmentCore16RecoveryStrategyO18resetConfigurationyA2CmFWC", "_$s11AugmentCore13ErrorSeverityOs12CaseIterableAAMc", "_$s11AugmentCore13SecurityEventO15bookmarkRemovedyACSS_tcACmFWC", "_$s11AugmentCore0A6LoggerC8LogLevelOMa", "_$s11AugmentCore13StorageConfigV27autoCleanupEnabledByDefaultSbvs", "_$s11AugmentCore22SecurityRecommendationV8categorySSvg", "_$s11AugmentCore17ErrorHistoryEntryV10isResolvedSbvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvgTj", "_$s11AugmentCore12SnapshotFileV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore12SearchEngineCMm", "_$s11AugmentCore14DataEncryptionC0D5ErrorO16keyStorageFailedyA2EmFWC", "_$s11AugmentCore16RecoverableErrorV2id08originalD08category7context9timestamp18recoveryStrategies11userMessage16technicalDetailsACSS_s0D0_pAA0D8CategoryOSSSg10Foundation4DateVSayAA16RecoveryStrategyOGS2StcfC", "_$s11AugmentCore13SpaceSettingsV24storageManagementEnabledSbvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV18commandSTimeWindowSdvg", "_$s11AugmentCore8SnapshotV9timestamp10Foundation4DateVvg", "_$s11AugmentCore10FileHasherP6update4datay10Foundation4DataV_tFTq", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA_", "_$s11AugmentCore11FileVersionV7commentSSSgvg", "_$s11AugmentCore8FileItemV12versionCountSivpMV", "_$s11AugmentCore8FileTypeOs12CaseIterableAAMc", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV9diskUsages6UInt64VvpMV", "_$s11AugmentCore14StorageManagerC14stopMonitoringyyFTq", "_$s11AugmentCore12SearchEngineC10indexSpace9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore24LargeFileOperationResultV8fileSizes5Int64VvpMV", "_$s11AugmentCore18PreferencesManagerCfd", "_$s11AugmentCore22FileSystemMonitorErrorOMa", "_$s11AugmentCore14VersionControlC07restoreC08filePath7version7commentSb10Foundation3URLV_AA04FileC0VSSSgtFTj", "_$s11AugmentCore13SpaceSettingsV17syncConfigurationAA010SimpleSyncF0VvpMV", "_$s11AugmentCore22SecurityRecommendationV11descriptionSSvpMV", "_$s11AugmentCore15SnapshotManagerC6sharedACvpZMV", "_$s11AugmentCore0A5ErrorO13failureReasonSSSgvg", "_$s11AugmentCore12ExportFormatOMn", "_$s11AugmentCore13SpaceSettingsVSHAAMc", "_$s11AugmentCore0A6LoggerC12closeLogFileyyFTq", "_$s11AugmentCore14VersioningModeOSEAAMc", "_$s11AugmentCore19NotificationManagerC22getAuthorizationStatus10completionyySo015UNAuthorizationG0Vc_tFTq", "_$s11AugmentCore15PreviewFileTypeO9hashValueSivpMV", "_$s11AugmentCore0A5SpaceV12isMonitoringSbvs", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore24SpaceFileSystemProvidingTL", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvsTj", "_$s11AugmentCore13FolderVersionV9timestamp10Foundation4DateVvg", "_$s11AugmentCore30DefaultVersionCreationDelegateCMu", "_$s11AugmentCore13ErrorCategoryO8allCasesSayACGvgZ", "_$s11AugmentCore19UserInterfaceConfigVMn", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC6statusAE0F6StatusOvgTq", "_$s11AugmentCore8DiffTypeON", "_$s11AugmentCore19UserInterfaceConfigV15minWindowHeightSdvs", "_$s11AugmentCore19DependencyContainerC6inject15conflictManageryAA08ConflictG0C_tFTq", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvpMV", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA2_", "_$s11AugmentCore19BackupConfigurationV11isEncryptedSbvpMV", "_$s11AugmentCore16LargeFileHandlerC09calculateD4Hash3for9algorithm08progressE0010completionE0y10Foundation3URLV_AA0G9AlgorithmOySd_SStcys6ResultOySSAA0A5ErrorOGctFTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV07minimumC10SizeChanges5Int64Vvg", "_$s11AugmentCore22ConflictResolutionTypeO8keepBothyA2CmFWC", "_$s11AugmentCore19UserInterfaceConfigV15refreshIntervalSdvg", "_$s11AugmentCore20ErrorRecoveryManagerC6sharedACvpZMV", "_$s11AugmentCore13SyncDirectionOSYAAMc", "_$s11AugmentCore19DependencyContainerC6inject25memoryOptimizationServiceyAA06MemorygH0C_tFTq", "_$s11AugmentCore0A6LoggerC11LogCategoryOs12CaseIterableAAMc", "_$s11AugmentCore22SecurityRecommendationV8PriorityOMn", "_$s11AugmentCore19DependencyContainerC6inject16largeFileHandleryAA05LargegH0C_tFTq", "_$s11AugmentCore11FileVersionV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvsTj", "_$s11AugmentCore19NotificationManagerC22cancelAllNotificationsyyFTj", "_$s11AugmentCore12ExportFormatO8rawValueSSvg", "_$s11AugmentCore13MemoryManagerCfd", "_$s11AugmentCore13FileOperationO5writeyA2CmFWC", "_$s11AugmentCore13SpaceSettingsV15excludePatternsSaySSGvM", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvgTq", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvsTj", "_$s11AugmentCore15SecurityManagerCfd", "_$s11AugmentCore12SearchEngineCfd", "_$s11AugmentCore14RiskAssessmentV04highC6EventsSivg", "_$s11AugmentCore23SimpleSyncConfigurationVSHAAMc", "_$s11AugmentCore14VersionControlCMn", "_$s11AugmentCore16RecoverableErrorVSEAAMc", "_$s11AugmentCore12SearchConfigV10maxResultsSivpMV", "_$s11AugmentCore20ErrorRecoveryManagerC13configuration011preferencesE06loggerAcA0A13ConfigurationC_AA011PreferencesE0CAA0A6LoggerCtcfCTj", "_$s11AugmentCore17FileSystemMonitorC15startMonitoring9spacePath8callbackSb10Foundation3URLV_yAI_AA0cD5EventOtctFTj", "_$s11AugmentCore17PerformanceConfigVMn", "_$s11AugmentCore15SnapshotManagerC06createC09spacePath4name11descriptionAA0C0VSg10Foundation3URLV_S2SSgtFTj", "_$s11AugmentCore13NetworkConfigV16maxRetryAttemptsSivpMV", "_$s11AugmentCore0A13ConfigurationC04loadC0yyFTj", "_$s11AugmentCore12FileConflictV8filePath12conflictType12localVersion06remoteJ0AC10Foundation3URLV_AA0dH0OAA0cJ0VANtcfC", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA5_", "_$s11AugmentCore19DependencyContainerC18preferencesManagerAA011PreferencesF0CyFTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4typeAC0F4TypeOvg", "_$s11AugmentCore15SecurityManagerCN", "_$s11AugmentCore11NetworkSyncC9syncSpace9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvsTq", "_$s11AugmentCore10FileHasherP8finalizeSSyFTq", "_$s11AugmentCore22SecurityRecommendationV5titleSSvg", "_$s11AugmentCore14VersioningModeO6manualyA2CmFWC", "_$s11AugmentCore15MetadataManagerC013deleteVersionC07version9spacePathSbAA06FolderF0V_10Foundation3URLVtFTj", "_$s11AugmentCore8SnapshotV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore13MemoryManagerC010getCurrentC5UsageAA0cG8SnapshotVyFTj", "_$s11AugmentCore14StorageManagerC14stopMonitoringyyFTj", "_$s11AugmentCore9RiskLevelO8rawValueSSvpMV", "_$s11AugmentCore0A6LoggerC8LogLevelO02osD4TypeSo0F11_log_type_tavg", "_$s11AugmentCore14SecurityConfigV27fileAccessValidationEnabledSbvM", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO15defaultPriorityAC0fI0OvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC06handleC0_7context11autoRecoverys0C0_p_SSSgSbtFTj", "_$s11AugmentCore13SpaceSettingsV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore11StorageInfoV13newestVersion10Foundation4DateVvg", "_$s11AugmentCore12SearchResultV9relevanceSdvpMV", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore18PerformanceMonitorC22getCompletedOperations5limitSayAC16OperationMetricsVGSiSg_tFTq", "_$s11AugmentCore12StorageIssueO13limitExceededyA2CmFWC", "_$s11AugmentCore16RecoverableErrorV8categoryAA0D8CategoryOvpMV", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvpMV", "_$s11AugmentCore0A6LoggerC5error_7message8category4file8function4lineys5Error_p_SSSgAC11LogCategoryOS2SSitFTq", "_$s11AugmentCore17PerformanceConfigV18monitoringIntervalSdvs", "_$s11AugmentCore19UserInterfaceConfigV15refreshIntervalSdvpMV", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA8_", "_$s11AugmentCore15BackupFrequencyO8rawValueACSgSS_tcfC", "_$s11AugmentCore20ErrorRecoveryManagerC13configuration011preferencesE06loggerAcA0A13ConfigurationC_AA011PreferencesE0CAA0A6LoggerCtcfC", "_$s11AugmentCore14SecurityConfigV26securityEventRetentionDaysSivpMV", "_$s11AugmentCore18ConflictResolutionO8allCasesSayACGvpZMV", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidthSdvg", "_$s11AugmentCore11NetworkSyncC17getConfigurationsSayAA0D13ConfigurationVGyFTj", "_$s11AugmentCore8FileItemV4typeAA0C4TypeOvs", "_$s11AugmentCore15BackupRetentionOSeAAMc", "_$s11AugmentCore22SecurityRecommendationV8PriorityOSYAAMc", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivMTq", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOSQAAMc", "_$s11AugmentCore30DefaultVersionCreationDelegateCMm", "_$s11AugmentCore13DiffOperationV0D4TypeO7removedyA2EmFWC", "_$s11AugmentCore13MemoryManagerC15startMonitoringyyFTj", "_$s11AugmentCore20DependencyInjectableMp", "_$s11AugmentCore15SecurityManagerC6loggerAcA0A6LoggerC_tcfCTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV015enableAutomaticD0SbvpMV", "_$s11AugmentCore0A13ConfigurationC08validateC0SayAA0C5ErrorOGyFTq", "_$s11AugmentCore13PreviewEngineC6sharedACvgZ", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivgTj", "_$s11AugmentCore13SecurityEventO16fileAccessDeniedyACSS_S2StcACmFWC", "_$s11AugmentCore15ConflictManagerC18getActiveConflictsSayAA04FileC0VGyFTq", "_$s11AugmentCore13PreviewEngineCfD", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV04saveG7MethodsSayAC0fG6MethodOGvs", "_$s11AugmentCore13ErrorSeverityO8rawValueACSgSS_tcfC", "_$s11AugmentCore18PerformanceMonitorC15startMonitoring8intervalySd_tFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV12tasksInQueueSivpMV", "_$s11AugmentCore17FileSystemMonitorC22suppressAutoVersioning3fory10Foundation3URLV_tFTj", "_$s11AugmentCore8FileTypeOSYAAMc", "_$s11AugmentCore13SpaceSettingsV17syncConfigurationAA010SimpleSyncF0Vvg", "_$s11AugmentCore12SnapshotFileVN", "_$s11AugmentCore17PerformanceConfigV03maxC11HistorySizeSivM", "_$s11AugmentCore13SpaceSettingsV4hash4intoys6HasherVz_tF", "_$s11AugmentCore12ExportFormatO8rawValueSSvpMV", "_$s11AugmentCore11FileItemRowV4bodyQrvpQOMQ", "_$s11AugmentCore19BackupConfigurationV2id9spacePath06backupG09frequency9retention11isEncrypted8password0K7Enabled04lastC9TimestampAC10Foundation4UUIDV_AM3URLVAqA0C9FrequencyOAA0C9RetentionOSbSSSgSbAM4DateVSgtcfC", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvgTq", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvMTq", "_$s11AugmentCore16RecoveryStrategyO8allCasesSayACGvpZMV", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC11cancelTasks6ofTypeSiAC04TaskI0O_tFTq", "_$s11AugmentCore13MemoryManagerCMu", "_$s11AugmentCore15FileSystemEventO9hashValueSivpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV22enableProgressTrackingSbvpMV", "_$s11AugmentCore9RiskLevelOSeAAMc", "_$s11AugmentCore16SnapshotScheduleVN", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore12SearchEngineCACycfC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC7executeyyFTq", "_$s11AugmentCore13LoggingConfigVSEAAMc", "_$s11AugmentCore17SnapshotRetentionO11keepLimitedyACSicACmFWC", "_$s11AugmentCore22ConflictResolutionTypeOSQAAMc", "_$s11AugmentCore12ErrorMetricsC15getRecentErrors5limitSay10Foundation4DateV_AA0aC0OtGSi_tF", "_$s11AugmentCore12ErrorMetricsCfd", "_$s11AugmentCore11NetworkSyncCMa", "_$s11AugmentCore0A5ErrorOs0C0AAMc", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvgTj", "_$s11AugmentCore19NotificationManagerC21sendCleanupCompletion9spaceName15removedVersions10freedBytesySS_Sis5Int64VtFTj", "_$s11AugmentCore16RecoverableErrorV7contextSSSgvpMV", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvpMV", "_$s11AugmentCore12SearchEngineC9indexFile8filePath7versionSb10Foundation3URLV_AA0F7VersionVtFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC7executeyyFTj", "_$s11AugmentCore12ExportFormatO3csvyA2CmFWC", "_$s11AugmentCore13StoragePolicyV0D4TypeO6encode2toys7Encoder_p_tKF", "_$s11AugmentCore16RecoverableErrorVMa", "_$s11AugmentCore13FolderVersionVN", "_$s11AugmentCore18SecurityAuditEntryV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13ErrorCategoryO8rawValueSSvpMV", "_$s11AugmentCore23SimpleSyncConfigurationV20syncFrequencyMinutesSivs", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsVMn", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV11tasksByTypeSDyAC04TaskI0OSiGvg", "_$s11AugmentCore18ErrorHandlingUtilsC13createContext9component9operation14additionalInfoS2S_SSSDySSypGtFZ", "_$s11AugmentCore17SnapshotFrequencyO8rawValueACSgSS_tcfC", "_$s11AugmentCore13StorageConfigV28defaultCleanupFrequencyHoursSivs", "_$s11AugmentCore14RiskAssessmentV11riskFactorsSaySSGvpMV", "_$s11AugmentCore13StorageConfigV23defaultWarningThresholdSdvs", "_$s11AugmentCore13HashAlgorithmO9hashValueSivpMV", "_$s11AugmentCore22ConflictResolutionTypeO9keepLocalyA2CmFWC", "_$s11AugmentCore13BackupManagerC17getConfigurationsSayAA0C13ConfigurationVGyFTj", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOs12CaseIterableAAMc", "_$s11AugmentCore0A5ErrorO20authenticationFailedyACSS_tcACmFWC", "_$s11AugmentCore16SnapshotScheduleV2id9spacePath9frequency9retention9isEnabled04lastC9TimestampAC10Foundation4UUIDV_AJ3URLVAA0C9FrequencyOAA0C9RetentionOSbAJ4DateVSgtcfC", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA1_", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvMTq", "_$s11AugmentCore15SnapshotManagerC17scheduleSnapshots9spacePath0E0Sb10Foundation3URLV_AA0C8ScheduleVtFTq", "_$s11AugmentCore12ExportFormatOMa", "_$s11AugmentCore18PerformanceMonitorC22getCompletedOperations5limitSayAC16OperationMetricsVGSiSg_tFTj", "_$s11AugmentCore8FileItemV13fileExtensionSSvpMV", "_$s11AugmentCore19BackupConfigurationV9retentionAA0C9RetentionOvpMV", "_$s11AugmentCore8SnapshotV11descriptionSSSgvpMV", "_$s11AugmentCore14VersionControlC010createFileC08filePath7commentAA0fC0VSg10Foundation3URLV_SSSgtFTq", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOSHAAMc", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO11contentHashyA2EmFWC", "_$s11AugmentCore0A5ErrorO8categoryAA0C8CategoryOvg", "_$s11AugmentCore14SecurityConfigVACycfC", "_$s11AugmentCore15SnapshotManagerC06deleteC08snapshotSbAA0C0V_tFTj", "_$s11AugmentCore25MemoryOptimizationServiceC010unregisterD7Handler10identifierySS_tFTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsVMa", "_$s11AugmentCore13CleanupResultV15removedVersionsSivpMV", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvsTj", "_$s11AugmentCore8DiffTypeO4noneyA2CmFWC", "_$s11AugmentCore13SpaceSettingsV21cleanupFrequencyHoursSivM", "_$s11AugmentCore13MemoryManagerC03getC12UsageHistorySayAA0cF8SnapshotVGyFTj", "_$s11AugmentCore18SecurityAuditEntryV9timestamp5event6userId07processI0ACSS_AA0C5EventOSSs5Int32VtcfC", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA5_", "_$s11AugmentCore18PerformanceMonitorC14stopMonitoringyyFTq", "_$s11AugmentCore14VersionControlCMo", "_$s11AugmentCore19DependencyContainerC6inject12searchEngineyAA06SearchG0C_tFTq", "_$s11AugmentCore20DependencyInjectablePAAE12dependenciesAA0C9ContainerCvpMV", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvMTq", "_$s11AugmentCore17FileSystemMonitorC6loggerAA0A6LoggerCvg", "_$s11AugmentCore15ConflictManagerC03addC0yyAA04FileC0VFTq", "_$s11AugmentCore17CancellationErrorVACycfC", "_$s11AugmentCore0A5SpaceV2id4name4path11createdDate12isMonitoringAC10Foundation4UUIDV_SSAI3URLVAI0H0VSbtcfC", "_$s11AugmentCore8FileItemV16modificationDate10Foundation0F0Vvs", "_$s11AugmentCore17SyncConfigurationV2eeoiySbAC_ACtFZ", "_$s11AugmentCore14SecurityConfigVN", "_$s11AugmentCore13SpaceSettingsV13autoSnapshotsSbvg", "_$s11AugmentCore0A6LoggerC11LogCategoryO7generalyA2EmFWC", "_$s11AugmentCore12SearchEngineCMa", "_$s11AugmentCore13ErrorSeverityOMn", "_$s11AugmentCore13DiffOperationVMa", "_$s11AugmentCore18ConfigurationErrorOMa", "_$s11AugmentCore19MemoryUsageSnapshotVSeAAMc", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore11NetworkSyncC16getConfiguration9spacePathAA0dF0VSg10Foundation3URLV_tFTq", "_$s11AugmentCore12SearchConfigV18relevanceThresholdSdvM", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvpMV", "_$s11AugmentCore18ConfigurationErrorO16validationFailedyACSScACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC10submitTask4name4type8priority8workItemxSS_AC0G4TypeOAC0G8PriorityOSgxyYaKctYaKlFTjTu", "_$s11AugmentCore15FileSystemEventOSHAAMc", "_$s11AugmentCore26FileSystemMonitoringConfigV15fsEventsLatencySdvM", "_$s11AugmentCore0A13ConfigurationC16maxSearchResultsSivg", "_$s11AugmentCore12ConflictTypeOMn", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore12FileConflictV12localVersionAA0cF0Vvg", "_$s11AugmentCore23SimpleSyncConfigurationV20syncFrequencyMinutesSivM", "_$s11AugmentCore13ErrorCategoryOMa", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8rawValueSivpMV", "_$s11AugmentCore15PreviewFileTypeOMa", "_$s11AugmentCore8FileTypeOSQAAMc", "_$s11AugmentCore0A6LoggerC16measureTimeAsync_8category4file8function4line5blockxSS_AC11LogCategoryOS2SSixyYaKXEtYaKlFTjTu", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabledSbvM", "_$s11AugmentCore11FileVersionV10CodingKeysOs0E3KeyAAMc", "_$s11AugmentCore24LargeFileOperationResultVMn", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO2eeoiySbAE_AEtFZ", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfcfA3_", "_$s11AugmentCore12SearchEngineC10indexSpace9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore0A6LoggerC11measureTime_8category4file8function4line5blockxSS_AC11LogCategoryOS2SSixyKXEtKlFTj", "_$s11AugmentCore17SyncConfigurationVN", "_$s11AugmentCore19SecurityAuditReportV11totalEventsSivg", "_$s11AugmentCore15SnapshotManagerC14removeSchedule9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore20ErrorRecoveryManagerCfD", "_$s11AugmentCore13DiffOperationVSeAAMc", "_$s11AugmentCore18SecurityAuditEntryV9processIds5Int32Vvg", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvsTq", "_$s11AugmentCore27BackgroundProcessingServiceC6logger18performanceMonitor13memoryManager6configAcA0A6LoggerC_AA011PerformanceH0CAA06MemoryJ0CSgAC0D6ConfigVtcfc", "_$s11AugmentCore16SHA256FileHasherVAA0dE0AAMc", "_$s11AugmentCore13SpaceSettingsV24storageManagementEnabledSbvg", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV12tasksInQueueSivg", "_$s11AugmentCore0A6LoggerC16measureTimeAsync_8category4file8function4line5blockxSS_AC11LogCategoryOS2SSixyYaKXEtYaKlFTj", "_$s11AugmentCore19NotificationManagerC6sharedACvgZ", "_$s11AugmentCore25MemoryOptimizationServiceC23getCurrentConfigurationAC0D6ConfigVyFTj", "_$s11AugmentCore13SpaceSettingsV14versioningModeAA010VersioningF0OvM", "_$s11AugmentCore13SpaceSettingsVN", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvsTq", "_$s11AugmentCore19NotificationManagerC04userC6Center_11willPresent21withCompletionHandlerySo06UNUsercF0C_So14UNNotificationCySo0M19PresentationOptionsVctF", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvgTj", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOSHAAMc", "_$s11AugmentCore19SecurityAuditReportV14riskAssessmentAA04RiskG0Vvg", "_$s11AugmentCore14VersioningModeOSQAAMc", "_$s11AugmentCore12FileConflictV21localModificationDate10Foundation0G0VvpMV", "_$s11AugmentCore19UserInterfaceConfigV15refreshIntervalSdvM", "_$s11AugmentCore13FolderVersionV11storagePath10Foundation3URLVvpMV", "_$s11AugmentCore15PreviewFileTypeO5imageyA2CmFWC", "_$s11AugmentCore16SnapshotScheduleV9frequencyAA0C9FrequencyOvg", "_$s11AugmentCore0A5SpaceV4path10Foundation3URLVvpMV", "_$s11AugmentCore0A6LoggerC11LogCategoryO8allCasesSayAEGvgZ", "_$s11AugmentCore11FileVersionV4sizes6UInt64VvpMV", "_$s11AugmentCore13SyncFrequencyOSQAAMc", "_$s11AugmentCore16RecoveryStrategyOMn", "_$s11AugmentCore19UserInterfaceConfigV17animationDurationSdvpMV", "_$s11AugmentCore16SnapshotScheduleV9isEnabledSbvM", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvgTj", "_$s11AugmentCore13LoggingConfigV15defaultLogLevelSSvM", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore12SearchResultV4hash4intoys6HasherVz_tF", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV04saveG7MethodsSayAC0fG6MethodOGvg", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4nameSSvpMV", "_$s11AugmentCore17SnapshotSchedulerC14removeSchedule9spacePathy10Foundation3URLV_tFTj", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV16activeOperationsSivpMV", "_$s11AugmentCore19UserInterfaceConfigV26animationsEnabledByDefaultSbvg", "_$s11AugmentCore13SyncDirectionO13localToRemoteyA2CmFWC", "_$s11AugmentCore14VersionControlC010createFileC5Async8filePath7commentAA0fC0VSg10Foundation3URLV_SSSgtYaFTj", "_$s11AugmentCore0A5ErrorON", "_$s11AugmentCore19UserInterfaceConfigV17animationDurationSdvM", "_$s11AugmentCore13PreviewEngineCMm", "_$s11AugmentCore16RecoverableErrorVs12IdentifiableAAMc", "_$s11AugmentCore12SearchEngineCN", "_$s11AugmentCore13FileOperationO13requiresWriteSbvg", "_$s11AugmentCore17SecurityViolationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore12FileConflictV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13ErrorHandlingP6loggerAA0A6LoggerCvgTq", "_$s11AugmentCore11NetworkSyncC07performD013configurationyAA0D13ConfigurationV_tFTq", "_$s11AugmentCore13SpaceSettingsV27storageNotificationsEnabledSbvpMV", "_$s11AugmentCore16SHA256FileHasherVMn", "_$s11AugmentCore12SearchConfigV31contentIndexingEnabledByDefaultSbvg", "_$s11AugmentCore19UserInterfaceConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore11FileVersionV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV8durationSdvpMV", "_$s11AugmentCore13StoragePolicyV16warningThresholdSdvpMV", "_$s11AugmentCore19UserInterfaceConfigV19defaultWindowHeightSdvs", "_$s11AugmentCore0A5SpaceV8settingsAA0C8SettingsVvpMV", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGBSdvpMV", "_$s11AugmentCore23PolicyEnforcementResultO8enforcedyACSScACmFWC", "_$s11AugmentCore13DiffOperationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore0A6LoggerCMm", "_$s11AugmentCore13ErrorSeverityO8prioritySivpMV", "_$s11AugmentCore12ConflictTypeOSYAAMc", "_$s11AugmentCore0A5SpaceVMa", "_$s11AugmentCore26FileSystemMonitoringConfigV18maxMonitoredSpacesSivM", "_$s11AugmentCore15BackupFrequencyO5dailyyA2CmFWC", "_$s11AugmentCore11FileVersionV10CodingKeysOSHAAMc", "_$s11AugmentCore12ConflictTypeO010permissionC0yA2CmFWC", "_$s11AugmentCore13SpaceSettingsV21cleanupFrequencyHoursSivs", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore20ErrorRecoveryManagerCMa", "_$s11AugmentCore13ErrorHandlingP20errorRecoveryManagerAA0cfG0CvgTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4nameSSvg", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvMTj", "_$s11AugmentCore0A5SpaceV12isMonitoringSbvM", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvgTq", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivMTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCMo", "_$s11AugmentCore14VersionControlC06createC010folderPath7commentAA06FolderC0VSg10Foundation3URLV_SSSgtFTj", "_$s11AugmentCore0A6LoggerC11LogCategoryO8securityyA2EmFWC", "_$s11AugmentCore13SyncDirectionO8rawValueSSvpMV", "_$s11AugmentCore13SpaceSettingsV17maxVersionAgeDaysSivs", "_$s11AugmentCore14VersionControlC11getVersions8filePathSayAA04FileC0VG10Foundation3URLV_tFTq", "_$s11AugmentCore8FileItemV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore19SecurityAuditReportV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13FolderVersionV10folderPath10Foundation3URLVvpMV", "_$s11AugmentCore13ErrorHandlingP06reportC0_7contextys0C0_p_SSSgtFTj", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvsTj", "_$s11AugmentCore0A6LoggerC8LogLevelO8allCasesSayAEGvgZ", "_$s11AugmentCore19DependencyContainerCfD", "_$s11AugmentCore18PerformanceMonitorC12endOperation_7success12errorMessagey10Foundation4UUIDV_SbSSSgtFTq", "_$s11AugmentCore23SimpleSyncConfigurationVSeAAMc", "_$s11AugmentCore26FileSystemMonitoringConfigV18maxMonitoredSpacesSivpMV", "_$s11AugmentCore15FileSystemEventO7renamedyA2CmFWC", "_$s11AugmentCore18PerformanceMonitorC17getCurrentMetricsAC0cG0VSgyFTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV22enableContentHashCheckSbvs", "_$s11AugmentCore10DiffEngineCN", "_$s11AugmentCore12FileConflictV12localVersionAA0cF0VvpMV", "_$s11AugmentCore17CancellationErrorVMa", "_$s11AugmentCore16RecoveryStrategyO14retryOperationyA2CmFWC", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvpMV", "_$s11AugmentCore17FileSystemMonitorC19setDebounceIntervalyySdFTq", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvMTq", "_$s11AugmentCore18PerformanceMonitorC5resetyyFTq", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8allCasesSayAEGvpZMV", "_$s11AugmentCore13HashAlgorithmO12createHasherAA04FileF0_pyF", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvgTj", "_$s11AugmentCore15ErrorValidationC27validateStorageAvailability4path13requiredBytesy10Foundation3URLV_s5Int64VtKFZ", "_$s11AugmentCore8FileTypeO8rawValueSSvpMV", "_$s11AugmentCore22ConflictResolutionTypeO10keepRemoteyA2CmFWC", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore13BackupManagerC16createAllBackupsSbyFTj", "_$s11AugmentCore15ErrorValidationCMo", "_$s11AugmentCore13SpaceSettingsV27storageNotificationsEnabledSbvs", "_$s11AugmentCore27BackgroundProcessingServiceC10cancelTask6taskIdSb10Foundation4UUIDV_tFTj", "_$s11AugmentCore12SearchConfigVN", "_$s11AugmentCore12SnapshotFileVMa", "_$s11AugmentCore17PerformanceConfigV22memoryCleanupThresholdSivs", "_$s11AugmentCore12SearchEngineC6search5query10spacePathsSayAA0C6ResultVGSS_Say10Foundation3URLVGtFTq", "_$s11AugmentCore8FileDiffV11fromVersionAA0cF0VvpMV", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore19DependencyContainerCMn", "_$s11AugmentCore13SecurityEventON", "_$s11AugmentCore13StoragePolicyV0D4TypeOMn", "_$s11AugmentCore18ConflictResolutionO8allCasesSayACGvgZ", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvMTj", "_$s11AugmentCore0A6LoggerC13getLogFileURL10Foundation0G0VSgyFTj", "_$s11AugmentCore13ErrorHandlingPAAE06handleC0_7context11autoRecoverys0C0_p_SSSgSbtF", "_$s11AugmentCore8SnapshotV10CodingKeysOSQAAMc", "_$s11AugmentCore10DiffEngineCMa", "_$s11AugmentCore13BackupManagerC16getConfiguration9spacePathAA0cF0VSg10Foundation3URLV_tFTj", "_$s11AugmentCore13PreviewEngineC08generateC08filePath4sizeSo7NSImageCSg10Foundation3URLV_So6CGSizeVtFTj", "_$s11AugmentCore19DependencyContainerC5resetyyFTj", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfcfA0_", "_$s11AugmentCore17PerformanceConfigVMa", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfcfA1_", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvsTj", "_$s11AugmentCore12FileConflictV13remoteVersionAA0cF0Vvg", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvsTj", "_$s11AugmentCore13MemoryManagerCMa", "_$s11AugmentCore18LargeFileOperationOMn", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore16RecoverableErrorV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore22ConflictResolutionTypeO2eeoiySbAC_ACtFZ", "_$s11AugmentCore18PerformanceMonitorC03getC7History5limitSayAC0C7MetricsVGSiSg_tFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV18maxConcurrentTasks11taskTimeout17enableTaskHistory0gN4Size0L16ProgressTrackingAESDyAC0M8PriorityOSiG_SdSbSiSbtcfC", "_$s11AugmentCore15BackupFrequencyO8intervalSdvpMV", "_$s11AugmentCore12SearchConfigV18relevanceThresholdSdvg", "_$s11AugmentCore15ConflictManagerC18getActiveConflictsSayAA04FileC0VGyFTj", "_$s11AugmentCore17FileAccessSummaryV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore8SnapshotV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore15MetadataManagerCMo", "_$s11AugmentCore14StorageManagerCMm", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore15PreviewFileTypeO4hash4intoys6HasherVz_tF", "_$s11AugmentCore19MemoryUsageSnapshotV18trackedObjectCountSivpMV", "_$s11AugmentCore13CleanupResultVMn", "_$s11AugmentCore23SimpleSyncConfigurationV9serverURLSSvs", "_$s11AugmentCore19SecurityAuditReportV17fileAccessSummaryAA04FilegH0VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusOSQAAMc", "_$s11AugmentCore24LargeFileOperationResultV7successSbvpMV", "_$s11AugmentCore13ErrorSeverityO8prioritySivg", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvgTj", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA_", "_$s11AugmentCore23SimpleSyncConfigurationV04autoD0SbvpMV", "_$s11AugmentCore13LoggingConfigVMn", "_$s11AugmentCore12SearchEngineC10clearIndex9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvsTq", "_$s11AugmentCore19NotificationManagerCMn", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvsTq", "_$s11AugmentCore13BackupManagerCMo", "_$s11AugmentCore17PerformanceConfigV22memoryCleanupThresholdSivg", "_$s11AugmentCore17FileAccessSummaryV06deniedcD0Sivg", "_$s11AugmentCore13SyncFrequencyO6weeklyyA2CmFWC", "_$s11AugmentCore19DependencyContainerC27backgroundProcessingServiceAA010BackgroundfG0CyFTj", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvgTq", "_$s11AugmentCore19UserInterfaceConfigV14minWindowWidthSdvg", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvpMV", "_$s11AugmentCore12SearchEngineC15indexSpaceAsync9spacePathSb10Foundation3URLV_tYaFTjTu", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV16activeOperationsSivg", "_$s11AugmentCore13CleanupResultV7summarySSvpMV", "_$s11AugmentCore14StorageManagerCMo", "_$s11AugmentCore13StorageConfigVN", "_$s11AugmentCore15MetadataManagerC015loadFileVersionC08filePath05spaceI0SayAA0fG0VG10Foundation3URLV_ALtFTq", "_$s11AugmentCore18ConfigurationErrorO07missingC0yACSScACmFWC", "_$s11AugmentCore23PolicyEnforcementResultOMa", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8durationSdSgvgTj", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvsTq", "_$s11AugmentCore15ConflictManagerCMm", "_$s11AugmentCore19DependencyContainerC13configurationAA0A13ConfigurationCyFTq", "_$s11AugmentCore18ErrorHandlingUtilsCACycfc", "_$s11AugmentCore19MemoryPressureLevelO8rawValueSSvg", "_$s11AugmentCore22SecurityRecommendationV8PriorityOSHAAMc", "_$s11AugmentCore19MemoryPressureLevelO8rawValueSSvpMV", "_$s11AugmentCore17FileSystemMonitorC13memoryManagerAA06MemoryG0Cvg", "_$s11AugmentCore8FileItemV2eeoiySbAC_ACtFZ", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyOMn", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvgTq", "_$s11AugmentCore16SnapshotScheduleV9frequencyAA0C9FrequencyOvpMV", "_$s11AugmentCore0A6LoggerCMu", "_$s11AugmentCore13MemoryManagerC22registerCleanupHandler10identifier7handlerySS_yyctFTj", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvMTq", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvgTj", "_$s11AugmentCore12SearchEngineC15indexSpaceAsync9spacePathSb10Foundation3URLV_tYaFTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV16handlersExecutedSivg", "_$s11AugmentCore13SpaceSettingsV22snapshotFrequencyHoursSivM", "_$s11AugmentCore30DefaultVersionCreationDelegateCAA0deF0AAMc", "_$s11AugmentCore30DefaultVersionCreationDelegateCACycfc", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvMTj", "_$s11AugmentCore13SecurityEventO23securityPolicyViolationyACSS_S2StcACmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC03getD7HistorySayAC0D5StatsVGyFTq", "_$s11AugmentCore14StorageManagerC6logger06memoryD0AcA0A6LoggerC_AA06MemoryD0CSgtcfc", "_$s11AugmentCore19BackupConfigurationVMa", "_$s11AugmentCore0A5SpaceV12isMonitoringSbvpMV", "_$s11AugmentCore8FileItemVSEAAMc", "_$s11AugmentCore18PreferencesManagerCMo", "_$s11AugmentCore12SearchEngineC11searchAsync5query10spacePathsSayAA0C6ResultVGSS_Say10Foundation3URLVGtYaFTjTu", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleIntervalSdvpMV", "_$s11AugmentCore18PerformanceMonitorCN", "_$s11AugmentCore9RiskLevelOMa", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvsTq", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutesSivg", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvMTq", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvgTq", "_$s11AugmentCore14RiskAssessmentV03lowC6EventsSivg", "_$s11AugmentCore12SearchResultVMa", "_$s11AugmentCore23SimpleSyncConfigurationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC6cancelyyFTj", "_$s11AugmentCore17SnapshotSchedulerCMa", "_$s11AugmentCore19BackupConfigurationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore14DataEncryptionC0D5ErrorON", "_$s11AugmentCore13ErrorSeverityOSYAAMc", "_$s11AugmentCore8SnapshotV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore19MemoryUsageSnapshotV08physicalC4Useds6UInt64Vvg", "_$s11AugmentCore22SecurityRecommendationV8PriorityO8rawValueAESgSS_tcfC", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvsTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC9startedAt10Foundation4DateVSgvgTj", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfC", "_$s11AugmentCore12SearchConfigV10maxResultsSivs", "_$s11AugmentCore17SnapshotSchedulerCACycfCTj", "_$s11AugmentCore17PerformanceConfigV21uiUpdatePauseIntervalSdvg", "_$s11AugmentCore12SnapshotFileV16modificationDate10Foundation0F0Vvg", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore19MemoryUsageSnapshotV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore22SecurityRecommendationV8PriorityO4highyA2EmFWC", "_$s11AugmentCore27ConflictResolutionUtilitiesCMm", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV25memoryRecoveredPercentageSdvpMV", "_$s11AugmentCore23SimpleSyncConfigurationVSQAAMc", "_$s11AugmentCore15ConflictManagerCfd", "_$s11AugmentCore21SecurityAuditReporterCfD", "_$s11AugmentCore23SimpleSyncConfigurationV9serverURLSSvM", "_$s11AugmentCore13SpaceSettingsV9hashValueSivpMV", "_$s11AugmentCore8FileItemV10systemIconSSvg", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvgTj", "_$s11AugmentCore13StoragePolicyV16warningThresholdSdvg", "_$s11AugmentCore11StorageInfoV17originalFilesSizes5Int64Vvg", "_$s11AugmentCore15PreviewFileTypeO9hashValueSivg", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvMTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC6cancelyyFTq", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvsTj", "_$s11AugmentCore17CancellationErrorVN", "_$s11AugmentCore19VersionControlErrorOs0E0AAMc", "_$s11AugmentCore13PreviewEngineCMn", "_$s11AugmentCore11NetworkSyncC10fileSystem15conflictManagerAcA09SpaceFileF9Providing_p_AA08ConflictH0CtcfC", "_$s11AugmentCore11FileItemRowV4fileAA0cD0VvpMV", "_$s11AugmentCore17FileSystemMonitorCMa", "_$s11AugmentCore13SpaceSettingsV21monitorSubdirectoriesSbvs", "_$s11AugmentCore19DependencyContainerC18performanceMonitorAA011PerformanceF0CyFTq", "_$s11AugmentCore19MemoryUsageSnapshotV06memoryD10PercentageSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV19totalTasksProcessedSivpMV", "_$s11AugmentCore18ConflictResolutionO8rawValueACSgSS_tcfC", "_$s11AugmentCore19NotificationManagerC18sendStorageWarning9spaceName10percentageySS_SdtFTj", "_$s11AugmentCore14StorageManagerC03getC6Policy3forAA0cF0V10Foundation4UUIDV_tFTj", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore20ErrorRecoveryManagerC7Combine16ObservableObjectAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV11successRateSdvpMV", "_$s11AugmentCore12ErrorMetricsC03getC10StatisticsSDySSSiGyF", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidthSdvs", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvMTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV04saveG7MethodsSayAC0fG6MethodOGvM", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvsTj", "_$s11AugmentCore19NotificationManagerC6loggerAcA0A6LoggerC_tcfc", "_$s11AugmentCore13LoggingConfigV15defaultLogLevelSSvs", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabledSbvpMV", "_$s11AugmentCore14DataEncryptionC0D5ErrorO16decryptionFailedyA2EmFWC", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV9timestamp10Foundation4DateVvg", "_$s11AugmentCore11FileVersionV4hash4intoys6HasherVz_tF", "_$s11AugmentCore19NotificationManagerC21sendCleanupCompletion9spaceName15removedVersions10freedBytesySS_Sis5Int64VtFTq", "_$s11AugmentCore15MetadataManagerC015saveFileVersionC07version9spacePathSbAA0fG0V_10Foundation3URLVtFTj", "_AugmentCoreVersionString", "_$s11AugmentCore16SHA256FileHasherV6update4datay10Foundation4DataV_tF", "_$s11AugmentCore0A5ErrorO16checksumMismatchyACSS_S2StcACmFWC", "_$s11AugmentCore16SHA256FileHasherVN", "_$s11AugmentCore25MemoryOptimizationServiceC013stopAutomaticD0yyFTj", "_$s11AugmentCore16RecoverableErrorVSeAAMc", "_$s11AugmentCore13ErrorHandlingP06handleC0_7context11autoRecoverys0C0_p_SSSgSbtFTj", "_$s11AugmentCore16RecoveryStrategyO18restartApplicationyA2CmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC23getCurrentConfigurationAC0D6ConfigVyFTq", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvgTq", "_$s11AugmentCore18PreferencesManagerCMa", "_$s11AugmentCore8FileTypeO11descriptionSSvg", "_$s11AugmentCore22ConflictResolutionTypeO9hashValueSivg", "_$s11AugmentCore19DependencyContainerC25memoryOptimizationServiceAA06MemoryfG0CyFTj", "_$s11AugmentCore11NetworkSyncCfd", "_$s11AugmentCore25MemoryOptimizationServiceC08registerD7Handler10identifier7handlerySS_yAC0D8StrategyOctFTj", "_$s11AugmentCore14VersioningModeOMn", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV23memoryPressureThresholdSdvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigV22defaultExcludePatternsSaySSGvg", "_$s11AugmentCore19NotificationManagerC19sendStorageCritical9spaceNameySS_tFTj", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvpMV", "_$s11AugmentCore0A5ErrorOs23CustomStringConvertibleAAMc", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvgTj", "_$s11AugmentCore13StorageConfigV28defaultCleanupFrequencyHoursSivg", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvMTj", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA1_", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOMn", "_$s11AugmentCore17SecurityViolationV9timestampSSvg", "_$s11AugmentCore14SecurityConfigVSeAAMc", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvgTj", "_$s11AugmentCore11NetworkSyncCN", "_$s11AugmentCore13SpaceSettingsV17maxVersionAgeDaysSivg", "_$s11AugmentCore14StorageManagerC07enforceC6Policy_3forAA0F17EnforcementResultOAA0cF0V_AA0A5SpaceVtFTj", "_$s11AugmentCore12SnapshotFileV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore8SnapshotV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore0A5ErrorO26notificationDeliveryFailedyACSS_tcACmFWC", "_$s11AugmentCore22SecurityRecommendationV6actionSSvg", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8progressSdvgTj", "_$s11AugmentCore11FileItemRowV7SwiftUI4ViewAAMc", "_$s11AugmentCore0A6LoggerC8critical_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTq", "_$s11AugmentCore17FileSystemMonitorC32unsuppressAutoVersioningForSpace9spacePathy10Foundation3URLV_tFTj", "_$s11AugmentCore18ErrorHandlingUtilsC12handleResult_7context7handler11autoRecoverxSgs0G0Oyxs0C0_pG_SSAA0cD0_pSbtlFZ", "_$s11AugmentCore12ExportFormatON", "_$s11AugmentCore19DependencyContainerC6inject15securityManageryAA08SecurityG0C_tFTq", "_$s11AugmentCore12ConflictTypeO07contentC0yA2CmFWC", "_$s11AugmentCore17SnapshotFrequencyO8rawValueSSvg", "_$s11AugmentCore25MemoryOptimizationServiceCfd", "_$s11AugmentCore12SnapshotFileV10CodingKeysON", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvMTj", "_$s11AugmentCore17SyncConfigurationV04lastC9Timestamp10Foundation4DateVSgvs", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11completedAt10Foundation4DateVSgvgTq", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV11isCompletedSbvg", "_$s11AugmentCore19UserInterfaceConfigVSeAAMc", "_$s11AugmentCore19DependencyContainerC6inject15metadataManageryAA08MetadataG0C_tFTq", "_$s11AugmentCore22SecurityRecommendationV8PriorityO8rawValueSSvg", "_$s11AugmentCore12SnapshotFileV2id8filePath08relativeG04size16modificationDateAC10Foundation4UUIDV_AI3URLVSSs6UInt64VAI0K0VtcfC", "_$s11AugmentCore19UserInterfaceConfigV19defaultWindowHeightSdvM", "_$s11AugmentCore15ErrorValidationCMa", "_$s11AugmentCore16RecoveryStrategyO8rawValueSSvpMV", "_$s11AugmentCore13ErrorCategoryO11permissionsyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV18maxConcurrentTasksSDyAC12TaskPriorityOSiGvg", "_$s11AugmentCore17FileSystemMonitorC32enableComprehensiveSaveDetectionyyF", "_$s11AugmentCore17SyncConfigurationVSeAAMc", "_$s11AugmentCore12FileConflictVSeAAMc", "_$s11AugmentCore14VersioningModeO8rawValueSSvg", "_$s11AugmentCore16RecoveryStrategyO11freeUpSpaceyA2CmFWC", "_$s11AugmentCore16SnapshotScheduleVSeAAMc", "_$s11AugmentCore13NetworkConfigV10retryDelaySdvM", "_$s11AugmentCore0A5ErrorO11descriptionSSvpMV", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvMTj", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore13FolderVersionV7commentSSSgvg", "_$s11AugmentCore14VersioningModeO8rawValueACSgSS_tcfC", "_$s11AugmentCore0A6LoggerC11LogCategoryON", "_$s11AugmentCore22FileSystemMonitorErrorON", "_$s11AugmentCore8FileItemV4pathSSvpMV", "_$s11AugmentCore14DataEncryptionCMm", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvpWvd", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore15BackupFrequencyOSQAAMc", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV04saveG7MethodsSayAC0fG6MethodOGvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC07dismissC6DialogyyFTj", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvpMV", "_$s11AugmentCore13ErrorSeverityO6mediumyA2CmFWC", "_$s11AugmentCore16RecoveryStrategyO12recreateFileyA2CmFWC", "_$s11AugmentCore13StorageConfigVACycfC", "_$s11AugmentCore19BackupConfigurationVMn", "_$s11AugmentCore13BackupManagerCMa", "_$s11AugmentCore18PerformanceMonitorC6sharedACvpZMV", "_$s11AugmentCore19SecurityAuditReportV11generatedAt10Foundation4DateVvg", "_$s11AugmentCore18SecurityAuditEntryVMn", "_$s11AugmentCore13ErrorCategoryO4iconSSvpMV", "_$s11AugmentCore12SearchResultVMn", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvMTq", "_$s11AugmentCore13SpaceSettingsV17maxVersionAgeDaysSivpMV", "_$s11AugmentCore27ConflictResolutionUtilitiesCMo", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvsTq", "_$s11AugmentCore11FileVersionV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore13FileOperationOSQAAMc", "_$s11AugmentCore26FileSystemMonitoringConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13NetworkConfigVSeAAMc", "_$s11AugmentCore0A13ConfigurationC7loggingAA13LoggingConfigVvpMV", "_$s11AugmentCore0A6LoggerC12closeLogFileyyFTj", "_$s11AugmentCore17SnapshotFrequencyO8rawValueSSvpMV", "_$s11AugmentCore0A6LoggerC5error_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTj", "_$s11AugmentCore14VersioningModeOSYAAMc", "_$s11AugmentCore15SnapshotManagerCMa", "_$s11AugmentCore15SnapshotManagerCMm", "_$s11AugmentCore15SnapshotManagerCfD", "_$s11AugmentCore11StorageInfoV9totalSizes5Int64VvpMV", "_$s11AugmentCore18PreferencesManagerC18$maxVersionAgeDays7Combine9PublishedV9PublisherVySi_GvMTj", "_$s11AugmentCore13CleanupResultV12isSuccessfulSbvpMV", "_$s11AugmentCore11StorageInfoV17originalFilesSizes5Int64VvpMV", "_$s11AugmentCore0A5ErrorO22performanceDegradationyACSS_S2dtcACmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV015enableAutomaticD0Sbvg", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvgTj", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV8durationSdSgvg", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCMu", "_$s11AugmentCore22FileSystemMonitorErrorO20localizedDescriptionSSvpMV", "_$s11AugmentCore8SnapshotV10CodingKeysOSHAAMc", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore12ErrorMetricsCMa", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCMm", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV8strategyAC0D8StrategyOvpMV", "_$s11AugmentCore12FileConflictV21localModificationDate10Foundation0G0Vvg", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvpMV", "_$s11AugmentCore13SpaceSettingsV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore0A5SpaceV8settingsAA0C8SettingsVvg", "_$s11AugmentCore17PerformanceConfigV13fileBatchSizeSivM", "_$s11AugmentCore8FileItemV4sizes5Int64VvM", "_$s11AugmentCore13SyncFrequencyO5dailyyA2CmFWC", "_$s11AugmentCore13DiffOperationV4typeAC0D4TypeOvg", "_$s11AugmentCore13FileOperationO8allCasesSayACGvpZMV", "_$s11AugmentCore19NotificationManagerC24sendErrorRecoverySuccess7errorIdySS_tFTj", "_$s11AugmentCore13StoragePolicyV0D4TypeOSeAAMc", "_$s11AugmentCore11StorageInfoV9totalSizes5Int64Vvg", "_$s11AugmentCore8DiffTypeO6binaryyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO9completedyA2GmFWC", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfC", "_$s11AugmentCore13FileOperationO6backupyA2CmFWC", "_$s11AugmentCore19UserInterfaceConfigV15minWindowHeightSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8priorityAC0F8PriorityOvpMV", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA1_", "_$s11AugmentCore13ErrorCategoryOSeAAMc", "_$s11AugmentCore13NetworkConfigV20syncEnabledByDefaultSbvg", "_$s11AugmentCore13NetworkConfigV16maxRetryAttemptsSivs", "_$s11AugmentCore0A13ConfigurationC15resetToDefaultsyyFTq", "_$s11AugmentCore19DependencyContainerC6sharedACvpZMV", "_$s11AugmentCore25MemoryOptimizationServiceC13memoryManager6logger18performanceMonitor6configAcA0cG0C_AA0A6LoggerCAA011PerformanceJ0CAC0D6ConfigVtcfCTj", "_$s11AugmentCore13LoggingConfigV04fileC7EnabledSbvg", "_$s11AugmentCore13ErrorCategoryOSQAAMc", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvMTq", "_$s11AugmentCore12SearchEngineCACycfCTq", "_$s11AugmentCore19BackupConfigurationV9isEnabledSbvpMV", "_$s11AugmentCore8LoggableP6loggerAA0A6LoggerCvgTj", "_$s11AugmentCore17SnapshotSchedulerC11addScheduleyyAA0cF0VF", "_$s11AugmentCore14SecurityConfigVMa", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvsTj", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvgTj", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvsTj", "_$s11AugmentCore8FileItemV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore24LargeFileOperationResultV9operationAA0cdE0OvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfcfA2_", "_$s11AugmentCore12SearchConfigV20indexRebuildIntervalSivs", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11isCompletedSbvpMV", "_$s11AugmentCore14VersionControlC15metadataManagerAcA08MetadataF0C_tcfc", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA3_", "_$s11AugmentCore15MetadataManagerCMu", "_$s11AugmentCore27ConflictResolutionUtilitiesCMn", "_$s11AugmentCore23PolicyEnforcementResultO9compliantyACSScACmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigVMa", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4modeSSvg", "_$s11AugmentCore19BackupConfigurationV9frequencyAA0C9FrequencyOvg", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8progressSdvpMV", "_$s11AugmentCore8SnapshotV10CodingKeysOMn", "_$s11AugmentCore11FileVersionV10CodingKeysOSYAAMc", "_$s11AugmentCore17PerformanceConfigV21uiUpdatePauseIntervalSdvM", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvMTj", "_$s11AugmentCore17SnapshotSchedulerC11getSchedule9spacePathAA0cF0VSg10Foundation3URLV_tF", "_$s11AugmentCore12SearchResultV2id8filePath7version5token7contextAC10Foundation4UUIDV_AI3URLVAA11FileVersionVS2StcfC", "_$s11AugmentCore18PreferencesManagerC06importC0yySDySSypGF", "_$s11AugmentCore0A13ConfigurationC26defaultStorageMaxSizeBytess5Int64VvpMV", "_$s11AugmentCore14StorageManagerC12getDiskUsage3forSDy10Foundation4UUIDVAA0C4InfoVGSayAA0A5SpaceVG_tFTq", "_$s11AugmentCore0A5ErrorO8severityAA0C8SeverityOvg", "_$s11AugmentCore14StorageManagerC12getDiskUsage3forSDy10Foundation4UUIDVAA0C4InfoVGSayAA0A5SpaceVG_tFTj", "_$s11AugmentCore16LargeFileHandlerCMu", "_$s11AugmentCore15ConflictManagerCMn", "_$s11AugmentCore17SyncConfigurationV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvgTq", "_$s11AugmentCore16RecoverableErrorV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore19UserInterfaceConfigV12maxListItemsSivg", "_$s11AugmentCore16RecoveryStrategyO8rawValueACSgSS_tcfC", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvMTq", "_$s11AugmentCore17SyncConfigurationV2id10Foundation4UUIDVvg", "_$s11AugmentCore26FileSystemMonitoringConfigV26throttlingCleanupThresholdSivg", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvsTj", "_$s11AugmentCore20DependencyInjectableP12dependenciesAA0C9ContainerCvgTq", "_$s11AugmentCore0A5ErrorO010diskAccessC0yACSS_s0C0_ptcACmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC9createdAt10Foundation4DateVvg", "_$s11AugmentCore17PerformanceConfigV22memoryCleanupThresholdSivpMV", "_$s11AugmentCore0A5ErrorO12fileNotFoundyACSS_tcACmFWC", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvpMV", "_$s11AugmentCore11FileVersionV4sizes6UInt64Vvg", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO11descriptionSSvg", "_$s11AugmentCore17SecurityViolationV8SeverityOMn", "_$s11AugmentCore13SpaceSettingsVSQAAMc", "_$s11AugmentCore8FileItemV12versionCountSivs", "_$s11AugmentCore15SecurityManagerC11getAuditLogSayAA0cF5EntryVGyFTj", "_$s11AugmentCore16LargeFileHandlerCMo", "_$s11AugmentCore18PerformanceMonitorC6loggerAcA0A6LoggerC_tcfc", "_$s11AugmentCore17SnapshotFrequencyOSEAAMc", "_$s11AugmentCore13ErrorCategoryOSHAAMc", "_$s11AugmentCore16RecoveryStrategyOSQAAMc", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV011memoryAfterD0s6UInt64VvpMV", "_$s11AugmentCore17FileSystemMonitorCMo", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO2eeoiySbAE_AEtFZ", "_$s11AugmentCore0A6LoggerC11LogCategoryO8databaseyA2EmFWC", "_$s11AugmentCore11FileVersionV11storagePath10Foundation3URLVvg", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivMTj", "_$s11AugmentCore30DefaultVersionCreationDelegateC010createFileD08filePath7commentSb10Foundation3URLV_SSSgtFTj", "_$s11AugmentCore13SpaceSettingsV18networkSyncEnabledSbvM", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvsTj", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOSQAAMc", "_$s11AugmentCore19DependencyContainerC14versionControlAA07VersionF0CyFTj", "_$s11AugmentCore17PerformanceConfigV18monitoringIntervalSdvM", "_$s11AugmentCore15BackupFrequencyOMa", "_$s11AugmentCore17ErrorHistoryEntryVMn", "_$s11AugmentCore13PreviewEngineC6sharedACvpZMV", "_$s11AugmentCore13SpaceSettingsV27storageNotificationsEnabledSbvM", "_$s11AugmentCore27BackgroundProcessingServiceC10submitTask4name4type8priority8workItemxSS_AC0G4TypeOAC0G8PriorityOSgxyYaKctYaKlFTq", "_$s11AugmentCore17ErrorHistoryEntryV5errorAA011RecoverableC0Vvg", "_$s11AugmentCore19MemoryUsageSnapshotV07virtualC4Useds6UInt64VvpMV", "_$s11AugmentCore19NotificationManagerCN", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA3_", "_$s11AugmentCore19SecurityAuditReportV11generatedAt10Foundation4DateVvpMV", "_$s11AugmentCore19DependencyContainerC16largeFileHandlerAA05LargefG0CyFTj", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeOs12CaseIterableAAMc", "_$s11AugmentCore22ConflictResolutionTypeON", "_$s11AugmentCore19SecurityAuditReportV15recommendationsSayAA0C14RecommendationVGvg", "_$s11AugmentCore0A5ErrorOMn", "_$s11AugmentCore14VersionControlC07restoreC010folderPath7versionSb10Foundation3URLV_AA06FolderC0VtFTq", "_$s11AugmentCore19UserInterfaceConfigV12maxListItemsSivM", "_$s11AugmentCore13NetworkConfigV10retryDelaySdvs", "_$s11AugmentCore20ErrorRecoveryManagerCMu", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO9hashValueSivpMV", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore11FileItemRowV4bodyQrvg", "_$s11AugmentCore17SecurityViolationV8SeverityO4highyA2EmFWC", "_$s11AugmentCore18LargeFileOperationON", "_$s11AugmentCore15ErrorValidationC13requireNotNil__7contextxxSg_S2StKlFZ", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore0A6LoggerC8LogLevelO8rawValueAESgSi_tcfC", "_$s11AugmentCore14StorageManagerC18cleanupOldVersions9olderThan2inAA13CleanupResultVSd_AA0A5SpaceVtFTj", "_$s11AugmentCore19DependencyContainerC14storageManagerAA07StorageF0CyFTj", "_$s11AugmentCore24LargeFileOperationResultVN", "_$s11AugmentCore13SpaceSettingsV18maxVersionsPerFileSivM", "_$s11AugmentCore25MemoryOptimizationServiceCN", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4nameSSvpMV", "_$s11AugmentCore0A5SpaceVSQAAMc", "_$s11AugmentCore15BackupFrequencyO8rawValueSSvg", "_$s11AugmentCore19NotificationManagerC28$cleanupNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore0A6LoggerC8LogLevelON", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvMTq", "_$s11AugmentCore13MemoryManagerCMm", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV22enableContentHashCheckSbvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoringSbvs", "_$s11AugmentCore17SecurityViolationV8SeverityOSeAAMc", "_$s11AugmentCore14StorageManagerCMu", "_$s11AugmentCore16RecoveryStrategyOs12CaseIterableAAMc", "_$s11AugmentCore19NotificationManagerC04userC6Center_10didReceive21withCompletionHandlerySo06UNUsercF0C_So22UNNotificationResponseCyyctF", "_$s11AugmentCore15MetadataManagerC015loadFileVersionC08filePath05spaceI0SayAA0fG0VG10Foundation3URLV_ALtFTj", "_$s11AugmentCore25MemoryOptimizationServiceC014startAutomaticD0yyFTj", "_$s11AugmentCore17PerformanceConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13StoragePolicyV7enabledSbvg", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA3_", "_$s11AugmentCore15SecurityManagerC6loggerAcA0A6LoggerC_tcfC", "_$s11AugmentCore16LargeFileHandlerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceH0CtcfCTj", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvsTq", "_$s11AugmentCore15ErrorValidationCACycfCTq", "_$s11AugmentCore16RecoveryStrategyOSHAAMc", "_$s11AugmentCore20ErrorRecoveryManagerCMo", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvsTq", "_$s11AugmentCore0A6LoggerC11LogCategoryO9subsystemSSvpMV", "_$s11AugmentCore13SpaceSettingsVMa", "_$s11AugmentCore19MemoryPressureLevelOSEAAMc", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA3_", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvpMV", "_$s11AugmentCore13SyncDirectionOSEAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO10encryptionyA2EmFWC", "_$s11AugmentCore18ConflictResolutionO5mergeyA2CmFWC", "_$s11AugmentCore8FileDiffV8diffTypeAA0dF0Ovg", "_$s11AugmentCore8SnapshotV10CodingKeysON", "_$s11AugmentCore13NetworkConfigVN", "_$s11AugmentCore15SecurityManagerC06createC14ScopedBookmark3forSb10Foundation3URLV_tFTj", "_$s11AugmentCore14DataEncryptionC13secureCompareySb10Foundation0C0V_AGtFZ", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore11NetworkSyncC16addConfigurationySbAA0dF0VFTj", "_$s11AugmentCore8FileItemV9hashValueSivg", "_$s11AugmentCore19MemoryUsageSnapshotV08physicalC4Useds6UInt64VvpMV", "_$s11AugmentCore17PerformanceConfigV19cpuWarningThresholdSdvg", "_$s11AugmentCore16RecoverableErrorV9timestamp10Foundation4DateVvg", "_$s11AugmentCore14StorageManagerC6logger06memoryD0AcA0A6LoggerC_AA06MemoryD0CSgtcfCTj", "_$s11AugmentCore0A13ConfigurationC7storageAA13StorageConfigVvpMV", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore13SpaceSettingsV14autoVersioningSbvpMV", "_$s11AugmentCore19VersionControlErrorON", "_$s11AugmentCore13NetworkConfigVMa", "_$s11AugmentCore15SecurityManagerC18validateFileAccess_3forSb10Foundation3URLV_AA0F9OperationOtFTj", "_$s11AugmentCore16LargeFileHandlerC07maximumD4Sizes5Int64VvpZMV", "_$s11AugmentCore17SyncConfigurationV10remotePath10Foundation3URLVvpMV", "_$s11AugmentCore10DiffEngineCMo", "_$s11AugmentCore18ErrorHandlingUtilsCMn", "_$s11AugmentCore26FileSystemMonitoringConfigVMa", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvMTq", "_$s11AugmentCore17SecurityViolationV8SeverityO8rawValueSSvpMV", "_$s11AugmentCore12SnapshotFileV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore11FileVersionV11contentHashSSvg", "_$s11AugmentCore15ConflictManagerC15detectConflicts3forSayAA04FileC0VG10Foundation3URLV_tFTq", "_AugmentCoreVersionNumber", "_$s11AugmentCore13MemoryManagerC010getCurrentC5UsageAA0cG8SnapshotVyFTq", "_$s11AugmentCore0A6LoggerCMn", "_$s11AugmentCore17PerformanceConfigV28responseTimeWarningThresholdSdvg", "_$s11AugmentCore24LargeFileOperationResultV10throughputSdvpMV", "_$s11AugmentCore25MemoryOptimizationServiceCMu", "_$s11AugmentCore13ErrorHandlingP20errorRecoveryManagerAA0cfG0CvgTj", "_$s11AugmentCore18PreferencesManagerC27storageNotificationsEnabledSbvsTq", "_$s11AugmentCore12SnapshotFileV10CodingKeysOMn", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC14updateProgressyySdFTq", "_$s11AugmentCore12SnapshotFileV10CodingKeysOs28CustomDebugStringConvertibleAAMc", "_$s11AugmentCore23SimpleSyncConfigurationV9hashValueSivpMV", "_$s11AugmentCore17SyncConfigurationV04lastC9Timestamp10Foundation4DateVSgvpMV", "_$s11AugmentCore11FileItemRowV4bodyQrvpMV", "_$s11AugmentCore8FileDiffV8diffData10Foundation0F0VvpMV", "_$s11AugmentCore19DependencyContainerC6inject18preferencesManageryAA011PreferencesG0C_tFTq", "_$s11AugmentCore19DependencyContainerC14versionControlAA07VersionF0CyFTq", "_$s11AugmentCore0A6LoggerC8critical_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTj", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvMTq", "_$s11AugmentCore13LoggingConfigV04fileC7EnabledSbvs", "_$s11AugmentCore0A5ErrorO13fileCorruptedyACSS_SSSgtcACmFWC", "_$s11AugmentCore12SearchEngineC10removeFile8filePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore13SpaceSettingsV14versioningModeAA010VersioningF0Ovs", "_$s11AugmentCore17FileSystemMonitorC6logger20errorRecoveryManager06memoryI0AcA0A6LoggerC_AA05ErrorhI0CAA06MemoryI0CtcfCTq", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA1_", "_$s11AugmentCore13BackupManagerC16addConfigurationySbAA0cF0VFTj", "_$s11AugmentCore0A5ErrorO21versionCreationFailedyACSS_SStcACmFWC", "_$s11AugmentCore13FolderVersionV7commentSSSgvpMV", "_$s11AugmentCore0A5ErrorO16fileAccessDeniedyACSS_tcACmFWC", "_$s11AugmentCore8SnapshotVMa", "_$s11AugmentCore19DependencyContainerC27backgroundProcessingServiceAA010BackgroundfG0CyFTq", "_$s11AugmentCore8DiffTypeOSQAAMc", "_$s11AugmentCore8LoggablePAAE6loggerAA0A6LoggerCvg", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore15ConflictManagerCN", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore0A5ErrorO23configurationLoadFailedyACs0C0_p_tcACmFWC", "_$s11AugmentCore13ErrorCategoryO4iconSSvg", "_$s11AugmentCore16RecoverableErrorV16technicalDetailsSSvg", "_$s11AugmentCore0A5ErrorO11descriptionSSvg", "_$s11AugmentCore15SecurityManagerC18validateFileAccess_3forSb10Foundation3URLV_AA0F9OperationOtFTq", "_$s11AugmentCore10FileHasherMp", "_$s11AugmentCore19DependencyContainerC13backupManagerAA06BackupF0CyFTq", "_$s11AugmentCore17SnapshotSchedulerC20applyRetentionPolicy33_7E7566F25733DFECB0CF9BB3A350FCB8LL8scheduleyAA0C8ScheduleV_tF", "_$s11AugmentCore26FileSystemMonitoringConfigV25emergencyCleanupThresholdSivs", "_$s11AugmentCore13DiffOperationV0D4TypeO8rawValueAESgSS_tcfC", "_$s11AugmentCore21SecurityAuditReporterC15securityManager6loggerAcA0cG0C_AA0A6LoggerCtcfC", "_$s11AugmentCore19BackupConfigurationVN", "_$s11AugmentCore11FileVersionVMn", "_$s11AugmentCore23SimpleSyncConfigurationV04autoD0Sbvg", "_$s11AugmentCore13StorageConfigV28defaultCleanupFrequencyHoursSivpMV", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvgTq", "_$s11AugmentCore15SecurityManagerC6loggerAcA0A6LoggerC_tcfc", "_$s11AugmentCore17SnapshotRetentionOMn", "_$s11AugmentCore16RecoverableErrorV16technicalDetailsSSvpMV", "_$s11AugmentCore15MetadataManagerC6logger17encryptionEnabledAcA0A6LoggerC_SbtcfCTq", "_$s11AugmentCore17PerformanceConfigVACycfC", "_$s11AugmentCore8SnapshotVSEAAMc", "_$s11AugmentCore12SearchEngineC18getIndexStatisticsSDySSypGyFTj", "_$s11AugmentCore17SnapshotSchedulerCN", "_$s11AugmentCore14StorageManagerC18cleanupOldVersions9olderThan2inAA13CleanupResultVSd_AA0A5SpaceVtFTq", "_$s11AugmentCore8FileItemVSHAAMc", "_$s11AugmentCore13FileOperationOSHAAMc", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4pathSSvpMV", "_$s11AugmentCore13DiffOperationV0D4TypeO5addedyA2EmFWC", "_$s11AugmentCore13BackupManagerC07restoreC010backupPath05spaceG08passwordSb10Foundation3URLV_AJSSSgtFTq", "_$s11AugmentCore17FileAccessSummaryV17accessSuccessRateSdvg", "_$s11AugmentCore13SecurityEventO15bookmarkCreatedyACSS_tcACmFWC", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsVMn", "_$s11AugmentCore15ErrorValidationCACycfc", "_$s11AugmentCore17CancellationErrorV10Foundation09LocalizedD0AAMc", "_$s11AugmentCore8FileTypeO4textyA2CmFWC", "_$s11AugmentCore19SecurityAuditReportVN", "_$s11AugmentCore14DataEncryptionCMo", "_$s11AugmentCore15PreviewFileTypeOMn", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOs0E3KeyAAMc", "_$s11AugmentCore17FileAccessSummaryV17accessSuccessRateSdvpMV", "_$s11AugmentCore14StorageManagerC6sharedACvpZMV", "_$s11AugmentCore19DependencyContainerC6inject19notificationManageryAA012NotificationG0C_tFTq", "_$s11AugmentCore19BackupConfigurationV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore12SearchEngineCfD", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvMTj", "_$s11AugmentCore17SyncConfigurationV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore17PerformanceConfigV31emergencyMemoryCleanupThresholdSivs", "_$s11AugmentCore13ErrorCategoryO8rawValueACSgSS_tcfC", "_$s11AugmentCore14VersionControlCMm", "_$s11AugmentCore16LargeFileHandlerC07maximumD4Sizes5Int64VvgZ", "_$s11AugmentCore13StorageConfigV29notificationsEnabledByDefaultSbvg", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleIntervalSdvM", "_$s11AugmentCore8FileItemV2id10Foundation4UUIDVvg", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO6backupyA2EmFWC", "_$s11AugmentCore11StorageInfoV9totalSize12versionCount13oldestVersion06newestJ016spaceUtilization0L4Path016augmentDirectoryF0013originalFilesF0ACs5Int64V_Si10Foundation4DateVAPSdAN3URLVA2MtcfC", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfcfA2_", "_$s11AugmentCore8FileItemV9typeColor7SwiftUI0F0VvpMV", "_$s11AugmentCore13SpaceSettingsV13autoSnapshotsSbvM", "_$s11AugmentCore18SecurityAuditEntryVSeAAMc", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvpMV", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvMTj", "_$s11AugmentCore17PerformanceConfigV21uiUpdatePauseIntervalSdvpMV", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvgTq", "_$s11AugmentCore24LargeFileOperationResultV16formattedSummarySSvg", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV13operationName9startTimeAESS_SdtcfC", "_$s11AugmentCore25MemoryOptimizationServiceC02isD6NeededSbyFTj", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivsTj", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA0_", "_$s11AugmentCore17SecurityViolationV8SeverityO3lowyA2EmFWC", "_$s11AugmentCore17ErrorHistoryEntryV10resolvedAt10Foundation4DateVSgvs", "_$s11AugmentCore17SnapshotSchedulerC11addScheduleyyAA0cF0VFTq", "_$s11AugmentCore19MemoryPressureLevelON", "_$s11AugmentCore11StorageInfoV12versionCountSivpMV", "_$s11AugmentCore8FileItemV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore12SearchConfigVACycfC", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvpMV", "_$s11AugmentCore19DependencyContainerC6inject19notificationManageryAA012NotificationG0C_tFTj", "_$s11AugmentCore17SnapshotRetentionOSeAAMc", "_$s11AugmentCore16RecoveryStrategyO11isAutomaticSbvpMV", "_$s11AugmentCore16SnapshotScheduleV04lastC9Timestamp10Foundation4DateVSgvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvMTj", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOMa", "_$s11AugmentCore16SHA256FileHasherV8finalizeSSyF", "_$s11AugmentCore13ErrorCategoryO7unknownyA2CmFWC", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore17ErrorHistoryEntryV10resolvedAt10Foundation4DateVSgvg", "_$s11AugmentCore14DataEncryptionCACycfC", "_$s11AugmentCore12SearchEngineC6search5query10spacePathsSayAA0C6ResultVGSS_Say10Foundation3URLVGtFTj", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8criticalyA2EmFWC", "_$s11AugmentCore18PreferencesManagerCfD", "_$s11AugmentCore19BackupConfigurationV04lastC9Timestamp10Foundation4DateVSgvpMV", "_$s11AugmentCore17SecurityViolationV8SeverityON", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore14RiskAssessmentV04highC6EventsSivpMV", "_$s11AugmentCore18PerformanceMonitorC7measure_5blockxSS_xyKXEtKlFTq", "_$s11AugmentCore23SimpleSyncConfigurationV9authTokenSSvM", "_$s11AugmentCore14VersionControlC11getVersions10folderPathSayAA06FolderC0VG10Foundation3URLV_tFTj", "_$s11AugmentCore19NotificationManagerC7Combine16ObservableObjectAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV18maxConcurrentTasksSDyAC12TaskPriorityOSiGvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO11descriptionSSvpMV", "_$s11AugmentCore0A6LoggerC11LogCategoryO7storageyA2EmFWC", "_$s11AugmentCore18PreferencesManagerC22$cleanupFrequencyHours7Combine9PublishedV9PublisherVySi_GvsTq", "_$s11AugmentCore14SecurityConfigV27fileAccessValidationEnabledSbvg", "_$s11AugmentCore15SnapshotManagerC17scheduleSnapshots9spacePath0E0Sb10Foundation3URLV_AA0C8ScheduleVtFTj", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityOSYAAMc", "_$s11AugmentCore12SearchResultV5tokenSSvpMV", "_$s11AugmentCore13SpaceSettingsV15maxStorageBytess5Int64VvM", "_$s11AugmentCore17PerformanceConfigV22maxCompletedOperationsSivs", "_$s11AugmentCore26FileSystemMonitoringConfigV15fsEventsLatencySdvpMV", "_$s11AugmentCore13MemoryManagerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceG0CtcfC", "_$s11AugmentCore15MetadataManagerCfd", "_$s11AugmentCore16SHA256FileHasherVMa", "_$s11AugmentCore12ExportFormatO8allCasesSayACGvgZ", "_$s11AugmentCore17SnapshotSchedulerCfd", "_$s11AugmentCore19BackupConfigurationVSeAAMc", "_$s11AugmentCore22SecurityRecommendationV8priorityAC8PriorityOvpMV", "_$s11AugmentCore8FileTypeO8allCasesSayACGvpZMV", "_$s11AugmentCore18SecurityAuditEntryV9timestampSSvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvsTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV8strategyAC0D8StrategyOvpMV", "_$s11AugmentCore15BackupFrequencyO8intervalSdvg", "_$s11AugmentCore8FileItemVSeAAMc", "_$s11AugmentCore19MemoryPressureLevelO7warningyA2CmFWC", "_$s11AugmentCore0A6LoggerC8LogLevelO5erroryA2EmFWC", "_$s11AugmentCore11FileVersionV8filePath10Foundation3URLVvpMV", "_$s11AugmentCore22SecurityRecommendationV8PriorityO8rawValueSSvpMV", "_$s11AugmentCore15ErrorValidationC16validateFilePath_9operation13requiresWritey10Foundation3URLV_SSSbtKFZ", "_$s11AugmentCore8FileTypeOMn", "_$s11AugmentCore18SecurityAuditEntryV11descriptionSSvpMV", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore14DataEncryptionC15rotateMasterKeyyyKFZ", "_$s11AugmentCore11StorageInfoV9spacePath10Foundation3URLVvg", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV13operationNameSSvg", "_$s11AugmentCore19UserInterfaceConfigVMa", "_$s11AugmentCore0A6LoggerC5debug_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTq", "_$s11AugmentCore9RiskLevelO3lowyA2CmFWC", "_$s11AugmentCore17FileSystemMonitorC6sharedACvgZ", "_$s11AugmentCore20ErrorRecoveryManagerC013executeManualD0_3foryAA0D8StrategyO_AA011RecoverableC0VtFTq", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigVMa", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleIntervalSdvg", "_$s11AugmentCore25MemoryOptimizationServiceC08registerD7Handler10identifier7handlerySS_yAC0D8StrategyOctFTq", "_$s11AugmentCore22SecurityRecommendationV8categorySSvpMV", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigVMn", "_$s11AugmentCore13PreviewEngineCMa", "_$s11AugmentCore0A13ConfigurationC17defaultWindowSizeSd5width_Sd6heighttvpMV", "_$s11AugmentCore13SpaceSettingsV14autoVersioningSbvg", "_$s11AugmentCore18SecurityAuditEntryV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore13ErrorSeverityO8rawValueSSvg", "_$s11AugmentCore25MemoryOptimizationServiceC07performD08strategyAC0D5StatsVAC0D8StrategyOSg_tFTq", "_$s11AugmentCore24LargeFileOperationResultV9operation8fileSize8duration10throughput7successAcA0cdE0O_s5Int64VS2dSbtcfC", "_$s11AugmentCore17PerformanceConfigV21uiUpdatePauseIntervalSdvs", "_$s11AugmentCore19DependencyContainerC5resetyyFTq", "_$s11AugmentCore13NetworkConfigV20syncEnabledByDefaultSbvs", "_$s11AugmentCore15SecurityManagerCMn", "_$s11AugmentCore25MemoryOptimizationServiceC13memoryManager6logger18performanceMonitor6configAcA0cG0C_AA0A6LoggerCAA011PerformanceJ0CAC0D6ConfigVtcfC", "_$s11AugmentCore13MemoryManagerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceG0CtcfCTj", "_$s11AugmentCore14RiskAssessmentV03lowC6EventsSivpMV", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8rawValueSSvg", "_$s11AugmentCore9RiskLevelO8rawValueSSvg", "_$s11AugmentCore17PerformanceConfigV22memoryCleanupThresholdSivM", "_$s11AugmentCore14DataEncryptionC0D5ErrorO16encryptionFailedyA2EmFWC", "_$s11AugmentCore16RecoveryStrategyO4iconSSvg", "_$s11AugmentCore23SimpleSyncConfigurationV2eeoiySbAC_ACtFZ", "_$s11AugmentCore17SnapshotSchedulerC14removeSchedule9spacePathy10Foundation3URLV_tFTq", "_$s11AugmentCore15ErrorValidationCN", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvMTq", "_$s11AugmentCore9RiskLevelO6mediumyA2CmFWC", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigVAEycfC", "_$s11AugmentCore0A13ConfigurationC14$userInterface7Combine9PublishedV9PublisherVyAA04UserE6ConfigV_GvMTj", "_$s11AugmentCore19DependencyContainerC15securityManagerAA08SecurityF0CyFTj", "_$s11AugmentCore13SpaceSettingsV23storageWarningThresholdSdvM", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore15BackupRetentionON", "_$s11AugmentCore16RecoverableErrorV11userMessageSSvpMV", "_$s11AugmentCore11FileVersionV10CodingKeysOMn", "_$s11AugmentCore17FileSystemMonitorCN", "_$s11AugmentCore8FileItemV3url10Foundation3URLVvpMV", "_$s11AugmentCore17SnapshotSchedulerCMo", "_$s11AugmentCore20ErrorRecoveryManagerC6sharedACvgZ", "_$s11AugmentCore19SecurityAuditReportV7endDate10Foundation0G0VSgvg", "_$s11AugmentCore0A6LoggerC8LogLevelO02osD4TypeSo0F11_log_type_tavpMV", "_$s11AugmentCore11NetworkSyncC17getConfigurationsSayAA0D13ConfigurationVGyFTq", "_$s11AugmentCore13ErrorSeverityOSQAAMc", "_$s11AugmentCore11NetworkSyncC19removeConfiguration9spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore14VersionControlC11getVersions10folderPathSayAA06FolderC0VG10Foundation3URLV_tFTq", "_$s11AugmentCore27ConflictResolutionUtilitiesCfD", "_$s11AugmentCore13SpaceSettingsV21cleanupFrequencyHoursSivpMV", "_$s11AugmentCore25MemoryOptimizationServiceC13memoryManager6logger18performanceMonitor6configAcA0cG0C_AA0A6LoggerCAA011PerformanceJ0CAC0D6ConfigVtcfCTq", "_$s11AugmentCore18LargeFileOperationO5writeyA2CmFWC", "_$s11AugmentCore14VersionControlC010initializecD010folderPathSb10Foundation3URLV_tFTj", "_$s11AugmentCore20ErrorRecoveryManagerC19clearResolvedErrorsyyFTj", "_$s11AugmentCore8SnapshotV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore8SnapshotV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore16SnapshotScheduleV04lastC9Timestamp10Foundation4DateVSgvM", "_$s11AugmentCore23SimpleSyncConfigurationV9serverURLSSvpMV", "_$s11AugmentCore17ErrorHistoryEntryVSeAAMc", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO12fileMetadatayA2EmFWC", "_$s11AugmentCore17FileSystemMonitorC6logger20errorRecoveryManager06memoryI0AcA0A6LoggerC_AA05ErrorhI0CAA06MemoryI0CtcfCTj", "_$s11AugmentCore8SnapshotVN", "_$s11AugmentCore8FileItemV16modificationDate10Foundation0F0Vvg", "_$s11AugmentCore13FolderVersionVSeAAMc", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodOMn", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore12SearchConfigV12indexTimeoutSdvM", "_$s11AugmentCore18PreferencesManagerC12maxStorageGBSdvgTj", "_$s11AugmentCore17SnapshotFrequencyOSYAAMc", "_$s11AugmentCore19UserInterfaceConfigV17animationDurationSdvs", "_$s11AugmentCore8SnapshotV5filesSayAA0C4FileVGvM", "_$s11AugmentCore14DataEncryptionC9deriveKey4from4salt10iterations9CryptoKit09SymmetricF0VSS_10Foundation0C0VSitFZ", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV9timestamp8strategy012memoryBeforeD00i5AfterD08duration16handlersExecutedAE10Foundation4DateV_AC0D8StrategyOs6UInt64VARSdSitcfC", "_$s11AugmentCore12FileConflictV22remoteModificationDate10Foundation0G0Vvg", "_$s11AugmentCore19MemoryUsageSnapshotV06memoryD10PercentageSdvg", "_$s11AugmentCore26FileSystemMonitoringConfigV22defaultExcludePatternsSaySSGvM", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV25memoryRecoveredPercentageSdvg", "_$s11AugmentCore15BackupFrequencyON", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA3_", "_$s11AugmentCore23VersionCreationDelegateP010createFileC08filePath7commentSb10Foundation3URLV_SSSgtFTj", "_$s11AugmentCore17PerformanceConfigV18monitoringIntervalSdvpMV", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV8cpuUsageSdvpMV", "_$s11AugmentCore12SearchConfigV14maxQueryLengthSivs", "_$s11AugmentCore17SnapshotSchedulerCfD", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvgTq", "_$s11AugmentCore11NetworkSyncCMu", "_$s11AugmentCore18ConflictResolutionOSQAAMc", "_$s11AugmentCore16RecoveryStrategyO20checkFilePermissionsyA2CmFWC", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA0_", "_$s11AugmentCore0A13ConfigurationC22configureForProductionyyF", "_$s11AugmentCore17SnapshotRetentionO4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11completedAt10Foundation4DateVSgvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC10submitTask4name4type8priority8workItem15progressHandler010completionN0AC0cG0CSS_AC0G4TypeOAC0G8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtFTj", "_$s11AugmentCore13SyncFrequencyOMn", "_$s11AugmentCore0A6LoggerC8LogLevelO7warningyA2EmFWC", "_$s11AugmentCore18ConfigurationErrorO16errorDescriptionSSSgvg", "_$s11AugmentCore8SnapshotV4nameSSvpMV", "_$s11AugmentCore14DataEncryptionC0D5ErrorO08keychainE0yAEs5Int32VcAEmFWC", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfcfA0_", "_$s11AugmentCore27BackgroundProcessingServiceCfd", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigVMa", "_$s11AugmentCore8SnapshotV10CodingKeysOMa", "_$s11AugmentCore8FileTypeON", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigVN", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV9diskUsages6UInt64Vvg", "_$s11AugmentCore9RiskLevelOMn", "_$s11AugmentCore19DependencyContainerCMm", "_$s11AugmentCore0A13ConfigurationC13userInterfaceAA04UserE6ConfigVvgTq", "_$s11AugmentCore11FileVersionV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore13SpaceSettingsV15excludePatternsSaySSGvs", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore14DataEncryptionC0D5ErrorOMa", "_$s11AugmentCore13HashAlgorithmOMa", "_$s11AugmentCore0A5ErrorO14dataCorruptionyACSS_SStcACmFWC", "_$s11AugmentCore19MemoryUsageSnapshotV14memoryPressureAA0cG5LevelOvg", "_$s11AugmentCore0A5ErrorO13failureReasonSSSgvpMV", "_$s11AugmentCore13SyncFrequencyO8intervalSdvg", "_$s11AugmentCore19DependencyContainerC6inject18performanceMonitoryAA011PerformanceG0C_tFTq", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyOMa", "_$s11AugmentCore8FileItemV4typeAA0C4TypeOvpMV", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvsTj", "_$s11AugmentCore17SnapshotSchedulerC11getSchedule9spacePathAA0cF0VSg10Foundation3URLV_tFTq", "_$s11AugmentCore12SnapshotFileV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore13LoggingConfigV011performanceC7EnabledSbvs", "_$s11AugmentCore0A5ErrorO15versionNotFoundyACSS_SStcACmFWC", "_$s11AugmentCore8SnapshotV10CodingKeysOs23CustomStringConvertibleAAMc", "_$s11AugmentCore14SecurityConfigV17encryptionEnabledSbvpMV", "_$s11AugmentCore12SearchConfigV18relevanceThresholdSdvs", "_$s11AugmentCore15ConflictManagerC07resolveC0_10resolutionSbAA04FileC0V_AA0C10ResolutionOtFTq", "_$s11AugmentCore17CancellationErrorVs0D0AAMc", "_$s11AugmentCore0A6LoggerC11LogCategoryO14versionControlyA2EmFWC", "_$s11AugmentCore11FileVersionV11storagePath10Foundation3URLVvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4name4type8priority8workItem15progressHandler010completionM0AESS_AC0F4TypeOAC0F8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtcfCTq", "_$s11AugmentCore11FileVersionV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore17SecurityViolationV9timestampSSvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfC", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvgTj", "_$s11AugmentCore13MemoryManagerC08optimizeC5UsageyyFTj", "_$s11AugmentCore17SnapshotRetentionO6encode2toys7Encoder_p_tKF", "_$s11AugmentCore13NetworkConfigV13maxUploadSizes5Int64Vvs", "_$s11AugmentCore13HashAlgorithmO4hash4intoys6HasherVz_tF", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusOMa", "_$s11AugmentCore17PerformanceConfigV31emergencyMemoryCleanupThresholdSivpMV", "_$s11AugmentCore16LargeFileHandlerCfD", "_$s11AugmentCore26FileSystemMonitoringConfigV25emergencyCleanupThresholdSivg", "_$s11AugmentCore11FileVersionV9hashValueSivpMV", "_$s11AugmentCore12ErrorMetricsCMn", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvgTq", "_$s11AugmentCore11NetworkSyncC13syncAllSpacesSbyFTq", "_$s11AugmentCore18PerformanceMonitorC6loggerAcA0A6LoggerC_tcfCTq", "_$s11AugmentCore19DependencyContainerC6inject13backupManageryAA06BackupG0C_tFTq", "_$s11AugmentCore19SecurityAuditReportV12eventsByTypeSDySSSiGvpMV", "_$s11AugmentCore18ErrorHandlingUtilsCMm", "_$s11AugmentCore12SearchConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore13LoggingConfigV07consoleC7EnabledSbvM", "_$s11AugmentCore13BackupManagerCACycfC", "_$s11AugmentCore13SpaceSettingsV18maxVersionsPerFileSivs", "_$s11AugmentCore11FileVersionV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore13MemoryManagerCN", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore12SnapshotFileVSeAAMc", "_$s11AugmentCore13SyncDirectionOMn", "_$s11AugmentCore13StoragePolicyV0D4TypeO4fromAEs7Decoder_p_tKcfC", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV14maxHistorySizeSivg", "_$s11AugmentCore12FileConflictVMa", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivsTq", "_$s11AugmentCore0A6LoggerC4info_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTj", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsVN", "_$s11AugmentCore10DiffEngineC6sharedACvpZMV", "_$s11AugmentCore17SyncConfigurationV9isEnabledSbvpMV", "_$s11AugmentCore13ErrorCategoryO7storageyA2CmFWC", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvMTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC8durationSdSgvpMV", "_$s11AugmentCore13BackupManagerCACycfCTq", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvMTq", "_$s11AugmentCore10DiffEngineCMm", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC5errors5Error_pSgvgTq", "_$s11AugmentCore17SnapshotSchedulerCACycfc", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA5_", "_$s11AugmentCore16RecoverableErrorVSQAAMc", "_$s11AugmentCore17FileSystemMonitorC15startMonitoringyyFTj", "_$s11AugmentCore17PerformanceConfigV22memoryWarningThresholds5Int64VvpMV", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvsTq", "_$s11AugmentCore14VersionControlC010createFileC5Async8filePath7commentAA0fC0VSg10Foundation3URLV_SSSgtYaFTjTu", "_$s11AugmentCore15SnapshotManagerC11getSchedule9spacePathAA0cF0VSg10Foundation3URLV_tFTq", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA_", "_$s11AugmentCore13SpaceSettingsVSEAAMc", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfcfA3_", "_$s11AugmentCore8FileItemV10systemIconSSvpMV", "_$s11AugmentCore17SecurityViolationVMa", "_$s11AugmentCore21SecurityAuditReporterC12exportReport_6format2toSbAA0cdG0V_AA12ExportFormatO10Foundation3URLVtFTq", "_$s11AugmentCore10DiffEngineC6sharedACvgZ", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvsTq", "_$s11AugmentCore12ErrorMetricsC6sharedACvpZMV", "_$s11AugmentCore17SecurityViolationV8severityAC8SeverityOvpMV", "_$s11AugmentCore0A5ErrorO21viewStateInconsistentyACSS_SStcACmFWC", "_$s11AugmentCore18PreferencesManagerC13configuration07storageD0012notificationD06loggerAcA0A13ConfigurationC_AA07StorageD0CAA012NotificationD0CAA0A6LoggerCtcfCTj", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA0_", "_$s11AugmentCore0A6LoggerC5error_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTq", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodOSQAAMc", "_$s11AugmentCore15MetadataManagerC011saveVersionC07version9spacePathSbAA06FolderF0V_10Foundation3URLVtFTq", "_$s11AugmentCore13StorageConfigVSEAAMc", "_$s11AugmentCore13StoragePolicyV0D4TypeOMa", "_$s11AugmentCore11NetworkSyncC19removeConfiguration9spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvsTq", "_$s11AugmentCore19BackupConfigurationVSEAAMc", "_$s11AugmentCore0A13ConfigurationC8$storage7Combine9PublishedV9PublisherVyAA13StorageConfigV_GvsTq", "_$s11AugmentCore11FileVersionVSQAAMc", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvMTj", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA0_", "_$s11AugmentCore8FileItemV13fileExtensionSSvg", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOSQAAMc", "_$s11AugmentCore8FileDiffVMa", "_$s11AugmentCore19DependencyContainerC6inject12searchEngineyAA06SearchG0C_tFTj", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11completedAt10Foundation4DateVSgvgTj", "_$s11AugmentCore16RecoveryStrategyO14contactSupportyA2CmFWC", "_$s11AugmentCore15MetadataManagerC011saveVersionC07version9spacePathSbAA06FolderF0V_10Foundation3URLVtFTj", "_$s11AugmentCore13FolderVersionVs12IdentifiableAAMc", "_$s11AugmentCore8FileItemV4nameSSvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigVN", "_$s11AugmentCore15PreviewFileTypeO5otheryA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceCMn", "_$s11AugmentCore0A5ErrorO20backgroundTaskFailedyACSS_SStcACmFWC", "_$s11AugmentCore0A5SpaceVN", "_$s11AugmentCore13MemoryManagerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceG0Ctcfc", "_$s11AugmentCore13DiffOperationV4typeAC0D4TypeOvpMV", "_$s11AugmentCore13MemoryManagerC21getTrackedObjectCountSiyFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC6statusAE0F6StatusOvgTj", "_$s11AugmentCore26FileSystemMonitoringConfigVSeAAMc", "_$s11AugmentCore18ErrorHandlingUtilsCN", "_$s11AugmentCore19MemoryUsageSnapshotVMa", "_$s11AugmentCore13SpaceSettingsV15maxStorageBytess5Int64Vvs", "_$s11AugmentCore20ErrorRecoveryManagerC13$activeErrors7Combine9PublishedV9PublisherVySayAA011RecoverableC0VG_GvsTq", "_$s11AugmentCore9RiskLevelO7minimalyA2CmFWC", "_$s11AugmentCore17SnapshotSchedulerC05startD033_7E7566F25733DFECB0CF9BB3A350FCB8LLyyF", "_$s11AugmentCore17SyncConfigurationV9spacePath10Foundation3URLVvpMV", "_$s11AugmentCore17SnapshotFrequencyO6weeklyyA2CmFWC", "_$s11AugmentCore12SnapshotFileV12relativePathSSvpMV", "_$s11AugmentCore17SecurityViolationV8SeverityOMa", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvsTj", "_$s11AugmentCore0A13ConfigurationC06exportC0SSSgyF", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC4name4type8priority8workItem15progressHandler010completionM0AESS_AC0F4TypeOAC0F8PriorityOSgyyKcySdcSgys6ResultOyyts5Error_pGcSgtcfC", "_$s11AugmentCore8DiffTypeO8rawValueACSgSS_tcfC", "_$s11AugmentCore30DefaultVersionCreationDelegateCMa", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivpMV", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore13ErrorSeverityO8allCasesSayACGvgZ", "_$s11AugmentCore16RecoverableErrorV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore15FileSystemEventO8modifiedyA2CmFWC", "_$s11AugmentCore13ErrorHandlingPAAE03logC0_7contextys0C0_p_SSSgtF", "_$s11AugmentCore22SecurityRecommendationV11descriptionSSvg", "_$s11AugmentCore8FileTypeOMa", "_$s11AugmentCore8SnapshotVs12IdentifiableAAMc", "_$s11AugmentCore17SyncConfigurationV4hash4intoys6HasherVz_tF", "_$s11AugmentCore17SnapshotSchedulerC015createScheduledC033_7E7566F25733DFECB0CF9BB3A350FCB8LL8scheduleyAA0C8ScheduleV_tF", "_$s11AugmentCore19MemoryPressureLevelOSQAAMc", "_$s11AugmentCore23SimpleSyncConfigurationV9hashValueSivg", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvsTj", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV9timestamp10Foundation4DateVvpMV", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleInterval18maxMonitoredSpaces15fsEventsLatency26throttlingCleanupThreshold09emergencypQ00O15EntryExpiration22defaultExcludePatternsACSd_SiSdS2iSdSaySSGtcfcfA2_", "_$s11AugmentCore0A6LoggerC15minimumLogLevel17enableFileLoggingA2C0eF0O_SbtcfCTq", "_$s11AugmentCore20ErrorRecoveryManagerC03getC7HistorySayAA0cG5EntryVGyFTq", "_$s11AugmentCore16RecoveryStrategyO11workOfflineyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceCMu", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA4_", "_$s11AugmentCore19MemoryUsageSnapshotV09formattedcD0SSvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCfD", "_$s11AugmentCore8SnapshotV5filesSayAA0C4FileVGvg", "_$s11AugmentCore13SpaceSettingsV15maxStorageBytess5Int64Vvg", "_$s11AugmentCore8FileItemV6existsSbvg", "_$s11AugmentCore0A13ConfigurationC23fileBatchProcessingSizeSivg", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvgTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV15memoryRecovereds6UInt64VvpMV", "_$s11AugmentCore13ErrorHandlingMp", "_$s11AugmentCore8FileTypeO11descriptionSSvpMV", "_$s11AugmentCore11FileVersionV10CodingKeysON", "_$s11AugmentCore14SecurityConfigV20securityScanIntervalSivM", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV4nameSSvg", "_$s11AugmentCore18SecurityAuditEntryV5eventSSvg", "_$s11AugmentCore0A6LoggerC11LogCategoryOSQAAMc", "_$s11AugmentCore19UserInterfaceConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13SpaceSettingsV23storageWarningThresholdSdvg", "_$s11AugmentCore26FileSystemMonitoringConfigV25emergencyCleanupThresholdSivpMV", "_$s11AugmentCore18PerformanceMonitorC15startMonitoring8intervalySd_tFTj", "_$s11AugmentCore0A6LoggerC15minimumLogLevel17enableFileLoggingA2C0eF0O_SbtcfC", "_$s11AugmentCore8FileItemV25formattedModificationDateSSvg", "_$s11AugmentCore16SnapshotScheduleV9isEnabledSbvpMV", "_$s11AugmentCore19SecurityAuditReportV14riskAssessmentAA04RiskG0VvpMV", "_$s11AugmentCore19DependencyContainerC16largeFileHandlerAA05LargefG0CyFTq", "_$s11AugmentCore12SearchConfigV10maxResultsSivg", "_$s11AugmentCore8FileItemV16modificationDate10Foundation0F0VvpMV", "_$s11AugmentCore20ErrorRecoveryManagerCMn", "_$s11AugmentCore13StorageConfigVMa", "_$s11AugmentCore11NetworkSyncC10fileSystem15conflictManagerAcA09SpaceFileF9Providing_p_AA08ConflictH0CtcfCTj", "_$s11AugmentCore14DataEncryptionCfd", "_$s11AugmentCore0A13ConfigurationC12$performance7Combine9PublishedV9PublisherVyAA17PerformanceConfigV_GvMTq", "_$s11AugmentCore13BackupManagerC07restoreC010backupPath05spaceG08password10completiony10Foundation3URLV_AKSSSgySbctFTq", "_$s11AugmentCore0A5ErrorO14indexingFailedyACSS_SStcACmFWC", "_$s11AugmentCore13SpaceSettingsV22snapshotFrequencyHoursSivs", "_$s11AugmentCore0A13ConfigurationC30fileMonitoringThrottleIntervalSdvpMV", "_$s11AugmentCore14StorageManagerC15startMonitoring8intervalySd_tFTj", "_$s11AugmentCore8FileItemVMa", "_$s11AugmentCore19NotificationManagerC19cancelNotifications3forySS_tFTq", "_$s11AugmentCore13MemoryManagerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceG0CtcfCTq", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvsTj", "_$s11AugmentCore22ConflictResolutionTypeOMn", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvgTj", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV9timestamp10Foundation4DateVvg", "_$s11AugmentCore12SearchResultVSHAAMc", "_$s11AugmentCore17FileSystemMonitorC20errorRecoveryManagerAA05ErrorgH0CvpMV", "_$s11AugmentCore13DiffOperationV0D4TypeOSEAAMc", "_$s11AugmentCore14RiskAssessmentV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvpMV", "_$s11AugmentCore11StorageInfoVN", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV11taskTimeoutSdvpMV", "_$s11AugmentCore14RiskAssessmentVMn", "_$s11AugmentCore11StorageInfoV16spaceUtilizationSdvpMV", "_$s11AugmentCore0A5ErrorO20storageQuotaExceededyACs5Int64V_AFtcACmFWC", "_$s11AugmentCore12SearchConfigV14minQueryLengthSivg", "_$s11AugmentCore0A13ConfigurationCACycfCTq", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvsTq", "_$s11AugmentCore11FileVersionV2eeoiySbAC_ACtFZ", "_$s11AugmentCore15BackupRetentionOMa", "_$s11AugmentCore13LoggingConfigVSeAAMc", "_$s11AugmentCore19MemoryUsageSnapshotVSEAAMc", "_$s11AugmentCore19DependencyContainerC17fileSystemMonitorAA04FilefG0CyFTj", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvgTq", "_$s11AugmentCore12SnapshotFileV8filePath10Foundation3URLVvg", "_$s11AugmentCore16SHA256FileHasherVACycfC", "_$s11AugmentCore18LargeFileOperationOSQAAMc", "_$s11AugmentCore13StorageConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore12SearchEngineC18getIndexStatisticsSDySSypGyFTq", "_$s11AugmentCore19DependencyContainerCMo", "_$s11AugmentCore12SearchEngineCMu", "_$s11AugmentCore13LoggingConfigV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore11NetworkSyncCMo", "_$s11AugmentCore17SyncConfigurationVMn", "_$s11AugmentCore13MemoryManagerC22forceGarbageCollectionyyFTj", "_$s11AugmentCore19MemoryPressureLevelOSHAAMc", "_$s11AugmentCore30DefaultVersionCreationDelegateCfD", "_$s11AugmentCore19VersionControlErrorO17restorationFailedyACSScACmFWC", "_$s11AugmentCore13LoggingConfigV16logRetentionDaysSivg", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO8rawValueAGSgSS_tcfC", "_$s11AugmentCore13FolderVersionVSEAAMc", "_$s11AugmentCore18LargeFileOperationO4readyA2CmFWC", "_$s11AugmentCore19DependencyContainerC14storageManagerAA07StorageF0CyFTq", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvM", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore27ConflictResolutionUtilitiesCACycfCTj", "_$s11AugmentCore17SecurityViolationV8SeverityO6mediumyA2EmFWC", "_$s11AugmentCore0A6LoggerC11LogCategoryO10fileSystemyA2EmFWC", "_$s11AugmentCore13HashAlgorithmOMn", "_$s11AugmentCore13ErrorHandlingP03logC0_7contextys0C0_p_SSSgtFTq", "_$s11AugmentCore24LargeFileOperationResultV7successSbvg", "_$s11AugmentCore13SpaceSettingsV23storageWarningThresholdSdvs", "_$s11AugmentCore18PreferencesManagerC12loadSettingsyyFTj", "_$s11AugmentCore13SyncFrequencyO8intervalSdvpMV", "_$s11AugmentCore13LoggingConfigV14maxLogFileSizes5Int64Vvg", "_$s11AugmentCore19NotificationManagerC6loggerAcA0A6LoggerC_tcfCTj", "_$s11AugmentCore12SearchEngineCMo", "_$s11AugmentCore17SyncConfigurationVSEAAMc", "_$s11AugmentCore13ErrorSeverityOMa", "_$s11AugmentCore19SecurityAuditReportVMn", "_$s11AugmentCore17SecurityViolationV9timestamp4type11description6userId8severityACSS_S3SAC8SeverityOtcfC", "_$s11AugmentCore0A5ErrorO18networkUnavailableyA2CmFWC", "_$s11AugmentCore0A6LoggerC11LogCategoryO8rawValueSSvg", "_$s11AugmentCore12SnapshotFileV4sizes6UInt64Vvg", "_$s11AugmentCore19DependencyContainerC15conflictManagerAA08ConflictF0CyFTj", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidthSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO9cancelledyA2GmFWC", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV20optimizationIntervalSdvg", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivsTq", "_$s11AugmentCore18SecurityAuditEntryVN", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoringSbvpMV", "_$s11AugmentCore17SecurityViolationV4typeSSvg", "_$s11AugmentCore12SnapshotFileV10CodingKeysOSQAAMc", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvMTj", "_$s11AugmentCore18PreferencesManagerC22$autoVersioningEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore0A6LoggerC6sharedACvpZMV", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC12TaskPriorityO8rawValueAESgSi_tcfC", "_$s11AugmentCore13SpaceSettingsV14autoVersioningSbvM", "_$s11AugmentCore12ConflictTypeO8rawValueSSvg", "_$s11AugmentCore13BackupManagerCACycfc", "_$s11AugmentCore14RiskAssessmentVSeAAMc", "_$s11AugmentCore0A6LoggerC15minimumLogLevel17enableFileLoggingA2C0eF0O_Sbtcfc", "_$s11AugmentCore8FileItemV4typeAA0C4TypeOvg", "_$s11AugmentCore0A5ErrorO16operationTimeoutyACSS_SdtcACmFWC", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvMTj", "_$s11AugmentCore18PreferencesManagerC13configuration07storageD0012notificationD06loggerAcA0A13ConfigurationC_AA07StorageD0CAA012NotificationD0CAA0A6LoggerCtcfc", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA4_", "_$s11AugmentCore17FileAccessSummaryV05totalcD0SivpMV", "_$s11AugmentCore11FileVersionV9hashValueSivg", "_$s11AugmentCore8SnapshotV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore15MetadataManagerCMn", "_$s11AugmentCore17PerformanceConfigV28responseTimeWarningThresholdSdvM", "_$s11AugmentCore12SearchResultV9hashValueSivg", "_$s11AugmentCore19SecurityAuditReportV9startDate10Foundation0G0VSgvpMV", "_$s11AugmentCore14StorageManagerC6sharedACvgZ", "_$s11AugmentCore16RecoverableErrorV2idSSvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV07minimumC10SizeChanges5Int64VvM", "_$s11AugmentCore20ErrorRecoveryManagerC07currentC0AA011RecoverableC0VSgvgTj", "_$s11AugmentCore17PerformanceConfigV18monitoringIntervalSdvg", "_$s11AugmentCore8SnapshotV5filesSayAA0C4FileVGvpMV", "_$s11AugmentCore17FileAccessSummaryV19uniqueFilesAccessedSivpMV", "_$s11AugmentCore13LoggingConfigV04fileC7EnabledSbvpMV", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvsTj", "_$s11AugmentCore10FileHasherTL", "_$s11AugmentCore13NetworkConfigV13maxUploadSizes5Int64VvM", "_$s11AugmentCore23SimpleSyncConfigurationVN", "_$s11AugmentCore19DependencyContainerC15metadataManagerAA08MetadataF0CyFTj", "_$s11AugmentCore0A13ConfigurationC21$fileSystemMonitoring7Combine9PublishedV9PublisherVyAA04FileeF6ConfigV_GvMTq", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvgTj", "_$s11AugmentCore17SyncConfigurationV10CodingKeysOMn", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO16keyboardShortcutyA2EmFWC", "_$s11AugmentCore23SimpleSyncConfigurationVMn", "_$s11AugmentCore0A5ErrorO20versionRestoreFailedyACSS_S2StcACmFWC", "_$s11AugmentCore19SecurityAuditReportV17fileAccessSummaryAA04FilegH0Vvg", "_$s11AugmentCore12StorageIssueON", "_$s11AugmentCore22SecurityRecommendationV8priority8category5title11description6actionA2C8PriorityO_S4StcfC", "_$s11AugmentCore8SnapshotV10CodingKeysO11stringValueAESgSS_tcfC", "_$s11AugmentCore13MemoryManagerC21getTrackedObjectCountSiyFTj", "_$s11AugmentCore13LoggingConfigV16logRetentionDaysSivpMV", "_$s11AugmentCore14DataEncryptionC7decrypty10Foundation0C0VAGKFZ", "_$s11AugmentCore19SecurityAuditReportV18securityViolationsSayAA0C9ViolationVGvpMV", "_$s11AugmentCore18PerformanceMonitorC6loggerAcA0A6LoggerC_tcfCTj", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV8durationSdSgvpMV", "_$s11AugmentCore16SnapshotScheduleVs12IdentifiableAAMc", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvsTq", "_$s11AugmentCore18PerformanceMonitorC6sharedACvgZ", "_$s11AugmentCore17SecurityViolationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore12StorageIssueOMa", "_$s11AugmentCore0A13ConfigurationC30fileMonitoringThrottleIntervalSdvg", "_$s11AugmentCore13SpaceSettingsV15maxStorageBytess5Int64VvpMV", "_$s11AugmentCore13StorageConfigV24defaultMaxVersionAgeDaysSivpMV", "_$s11AugmentCore17SnapshotSchedulerC11addScheduleyyAA0cF0VFTj", "_$s11AugmentCore19SecurityAuditReportVMa", "_$s11AugmentCore0A13ConfigurationCACycfc", "_$s11AugmentCore14RiskAssessmentV07overallC004highC6Events06mediumcG003lowcG011riskFactorsAcA0C5LevelO_S3iSaySSGtcfC", "_$s11AugmentCore13SpaceSettingsV27storageNotificationsEnabledSbvg", "_$s11AugmentCore17FileAccessSummaryVSeAAMc", "_$s11AugmentCore19DependencyContainerC18performanceMonitorAA011PerformanceF0CyFTj", "_$s11AugmentCore14SecurityConfigV19auditLoggingEnabled26securityEventRetentionDays020fileAccessValidationG0010encryptionG00H12ScanIntervalACSb_SiS2bSitcfC", "_$s11AugmentCore13PreviewEngineCMo", "_$s11AugmentCore13CleanupResultV6errorsSays5Error_pGvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC9createdAt10Foundation4DateVvpMV", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvpMV", "_$s11AugmentCore13ErrorHandlingTL", "_$s11AugmentCore0A13ConfigurationC7networkAA13NetworkConfigVvsTj", "_$s11AugmentCore0A6LoggerC5debug_8category4file8function4lineySS_AC11LogCategoryOS2SSitFTj", "_$s11AugmentCore12SearchConfigVMa", "_$s11AugmentCore14SecurityConfigV27fileAccessValidationEnabledSbvs", "_$s11AugmentCore18PerformanceMonitorCMm", "_$s11AugmentCore18PreferencesManagerCMn", "_$s11AugmentCore22SecurityRecommendationV8PriorityOSeAAMc", "_$s11AugmentCore20ErrorRecoveryManagerC19clearResolvedErrorsyyFTq", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV19averageResponseTimeSdvg", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV17enableTaskHistorySbvg", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvsTq", "_$s11AugmentCore16SnapshotScheduleV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore12ConflictTypeO8allCasesSayACGvgZ", "_$s11AugmentCore17ErrorHistoryEntryV10resolvedAt10Foundation4DateVSgvM", "_$s11AugmentCore17FileSystemMonitorC15startMonitoringyyFTq", "_$s11AugmentCore0A5ErrorO12fileTooLargeyACSS_s5Int64VAFtcACmFWC", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore22withAsyncErrorHandling7context7handler11autoRecover9operationxSgSS_AA0eF0_pSbxyYaKXEtYalF", "_$s11AugmentCore15SnapshotManagerCN", "_$s11AugmentCore13FolderVersionV2id10Foundation4UUIDVvg", "_$s11AugmentCore15MetadataManagerC6logger17encryptionEnabledAcA0A6LoggerC_SbtcfC", "_$s11AugmentCore0A5ErrorO19insufficientStorageyACs5Int64V_AFtcACmFWC", "_$s11AugmentCore12SnapshotFileV10CodingKeysOs0E3KeyAAMc", "_$s11AugmentCore19MemoryPressureLevelOMn", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvgTj", "_$s11AugmentCore13SyncFrequencyOSEAAMc", "_$s11AugmentCore26FileSystemMonitoringConfigV18maxMonitoredSpacesSivg", "_$s11AugmentCore17PerformanceConfigV13fileBatchSizeSivpMV", "_$s11AugmentCore27ConflictResolutionUtilitiesCACycfCTq", "_$s11AugmentCore12SearchConfigV20indexRebuildIntervalSivg", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO9hashValueSivg", "_$s11AugmentCore13StoragePolicyVMn", "_$s11AugmentCore18SecurityAuditEntryV11descriptionSSvg", "_$s11AugmentCore14VersionControlC010createFileC08filePath7commentAA0fC0VSg10Foundation3URLV_SSSgtFTj", "_$s11AugmentCore21SecurityAuditReporterC12exportReport_6format2toSbAA0cdG0V_AA12ExportFormatO10Foundation3URLVtFTj", "_$s11AugmentCore13SpaceSettingsV21cleanupFrequencyHoursSivg", "_$s11AugmentCore13CleanupResultVMa", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigVN", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV11tasksByTypeSDyAC04TaskI0OSiGvpMV", "_$s11AugmentCore14DataEncryptionC0D5ErrorO16errorDescriptionSSSgvpMV", "_$s11AugmentCore8SnapshotV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore15ConflictManagerC7Combine16ObservableObjectAAMc", "_$s11AugmentCore15SnapshotManagerC11getSchedule9spacePathAA0cF0VSg10Foundation3URLV_tFTj", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV9timestamp11memoryUsage03cpuH004diskH016activeOperations19averageResponseTimeAE10Foundation4DateV_s6UInt64VSdAPSiSdtcfC", "_$s11AugmentCore15SecurityManagerC6loggerAcA0A6LoggerC_tcfCTq", "_$s11AugmentCore14SecurityConfigV26securityEventRetentionDaysSivM", "_$ss6ResultO11AugmentCores5Error_pRs_rlE06handleD04with7context11autoRecoveryAC0D8Handling_p_SSSgSbtF", "_$s11AugmentCore0A6LoggerC11measureTime_8category4file8function4line5blockxSS_AC11LogCategoryOS2SSixyKXEtKlFTq", "_$s11AugmentCore15BackupFrequencyO8rawValueSSvpMV", "_$s11AugmentCore12ConflictTypeOs12CaseIterableAAMc", "_$s11AugmentCore16LargeFileHandlerC04copycD04from2to08progressE0010completionE0y10Foundation3URLV_AKySd_SStcys6ResultOyAA0cd9OperationM0VAA0A5ErrorOGctFTj", "_$s11AugmentCore17SnapshotFrequencyO5dailyyA2CmFWC", "_$s11AugmentCore16SnapshotScheduleV9retentionAA0C9RetentionOvpMV", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA2_", "_$s11AugmentCore11NetworkSyncC16addConfigurationySbAA0dF0VFTq", "_$s11AugmentCore16RecoveryStrategyO10runCleanupyA2CmFWC", "_$s11AugmentCore11FileVersionV10CodingKeysOSQAAMc", "_$s11AugmentCore0A13ConfigurationC7$search7Combine9PublishedV9PublisherVyAA12SearchConfigV_GvsTj", "_$s11AugmentCore13ErrorHandlingP03logC0_7contextys0C0_p_SSSgtFTj", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutesSivM", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvsTj", "_$s11AugmentCore11FileVersionV2id10Foundation4UUIDVvg", "_$s11AugmentCore20ErrorRecoveryManagerC03getC7HistorySayAA0cG5EntryVGyFTj", "_$s11AugmentCore13SpaceSettingsV18autoCleanupEnabledSbvM", "_$s11AugmentCore0A13ConfigurationCMa", "_$s11AugmentCore0A6LoggerC17setConsoleLogging7enabledySb_tFTj", "_$s11AugmentCore14VersionControlC11getVersions8filePathSayAA04FileC0VG10Foundation3URLV_tFTj", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvsTq", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvsTq", "_$s11AugmentCore13DiffOperationV0D4TypeOSYAAMc", "_$s11AugmentCore0A5ErrorO26fileSystemMonitoringFailedyACs0C0_p_tcACmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV8strategy015enableAutomaticD020optimizationInterval23memoryPressureThresholdAeC0D8StrategyO_SbS2dtcfC", "_$s11AugmentCore19BackupConfigurationV9retentionAA0C9RetentionOvg", "_$s11AugmentCore8FileTypeO8rawValueSSvg", "_$s11AugmentCore8FileItemVs12IdentifiableAAMc", "_$s11AugmentCore13FileOperationO8rawValueSSvg", "_$s11AugmentCore15SecurityManagerCMo", "_$s11AugmentCore13LoggingConfigV04fileC7Enabled07consolecF016logRetentionDays14maxLogFileSize07defaultL5Level011performancecF0ACSb_SbSis5Int64VSSSbtcfcfA0_", "_$s11AugmentCore19DependencyContainerC13memoryManagerAA06MemoryF0CyFTq", "_$s11AugmentCore22SecurityRecommendationVMn", "_$s11AugmentCore13SecurityEventO20resourceAccessFailedyACSS_tcACmFWC", "_$s11AugmentCore16LargeFileHandlerCN", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysOSYAAMc", "_$s11AugmentCore18PreferencesManagerC25$storageManagementEnabled7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore16LargeFileHandlerC04copycD04from2to08progressE0010completionE0y10Foundation3URLV_AKySd_SStcys6ResultOyAA0cd9OperationM0VAA0A5ErrorOGctFTq", "_$s11AugmentCore19VersionControlErrorO20localizedDescriptionSSvg", "_$s11AugmentCore23SimpleSyncConfigurationV9authTokenSSvg", "_$s11AugmentCore12SearchResultV9hashValueSivpMV", "_$s11AugmentCore8FileItemV4pathSSvg", "_$s11AugmentCore11FileVersionV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore22SecurityRecommendationV8PriorityO3lowyA2EmFWC", "_$s11AugmentCore17SecurityViolationV8severityAC8SeverityOvg", "_$s11AugmentCore19UserInterfaceConfigV19defaultWindowHeightSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC2id10Foundation4UUIDVvpMV", "_$s11AugmentCore19BackupConfigurationV10backupPath10Foundation3URLVvpMV", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore0A6LoggerC8LogLevelOSYAAMc", "_$s11AugmentCore16RecoveryStrategyO21adjustStorageSettingsyA2CmFWC", "_$s11AugmentCore19MemoryUsageSnapshotV08physicalC5Totals6UInt64Vvg", "_$s11AugmentCore14RiskAssessmentVN", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC6statusAE0F6StatusOvpMV", "_$s11AugmentCore23SimpleSyncConfigurationVSEAAMc", "_$s11AugmentCore15SnapshotManagerC07restoreC08snapshotSbAA0C0V_tFTj", "_$s11AugmentCore27ConflictResolutionUtilitiesC21hasPotentialConflicts8filePath05spaceJ0Sb10Foundation3URLV_AItFZ", "_$s11AugmentCore0A5ErrorO10syncFailedyACSS_s0C0_pSgtcACmFWC", "_$s11AugmentCore13FolderVersionV10folderPath10Foundation3URLVvg", "_$s11AugmentCore17FileSystemMonitorC15startMonitoring9spacePath8callbackSb10Foundation3URLV_yAI_AA0cD5EventOtctFTq", "_$s11AugmentCore14DataEncryptionC0D5ErrorO16errorDescriptionSSSgvg", "_$s11AugmentCore10DiffEngineCMu", "_$s11AugmentCore19NotificationManagerC20notificationsEnabledSbvsTq", "_$s11AugmentCore12SearchResultVs12IdentifiableAAMc", "_$s11AugmentCore17ErrorHistoryEntryV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore0A5SpaceV11createdDate10Foundation0E0VvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodOSHAAMc", "_$s11AugmentCore0A6LoggerC11LogCategoryO8rawValueSSvpMV", "_$s11AugmentCore12SearchResultV8filePath10Foundation3URLVvg", "_$s11AugmentCore14StorageManagerC12getDiskUsage3forAA0C4InfoVAA0A5SpaceV_tFTq", "_$s11AugmentCore14VersionControlC010initializecD010folderPathSb10Foundation3URLV_tFTq", "_$s11AugmentCore18PreferencesManagerC19$autoCleanupEnabled7Combine9PublishedV9PublisherVySb_GvgTq", "_$s11AugmentCore17SyncConfigurationVSHAAMc", "_$s11AugmentCore14DataEncryptionCACycfCTj", "_$s11AugmentCore8DiffTypeO8rawValueSSvg", "_$s11AugmentCore25MemoryOptimizationServiceC19updateConfigurationyyAC0D6ConfigVFTj", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvMTq", "_$s11AugmentCore18PerformanceMonitorC20performMemoryCleanupyyFTj", "_$s11AugmentCore18PreferencesManagerC21cleanupFrequencyHoursSivgTq", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvMTj", "_$s11AugmentCore15SecurityManagerC013stopAccessingC14ScopedResourceyy10Foundation3URLVFTq", "_$s11AugmentCore19DependencyContainerCMa", "_$s11AugmentCore13NetworkConfigV16maxRetryAttemptsSivg", "_$s11AugmentCore13DiffOperationV0D4TypeO9unchangedyA2EmFWC", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvsTj", "_$s11AugmentCore17ErrorHistoryEntryV10isResolvedSbvs", "_$s11AugmentCore22FileSystemMonitorErrorO012memoryAccessF0yACSScACmFWC", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvgTj", "_$s11AugmentCore21SecurityAuditReporterC15securityManager6loggerAcA0cG0C_AA0A6LoggerCtcfCTj", "_$s11AugmentCore0A5SpaceVSHAAMc", "_$s11AugmentCore13BackupManagerC06createC09spacePathSb10Foundation3URLV_tFTj", "_$s11AugmentCore8FileItemVSQAAMc", "_$s11AugmentCore14SecurityConfigVMn", "_$s11AugmentCore0A5SpaceVMn", "_$s11AugmentCore14VersionControlC15metadataManagerAcA08MetadataF0C_tcfCTj", "_$s11AugmentCore12FileConflictVSEAAMc", "_$s11AugmentCore13LoggingConfigV07consoleC7EnabledSbvs", "_$s11AugmentCore0A6LoggerC18setMinimumLogLevelyyAC0fG0OFTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoringSbvM", "_$s11AugmentCore19SecurityAuditReportVSEAAMc", "_$s11AugmentCore19VersionControlErrorO07invalidC0yACSScACmFWC", "_$s11AugmentCore22SecurityRecommendationVN", "_$s11AugmentCore25MemoryOptimizationServiceC013stopAutomaticD0yyFTq", "_$s11AugmentCore8DiffTypeOSEAAMc", "_$s11AugmentCore13CleanupResultV10freedBytess5Int64VvpMV", "_$s11AugmentCore14VersionControlC010createFileC5Async8filePath7commentAA0fC0VSg10Foundation3URLV_SSSgtYaFTq", "_$s11AugmentCore19SecurityAuditReportV9startDate10Foundation0G0VSgvg", "_$s11AugmentCore0A5ErrorO28securityScopedBookmarkFailedyACSS_tcACmFWC", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvsTq", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvsTj", "_$s11AugmentCore18ConflictResolutionO8rawValueSSvpMV", "_$s11AugmentCore13ErrorCategoryOSEAAMc", "_$s11AugmentCore13SecurityEventO21resourceAccessStoppedyACSS_tcACmFWC", "_$s11AugmentCore17SecurityViolationV11descriptionSSvpMV", "_$s11AugmentCore8FileTypeO5color7SwiftUI5ColorVvpMV", "_$s11AugmentCore0A13ConfigurationC20fileSystemMonitoringAA04FileeF6ConfigVvsTj", "_$s11AugmentCore0A5ErrorO13isRecoverableSbvpMV", "_$s11AugmentCore19DependencyContainerC6inject13configurationyAA0A13ConfigurationC_tFTq", "_$s11AugmentCore15SecurityManagerCMu", "_$s11AugmentCore20ErrorRecoveryManagerC19$recoveryInProgress7Combine9PublishedV9PublisherVySb_GvMTq", "_$s11AugmentCore0A13ConfigurationCACycfC", "_$s11AugmentCore9RiskLevelOSEAAMc", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO11stringValueSSvpMV", "_$s11AugmentCore13SecurityEventOMa", "_$s11AugmentCore16SnapshotScheduleV9isEnabledSbvs", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvMTq", "_$s11AugmentCore13StoragePolicyV4type7enabled16warningThresholdA2C0D4TypeO_SbSdtcfC", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigVMn", "_$s11AugmentCore13SyncFrequencyOSeAAMc", "_$s11AugmentCore13BackupManagerCfd", "_$s11AugmentCore13NetworkConfigVACycfC", "_$s11AugmentCore11StorageInfoV20augmentDirectorySizes5Int64VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsVMa", "_$s11AugmentCore18PerformanceMonitorC12measureAsync_5blockxSS_xyYaKXEtYaKlFTj", "_$s11AugmentCore13ErrorHandlingP06handleC0_7context11autoRecoverys0C0_p_SSSgSbtFTq", "_$s11AugmentCore11FileItemRowVMa", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfC", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsV7successSbSgvpMV", "_$s11AugmentCore12SnapshotFileV10CodingKeysO8intValueSiSgvpMV", "_$s11AugmentCore13ErrorSeverityO3lowyA2CmFWC", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCfd", "_$s11AugmentCore0A6LoggerC11LogCategoryO7networkyA2EmFWC", "_$s11AugmentCore13LoggingConfigV14maxLogFileSizes5Int64VvpMV", "_$s11AugmentCore0A13ConfigurationC6searchAA12SearchConfigVvgTq", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore13SpaceSettingsV22snapshotFrequencyHoursSivpMV", "_$s11AugmentCore13StorageConfigV29notificationsEnabledByDefaultSbvs", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvMTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV07minimumC10SizeChanges5Int64VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV15tasksByPrioritySDyAC04TaskI0OSiGvpMV", "_$s11AugmentCore17FileSystemMonitorC22configureSaveDetectionyyAC0gH6ConfigVFTj", "_$s11AugmentCore0A5SpaceV4nameSSvs", "_$s11AugmentCore13BackupManagerC6sharedACvpZMV", "_$s11AugmentCore0A5ErrorO29configurationValidationFailedyACSaySSG_tcACmFWC", "_$s11AugmentCore22withAsyncErrorHandling7context7handler11autoRecover9operationxSgSS_AA0eF0_pSbxyYaKXEtYalFTu", "_$s11AugmentCore16RecoveryStrategyOMa", "_$s11AugmentCore17FileSystemMonitorC13memoryManagerAA06MemoryG0CvpMV", "_$s11AugmentCore17CancellationErrorV16errorDescriptionSSSgvpMV", "_$s11AugmentCore18PreferencesManagerC18autoCleanupEnabledSbvsTj", "_$s11AugmentCore18LargeFileOperationOMa", "_$s11AugmentCore19NotificationManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvpMV", "_$s11AugmentCore16RecoverableErrorV08originalD0s0D0_pvg", "_$s11AugmentCore0A5SpaceV2eeoiySbAC_ACtFZ", "_$s11AugmentCore22SecurityRecommendationV8priorityAC8PriorityOvg", "_$s11AugmentCore27ConflictResolutionUtilitiesCMu", "_$s11AugmentCore16SnapshotScheduleV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore16LargeFileHandlerCMn", "_$s11AugmentCore13NetworkConfigV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvsTq", "_$s11AugmentCore13PreviewEngineCN", "_$s11AugmentCore25MemoryOptimizationServiceC0D8StrategyO4hash4intoys6HasherVz_tF", "_$s11AugmentCore0A6LoggerCMo", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvgTj", "_$s11AugmentCore14DataEncryptionCfD", "_$s11AugmentCore22SecurityRecommendationV6actionSSvpMV", "_$s11AugmentCore16LargeFileHandlerCfd", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV19totalTasksProcessed12tasksInQueue07runningH007averageD4Time11successRate0J6ByType0jR8PriorityAESi_S2iS2dSDyAC04TaskS0OSiGSDyAC0uT0OSiGtcfC", "_$s11AugmentCore0A5SpaceV4path10Foundation3URLVvg", "_$s11AugmentCore19MemoryPressureLevelOMa", "_$s11AugmentCore8FileItemV4nameSSvs", "_$s11AugmentCore0A5ErrorO15fsEventsFailureyACSS_tcACmFWC", "_$s11AugmentCore17SnapshotSchedulerC9schedulesSayAA0C8ScheduleVGvs", "_$s11AugmentCore20ErrorRecoveryManagerC18recoveryInProgressSbvMTq", "_$s11AugmentCore13DiffOperationV0D4TypeO8rawValueSSvg", "_$s11AugmentCore19DependencyContainerC010createTestD0ACyFZ", "_$s11AugmentCore27BackgroundProcessingServiceC10cancelTask6taskIdSb10Foundation4UUIDV_tFTq", "_$s11AugmentCore18ErrorHandlingUtilsCfd", "_$s11AugmentCore15MetadataManagerC6sharedACvgZ", "_$s11AugmentCore16LargeFileHandlerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceH0Ctcfc", "_$s11AugmentCore18PreferencesManagerC21autoVersioningEnabledSbvMTq", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV07averageD4TimeSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO8allCasesSayAEGvgZ", "_$s11AugmentCore22SecurityRecommendationVSEAAMc", "_$s11AugmentCore12ConflictTypeOSQAAMc", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvgTj", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvgTj", "_$s11AugmentCore17SnapshotSchedulerCACycfC", "_$s11AugmentCore13StorageConfigV27autoCleanupEnabledByDefaultSbvM", "_$s11AugmentCore12ExportFormatOSQAAMc", "_$s11AugmentCore14VersionControlC6sharedACvgZ", "_$s11AugmentCore19NotificationManagerCMo", "_$s11AugmentCore19NotificationManagerCfD", "_$s11AugmentCore18PerformanceMonitorC16OperationMetricsVN", "_$s11AugmentCore25MemoryOptimizationServiceC014startAutomaticD0yyFTq", "_$s11AugmentCore26FileSystemMonitoringConfigV26throttlingCleanupThresholdSivs", "_$s11AugmentCore12SearchResultV8filePath10Foundation3URLVvpMV", "_$s11AugmentCore13DiffOperationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore8SnapshotV11descriptionSSSgvg", "_$s11AugmentCore14RiskAssessmentV07overallC0AA0C5LevelOvpMV", "_$s11AugmentCore8FileItemV25formattedModificationDateSSvpMV", "_$s11AugmentCore11FileVersionV8filePath10Foundation3URLVvg", "_$s11AugmentCore12ConflictTypeO8rawValueSSvpMV", "_$s11AugmentCore17PerformanceConfigV13fileBatchSize21uiUpdatePauseInterval22memoryCleanupThreshold015emergencyMemorymN003maxc7HistoryG00Q19CompletedOperations0l7WarningN003cpuuN0012responseTimeuN0010monitoringK0ACSi_SdS4is5Int64VS3dtcfcfA6_", "_$s11AugmentCore0A6LoggerC11LogCategoryO9subsystemSSvg", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvgTq", "_$s11AugmentCore18ErrorHandlingUtilsCfD", "_$s11AugmentCore23SimpleSyncConfigurationV4fromACs7Decoder_p_tKcfC", "_$s11AugmentCore13StorageConfigV18monitoringIntervalSdvpMV", "_$s11AugmentCore14VersionControlC15metadataManagerAcA08MetadataF0C_tcfC", "_$s11AugmentCore13LoggingConfigV07consoleC7EnabledSbvg", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvgTq", "_$s11AugmentCore8DiffTypeOSeAAMc", "_$s11AugmentCore17SecurityViolationV8SeverityO8rawValueAESgSS_tcfC", "_$s11AugmentCore17FileAccessSummaryV05totalcD0Sivg", "_$s11AugmentCore13CleanupResultV10freedBytess5Int64Vvg", "_$s11AugmentCore16SnapshotScheduleV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore19UserInterfaceConfigV26animationsEnabledByDefaultSbvpMV", "_$s11AugmentCore13BackupManagerCMu", "_$s11AugmentCore13SyncDirectionOSHAAMc", "_$s11AugmentCore18PreferencesManagerC17maxVersionAgeDaysSivMTq", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvsTj", "_$s11AugmentCore8FileItemV3url10Foundation3URLVvg", "_$s11AugmentCore0A6LoggerC6sharedACvgZ", "_$s11AugmentCore13ErrorSeverityO8allCasesSayACGvpZMV", "_$s11AugmentCore14DataEncryptionC7encrypty10Foundation0C0VxKSeRzSERzlFZ", "_$s11AugmentCore17FileSystemMonitorC19saveDetectionConfigAC04SavegH0VvgTq", "_$s11AugmentCore17SecurityViolationV6userIdSSvg", "_$s11AugmentCore12ConflictTypeON", "_$s11AugmentCore19MemoryUsageSnapshotV07virtualC4Useds6UInt64Vvg", "_$s11AugmentCore0A5ErrorO16errorDescriptionSSSgvg", "_$s11AugmentCore19NotificationManagerC27cleanupNotificationsEnabledSbvgTq", "_$s11AugmentCore19UserInterfaceConfigV15refreshIntervalSdvs", "_$s11AugmentCore8FileTypeO10systemIconSSvg", "_$s11AugmentCore15SecurityManagerCMa", "_$s11AugmentCore18PreferencesManagerC24$storageWarningThreshold7Combine9PublishedV9PublisherVySd_GvgTq", "_$s11AugmentCore0A5SpaceV4nameSSvM", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvMTj", "_$s11AugmentCore0A5SpaceV4hash4intoys6HasherVz_tF", "_$s11AugmentCore0A5SpaceVs12IdentifiableAAMc", "_$s11AugmentCore14DataEncryptionC0D5ErrorOs0E0AAMc", "_$s11AugmentCore16LargeFileHandlerC6logger18performanceMonitorAcA0A6LoggerC_AA011PerformanceH0CtcfCTq", "_$s11AugmentCore19UserInterfaceConfigV18defaultWindowWidth0fG6Height03mingH00jgI017animationDuration15refreshInterval12maxListItems26animationsEnabledByDefaultACSd_S5dSiSbtcfcfA4_", "_$s11AugmentCore8FileTypeO8allCasesSayACGvgZ", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO11stringValueSSvg", "_$s11AugmentCore18LargeFileOperationO8rawValueSSvpMV", "_$s11AugmentCore19DependencyContainerC12searchEngineAA06SearchF0CyFTq", "_$s11AugmentCore30DefaultVersionCreationDelegateCACycfC", "_$s11AugmentCore14RiskAssessmentV11riskFactorsSaySSGvg", "_$s11AugmentCore13SyncDirectionOMa", "_$s11AugmentCore0A6LoggerC16measureTimeAsync_8category4file8function4line5blockxSS_AC11LogCategoryOS2SSixyYaKXEtYaKlFTq", "_$s11AugmentCore13SyncFrequencyOSHAAMc", "_$s11AugmentCore15ConflictManagerCACycfCTq", "_$s11AugmentCore20ErrorRecoveryManagerC09isShowingC6DialogSbvMTj", "_$s11AugmentCore13StoragePolicyV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore25MemoryOptimizationServiceC0D5StatsV012memoryBeforeD0s6UInt64VvpMV", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvMTj", "_$s11AugmentCore18PreferencesManagerC24storageManagementEnabledSbvpMV", "_$s11AugmentCore17FileSystemMonitorC30enableLightweightSaveDetectionyyF", "_$s11AugmentCore12SnapshotFileV4sizes6UInt64VvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsVMn", "_$s11AugmentCore8FileItemV6existsSbvpMV", "_$s11AugmentCore18ConflictResolutionOSHAAMc", "_$s11AugmentCore23PolicyEnforcementResultO7warningyACSScACmFWC", "_$s11AugmentCore8SnapshotV9spacePath10Foundation3URLVvpMV", "_$s11AugmentCore13SpaceSettingsVMn", "_$s11AugmentCore18PreferencesManagerC28$storageNotificationsEnabled7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore17PerformanceConfigV22maxCompletedOperationsSivpMV", "_$s11AugmentCore17PerformanceConfigV28responseTimeWarningThresholdSdvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC8TaskTypeO7cleanupyA2EmFWC", "_$s11AugmentCore14SecurityConfigV17encryptionEnabledSbvM", "_$s11AugmentCore11FileVersionV10CodingKeysO8rawValueSSvpMV", "_$s11AugmentCore17FileSystemMonitorC24unsuppressAutoVersioning3fory10Foundation3URLV_tFTq", "_$s11AugmentCore13StorageConfigV16defaultMaxSizeGB0E16WarningThreshold0eF14VersionAgeDays0E21CleanupFrequencyHours18monitoringInterval04autoN16EnabledByDefault013notificationstuV0ACSd_SdS2iSdS2btcfcfA0_", "_$s11AugmentCore0A13ConfigurationC11performanceAA17PerformanceConfigVvMTq", "_$s11AugmentCore14DataEncryptionCN", "_$s11AugmentCore14StorageManagerCN", "_$s11AugmentCore19NotificationManagerC21$notificationsEnabled7Combine9PublishedV9PublisherVySb_GvsTq", "_$s11AugmentCore15SnapshotManagerC06createC09spacePath4name11descriptionAA0C0VSg10Foundation3URLV_S2SSgtFTq", "_$s11AugmentCore14VersionControlC15metadataManagerAcA08MetadataF0C_tcfCTq", "_$s11AugmentCore17SecurityViolationV11descriptionSSvg", "_$s11AugmentCore13SpaceSettingsV2eeoiySbAC_ACtFZ", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC11isCompletedSbvgTj", "_$s11AugmentCore8FileDiffV9toVersionAA0cF0VvpMV", "_$s11AugmentCore17CancellationErrorVMn", "_$s11AugmentCore18PreferencesManagerC06exportC0SDySSypGyF", "_$s11AugmentCore0A5ErrorO16permissionDeniedyACSS_SStcACmFWC", "_$s11AugmentCore0A5ErrorO8categoryAA0C8CategoryOvpMV", "_$s11AugmentCore0A13ConfigurationC7Combine16ObservableObjectAAMc", "_$s11AugmentCore13SpaceSettingsV21monitorSubdirectoriesSbvM", "_$s11AugmentCore19NotificationManagerC27storageNotificationsEnabledSbvMTj", "_$s11AugmentCore13MemoryManagerC14stopMonitoringyyFTj", "_$s11AugmentCore16RecoveryStrategyO15restoreDefaultsyA2CmFWC", "_$s11AugmentCore16RecoveryStrategyO4iconSSvpMV", "_$s11AugmentCore0A6LoggerC11LogCategoryO13errorRecoveryyA2EmFWC", "_$s11AugmentCore8FileItemV2id4name4path4type16modificationDate12versionCount4size12hasConflicts9isSyncingAC10Foundation4UUIDV_S2SAA0C4TypeOAM0J0VSis5Int64VS2btcfC", "_$s11AugmentCore14VersioningModeOSeAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0D5StatsV19totalTasksProcessedSivg", "_$s11AugmentCore9RiskLevelO4highyA2CmFWC", "_$s11AugmentCore18ConfigurationErrorO10Foundation09LocalizedD0AAMc", "_$s11AugmentCore18ConflictResolutionOs12CaseIterableAAMc", "_$s11AugmentCore18PreferencesManagerC23storageWarningThresholdSdvMTq", "_$s11AugmentCore19UserInterfaceConfigV15minWindowHeightSdvg", "_$s11AugmentCore19NotificationManagerC18sendStorageWarning9spaceName10percentageySS_SdtFTq", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCMn", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskC0F6StatusO7runningyA2GmFWC", "_$s11AugmentCore15BackupRetentionOSEAAMc", "_$s11AugmentCore13BackupManagerCACycfCTj", "_$s11AugmentCore13SyncDirectionOSQAAMc", "_$s11AugmentCore26FileSystemMonitoringConfigV16throttleIntervalSdvs", "_$s11AugmentCore19DependencyContainerC13backupManagerAA06BackupF0CyFTj", "_$s11AugmentCore0A13ConfigurationC04saveC0yyFTj", "_$s11AugmentCore0A13ConfigurationC9$security7Combine9PublishedV9PublisherVyAA14SecurityConfigV_GvgTq", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvMTj", "_$s11AugmentCore13MemoryManagerC03getC12UsageHistorySayAA0cF8SnapshotVGyFTq", "_$s11AugmentCore16SnapshotScheduleVSEAAMc", "_$s11AugmentCore27BackgroundProcessingServiceC0C4TaskCN", "_$s11AugmentCore26FileSystemMonitoringConfigV25throttlingEntryExpirationSdvM", "_$s11AugmentCore0A5ErrorO20configurationMissingyACSS_tcACmFWC", "_$s11AugmentCore15MetadataManagerC6logger17encryptionEnabledAcA0A6LoggerC_Sbtcfc", "_$s11AugmentCore19UserInterfaceConfigV26animationsEnabledByDefaultSbvs", "_$s11AugmentCore30DefaultVersionCreationDelegateCMn", "_$s11AugmentCore13NetworkConfigV27defaultSyncFrequencyMinutes14networkTimeout16maxRetryAttempts10retryDelay20syncEnabledByDefault0K10UploadSizeACSi_SdSiSdSbs5Int64VtcfcfA_", "_$s11AugmentCore19BackupConfigurationV10CodingKeysO8rawValueAESgSS_tcfC", "_$s11AugmentCore8FileItemV9typeColor7SwiftUI0F0Vvg", "_$s11AugmentCore11StorageInfoVMn", "_$s11AugmentCore19SecurityAuditReportV12eventsByTypeSDySSSiGvg", "_$s11AugmentCore13SpaceSettingsV13autoSnapshotsSbvs", "_$s11AugmentCore13StoragePolicyV0D4TypeO7maxSizeyAEs5Int64VcAEmFWC", "_$s11AugmentCore19SecurityAuditReportV11generatedAt9startDate03endI011totalEvents12eventsByType18securityViolations17fileAccessSummary14riskAssessment15recommendationsAC10Foundation0I0V_AOSgAPSiSDySSSiGSayAA0C9ViolationVGAA04FilesT0VAA04RiskV0VSayAA0C14RecommendationVGtcfC", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvMTj", "_$s11AugmentCore0A6LoggerC8LogLevelO8allCasesSayAEGvpZMV", "_$s11AugmentCore8LoggableMp", "_$s11AugmentCore13FileOperationOMa", "_$s11AugmentCore27ConflictResolutionUtilitiesCMa", "_$s11AugmentCore17PerformanceConfigV22memoryWarningThresholds5Int64Vvs", "_$s11AugmentCore0A5SpaceVSeAAMc", "_$s11AugmentCore17FileSystemMonitorC23versionCreationDelegateAA07VersiongH0_pSgvgTj", "_$s11AugmentCore27BackgroundProcessingServiceC0D6ConfigV17enableTaskHistorySbvpMV", "_$s11AugmentCore27BackgroundProcessingServiceC10submitTask4name4type8priority8workItemxSS_AC0G4TypeOAC0G8PriorityOSgxyYaKctYaKlFTj", "_$s11AugmentCore8FileItemVSLAAMc", "_$s11AugmentCore18PerformanceMonitorC0C7MetricsV19averageResponseTimeSdvpMV", "_$s11AugmentCore25MemoryOptimizationServiceCMo", "_$s11AugmentCore13SyncDirectionO13remoteToLocalyA2CmFWC", "_$s11AugmentCore12SearchResultV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore13BackupManagerC06createC09spacePathSb10Foundation3URLV_tFTq", "_$s11AugmentCore8DiffTypeOMa", "_$s11AugmentCore17FileSystemMonitorC11SpaceConfigV7isValidSbvg", "_$s11AugmentCore17SyncConfigurationV10CodingKeysO8intValueSiSgvg", "_$s11AugmentCore20ErrorRecoveryManagerCfd", "_$s11AugmentCore15SecurityManagerC6sharedACvpZMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionMethodO9hashValueSivpMV", "_$s11AugmentCore12SearchConfigV10maxResults12indexTimeout14minQueryLength0ejK018relevanceThreshold0G15RebuildInterval31contentIndexingEnabledByDefaultACSi_SdS2iSdSiSbtcfC", "_$s11AugmentCore13FileOperationOMn", "_$s11AugmentCore20ErrorRecoveryManagerC08$currentC07Combine9PublishedV9PublisherVyAA011RecoverableC0VSg_GvsTj", "_$s11AugmentCore13SyncFrequencyOSYAAMc", "_$s11AugmentCore18PreferencesManagerC13$maxStorageGB7Combine9PublishedV9PublisherVySd_GvgTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV22enableContentHashCheckSbvM", "_$s11AugmentCore19DependencyContainerC6inject17fileSystemMonitoryAA04FilegH0C_tFTq", "_$s11AugmentCore0A5ErrorO010fileSystemC09operation4path10underlyingACSS_SSs0C0_ptFZ", "_$s11AugmentCore13FolderVersionV6encode2toys7Encoder_p_tKF", "_$s11AugmentCore19DependencyContainerCfd", "_$s11AugmentCore17FileSystemMonitorC16debounceIntervalSdvgTq", "_$s11AugmentCore8LoggableTL", "_$s11AugmentCore13SpaceSettingsV18networkSyncEnabledSbvg", "_$s11AugmentCore13NetworkConfigV16maxRetryAttemptsSivM", "_$s11AugmentCore13PreviewEngineCMu", "_$s11AugmentCore13StorageConfigV24defaultMaxVersionAgeDaysSivg", "_$s11AugmentCore12SearchConfigV20indexRebuildIntervalSivpMV", "_$s11AugmentCore17SecurityViolationV8SeverityOSHAAMc", "_$s11AugmentCore13BackupManagerC07restoreC010backupPath05spaceG08password10completiony10Foundation3URLV_AKSSSgySbctFTj", "_$s11AugmentCore13StorageConfigV18monitoringIntervalSdvs", "_$s11AugmentCore14VersionControlC6sharedACvpZMV", "_$s11AugmentCore20ErrorRecoveryManagerC010$isShowingC6Dialog7Combine9PublishedV9PublisherVySb_GvgTj", "_$s11AugmentCore19NotificationManagerC22cancelAllNotificationsyyFTq", "_$s11AugmentCore13MemoryManagerCfD", "_$s11AugmentCore12FileConflictV2id10Foundation4UUIDVvpMV", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigV24enableKeyboardMonitoring18commandSTimeWindow07minimumC10SizeChange0I16ContentHashCheck04saveG7MethodsAESb_Sds5Int64VSbSayAC0fG6MethodOGtcfC", "_$s11AugmentCore17FileSystemMonitorC20startMonitoringAsync9spacePath8callbackSb10Foundation3URLV_yAI_AA0cD5EventOtYactFTj", "_$s11AugmentCore0A5ErrorO24backupVerificationFailedyACSS_SStcACmFWC", "_$s11AugmentCore0A13ConfigurationC19configureForTestingyyF", "_$s11AugmentCore17PerformanceConfigVSeAAMc", "_$s11AugmentCore17FileSystemMonitorC18performMaintenanceyyFTq", "_$s11AugmentCore0A6LoggerCfD", "_$s11AugmentCore0A13ConfigurationC8$logging7Combine9PublishedV9PublisherVyAA13LoggingConfigV_GvsTj", "_$s11AugmentCore13DiffOperationV0D4TypeOSeAAMc", "_$s11AugmentCore0A13ConfigurationC8$network7Combine9PublishedV9PublisherVyAA13NetworkConfigV_GvMTq", "_$s11AugmentCore15ConflictManagerC15activeConflictsSayAA04FileC0VGvMTj", "_$s11AugmentCore15BackupRetentionO11keepForTimeyACSdcACmFWC", "_$s11AugmentCore0A5SpaceV9hashValueSivpMV", "_$s11AugmentCore13DiffOperationV0D4TypeOMn", "_$s11AugmentCore17SnapshotSchedulerC5timer33_7E7566F25733DFECB0CF9BB3A350FCB8LLSo7NSTimerCSgvg", "_$s11AugmentCore17SyncConfigurationV9directionAA0C9DirectionOvg", "_$s11AugmentCore8FileTypeO8documentyA2CmFWC", "_$s11AugmentCore25MemoryOptimizationServiceC0D6ConfigV20optimizationIntervalSdvpMV", "_$s11AugmentCore13DiffOperationVSEAAMc", "_$s11AugmentCore17SyncConfigurationV9frequencyAA0C9FrequencyOvg", "_$s11AugmentCore13DiffOperationV0D4TypeOSQAAMc", "_$s11AugmentCore16LargeFileHandlerC02iscD0ySb10Foundation3URLVFTj", "_$s11AugmentCore17FileSystemMonitorC19SaveDetectionConfigVN", "_$s11AugmentCore0A13ConfigurationCN", "_$s11AugmentCore27BackgroundProcessingServiceCfD", "_$s11AugmentCore12SearchConfigV20indexRebuildIntervalSivM", "_$s11AugmentCore23SimpleSyncConfigurationVACycfC", "_$s11AugmentCore13MemoryManagerC14stopMonitoringyyFTq", "_$s11AugmentCore15ConflictManagerC16$activeConflicts7Combine9PublishedV9PublisherVySayAA04FileC0VG_GvMTq", "_$s11AugmentCore15ConflictManagerC18getActiveConflicts9spacePathSayAA04FileC0VG10Foundation3URLV_tFTj", "_$s11AugmentCore17FileAccessSummaryVMa", "_$s11AugmentCore12SearchConfigV31contentIndexingEnabledByDefaultSbvM", "_$s11AugmentCore15ErrorValidationCMu", "_$s11AugmentCore27BackgroundProcessingServiceCMm", "_$s11AugmentCore18SecurityAuditEntryV5eventSSvpMV", "_$s11AugmentCore19DependencyContainerC25memoryOptimizationServiceAA06MemoryfG0CyFTq", "_$s11AugmentCore0A13ConfigurationCfD", "_$s11AugmentCore15BackupFrequencyOSEAAMc", "_$s11AugmentCore13FileOperationO7versionyA2CmFWC", "_$s11AugmentCore0A13ConfigurationC8securityAA14SecurityConfigVvsTj", "_$s11AugmentCore11FileVersionV10CodingKeysO8intValueAESgSi_tcfC", "_$s11AugmentCore12SearchEngineC11searchAsync5query10spacePathsSayAA0C6ResultVGSS_Say10Foundation3URLVGtYaFTj", "_$s11AugmentCore20ErrorRecoveryManagerC12activeErrorsSayAA011RecoverableC0VGvpMV", "_$s11AugmentCore0A6LoggerCMa", "_$s11AugmentCore19DependencyContainerC6inject27backgroundProcessingServiceyAA010BackgroundgH0C_tFTq", "_$s11AugmentCore18PerformanceMonitorC5resetyyFTj", "_$s11AugmentCore23SimpleSyncConfigurationV20syncFrequencyMinutesSivg", "_$s11AugmentCore14StorageManagerCMn", "_$s11AugmentCore17PerformanceConfigV19cpuWarningThresholdSdvs", "_$s11AugmentCore12ConflictTypeOMa", "_$s11AugmentCore13ErrorHandlingPAAE06reportC0_7contextys0C0_p_SSSgtF", "_$s11AugmentCore26FileSystemMonitoringConfigVACycfC", "_$s11AugmentCore8FileTypeO8iconNameSSvg"], "objc_class": ["_TtC11AugmentCore19NotificationManager"]}}], "flags": [{"attributes": ["not_app_extension_safe"]}], "install_names": [{"name": "@rpath/AugmentCore.framework/Versions/A/AugmentCore"}], "swift_abi": [{"abi": 7}], "target_info": [{"min_deployment": "15.5", "target": "x86_64-macos"}, {"min_deployment": "15.5", "target": "arm64-macos"}]}, "tapi_tbd_version": 5}