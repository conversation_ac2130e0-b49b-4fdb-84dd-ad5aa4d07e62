{"client": {"name": "basic", "version": 0, "file-system": "device-agnostic", "perform-ownership-analysis": "no"}, "targets": {"": ["<all>"]}, "nodes": {"/Users/<USER>/Downloads/AugmentApp 2/build": {"is-mutated": true}, "/Users/<USER>/Downloads/AugmentApp 2/build/Debug": {"is-mutated": true}, "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A": {"is-mutated": true}, "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore": {"is-mutated": true}, "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug": {"is-mutated": true}, "<TRIGGER: CreateUniversalBinary /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore normal arm64\\ x86_64>": {"is-command-timestamp": true}, "<TRIGGER: MkDir /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A>": {"is-command-timestamp": true}}, "commands": {"<all>": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/_CodeSignature", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<target-AugmentCore-****************************************************************--begin-scanning>", "<target-AugmentCore-****************************************************************--end>", "<target-AugmentCore-****************************************************************--linker-inputs-ready>", "<target-AugmentCore-****************************************************************--modules-ready>", "<workspace-Debug--stale-file-removal>"], "outputs": ["<all>"]}, "<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/_CodeSignature", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist"], "roots": ["/tmp/Augment.dst", "/Users/<USER>/Downloads/AugmentApp 2/build", "/Users/<USER>/Downloads/AugmentApp 2/build"], "outputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>"]}, "<workspace-Debug--stale-file-removal>": {"tool": "stale-file-removal", "expectedOutputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"], "outputs": ["<workspace-Debug--stale-file-removal>"]}, "P0:::ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache": {"tool": "shell", "description": "ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "inputs": [], "outputs": ["/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-o", "/var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache"], "env": {}, "always-out-of-date": true, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2/Augment.xcodeproj", "signature": "b4e484cbd9a27baf2c1a0b9bfe2dafbc"}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build", "inputs": [], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "/Users/<USER>/Downloads/AugmentApp 2/build"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build/Debug", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug"]}, "P0:::CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug": {"tool": "create-build-directory", "description": "CreateBuildDirectory /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build"], "outputs": ["<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug"]}, "P0:::Gate WorkspaceHeaderMapVFSFilesWritten": {"tool": "phony", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"], "outputs": ["<WorkspaceHeaderMapVFSFilesWritten>"]}, "P0:::Gate target-AugmentCore-****************************************************************--AppIntentsMetadataTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList"], "outputs": ["<target-AugmentCore-****************************************************************--AppIntentsMetadataTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-ChangeAlternatePermissions": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-ChangePermissions>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-ChangeAlternatePermissions>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-ChangePermissions": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-StripSymbols>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-ChangePermissions>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-CodeSign": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<CodeSign /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-CodeSign>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-CopyAside": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-GenerateStubAPI>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-CopyAside>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-GenerateStubAPI": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<Eager Linking TBD Production /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-GenerateStubAPI>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-RegisterExecutionPolicyException": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-CodeSign>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-RegisterExecutionPolicyException>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-RegisterProduct": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-Validate>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<Touch /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-RegisterProduct>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-StripSymbols": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-CopyAside>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-StripSymbols>"]}, "P0:::Gate target-AugmentCore-****************************************************************--Barrier-Validate": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--Barrier-Validate>"]}, "P0:::Gate target-AugmentCore-****************************************************************--CopySwiftPackageResourcesTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--CopySwiftPackageResourcesTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--CustomTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--CustomTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--DocumentationTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--DocumentationTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--GeneratedFilesTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--GeneratedFilesTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ProductStructureTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--GeneratedFilesTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--HeadermapTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap"], "outputs": ["<target-AugmentCore-****************************************************************--HeadermapTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--InfoPlistTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist"], "outputs": ["<target-AugmentCore-****************************************************************--InfoPlistTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--ModuleMapTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--ModuleMapTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--RealityAssetsTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--ModuleMapTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-AugmentCore-****************************************************************--InfoPlistTaskProducer>", "<target-AugmentCore-****************************************************************--SanitizerTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-AugmentCore-****************************************************************--TestTargetTaskProducer>", "<target-AugmentCore-****************************************************************--TestHostTaskProducer>", "<target-AugmentCore-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-AugmentCore-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-AugmentCore-****************************************************************--DocumentationTaskProducer>", "<target-AugmentCore-****************************************************************--CustomTaskProducer>", "<target-AugmentCore-****************************************************************--StubBinaryTaskProducer>", "<target-AugmentCore-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--ProductStructureTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources>", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current"], "outputs": ["<target-AugmentCore-****************************************************************--ProductStructureTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--RealityAssetsTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--HeadermapTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--RealityAssetsTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--SanitizerTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--SanitizerTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--StubBinaryTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--StubBinaryTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--SwiftABIBaselineGenerationTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--SwiftABIBaselineGenerationTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--SwiftFrameworkABICheckerTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--SwiftFrameworkABICheckerTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--SwiftPackageCopyFilesTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--SwiftPackageCopyFilesTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--SwiftStandardLibrariesTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--SwiftStandardLibrariesTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--TAPISymbolExtractorTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--TAPISymbolExtractorTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--TestHostTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--TestHostTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--TestTargetPostprocessingTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--TestTargetPostprocessingTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--TestTargetTaskProducer": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--TestTargetTaskProducer>"]}, "P0:::Gate target-AugmentCore-****************************************************************--copy-headers-completion": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--copy-headers-completion>"]}, "P0:::Gate target-AugmentCore-****************************************************************--fused-phase0-copy-headers": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>"]}, "P0:::Gate target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json"], "outputs": ["<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>"]}, "P0:::Gate target-AugmentCore-****************************************************************--generated-headers": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--generated-headers>"]}, "P0:::Gate target-AugmentCore-****************************************************************--swift-generated-headers": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h"], "outputs": ["<target-AugmentCore-****************************************************************--swift-generated-headers>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:CodeSign /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A": {"tool": "code-sign-task", "description": "CodeSign /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift/", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift/", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.docc/", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift/", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist/", "<target-AugmentCore-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--entry>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore normal arm64\\ x86_64>", "<TRIGGER: MkDir /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/_CodeSignature", "<CodeSign /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:ExtractAppIntentsMetadata": {"tool": "shell", "description": "ExtractAppIntentsMetadata", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Metadata.appintents>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/appintentsmetadataprocessor", "--toolchain-dir", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain", "--module-name", "AugmentCore", "--sdk-root", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "--xcode-version", "16F6", "--platform-family", "macOS", "--deployment-target", "15.5", "--bundle-identifier", "a.augument-core", "--output", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources", "--target-triple", "arm64-apple-macos15.5", "--target-triple", "x86_64-apple-macos15.5", "--binary-file", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "--dependency-file", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "--dependency-file", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "--stringsdata-file", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ExtractedAppShortcutsMetadata.stringsdata", "--stringsdata-file", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ExtractedAppShortcutsMetadata.stringsdata", "--source-file-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "--source-file-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "--metadata-file-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "--static-metadata-file-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "--swift-const-vals-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "--swift-const-vals-list", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "--compile-time-extraction", "--deployment-aware-processing", "--validate-assistant-intents", "--no-app-shortcuts-localization"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "signature": "cc62609d07a03b93d7758bf83fa02d47"}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--begin-compiling": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-AugmentCore-****************************************************************--begin-compiling>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--begin-linking": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-AugmentCore-****************************************************************--begin-linking>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--begin-scanning": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--begin-scanning>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--end": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--entry>", "<CodeSign /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Metadata.appintents>", "<Eager Linking TBD Production /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources>", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist", "<RegisterExecutionPolicyException /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current", "<Touch /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist", "<target-AugmentCore-****************************************************************--AppIntentsMetadataTaskProducer>", "<target-AugmentCore-****************************************************************--Barrier-ChangeAlternatePermissions>", "<target-AugmentCore-****************************************************************--Barrier-ChangePermissions>", "<target-AugmentCore-****************************************************************--Barrier-CodeSign>", "<target-AugmentCore-****************************************************************--Barrier-CopyAside>", "<target-AugmentCore-****************************************************************--Barrier-GenerateStubAPI>", "<target-AugmentCore-****************************************************************--Barrier-RegisterExecutionPolicyException>", "<target-AugmentCore-****************************************************************--Barrier-RegisterProduct>", "<target-AugmentCore-****************************************************************--Barrier-StripSymbols>", "<target-AugmentCore-****************************************************************--Barrier-Validate>", "<target-AugmentCore-****************************************************************--CopySwiftPackageResourcesTaskProducer>", "<target-AugmentCore-****************************************************************--CustomTaskProducer>", "<target-AugmentCore-****************************************************************--DocumentationTaskProducer>", "<target-AugmentCore-****************************************************************--GenerateAppPlaygroundAssetCatalogTaskProducer>", "<target-AugmentCore-****************************************************************--GeneratedFilesTaskProducer>", "<target-AugmentCore-****************************************************************--HeadermapTaskProducer>", "<target-AugmentCore-****************************************************************--InfoPlistTaskProducer>", "<target-AugmentCore-****************************************************************--ModuleMapTaskProducer>", "<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AugmentCore-****************************************************************--ProductStructureTaskProducer>", "<target-AugmentCore-****************************************************************--RealityAssetsTaskProducer>", "<target-AugmentCore-****************************************************************--SanitizerTaskProducer>", "<target-AugmentCore-****************************************************************--StubBinaryTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftABIBaselineGenerationTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftFrameworkABICheckerTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftPackageCopyFilesTaskProducer>", "<target-AugmentCore-****************************************************************--SwiftStandardLibrariesTaskProducer>", "<target-AugmentCore-****************************************************************--TAPISymbolExtractorTaskProducer>", "<target-AugmentCore-****************************************************************--TestHostTaskProducer>", "<target-AugmentCore-****************************************************************--TestTargetPostprocessingTaskProducer>", "<target-AugmentCore-****************************************************************--TestTargetTaskProducer>", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--fused-phase1-compile-sources&link-binary&copy-bundle-resources>", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>"], "outputs": ["<target-AugmentCore-****************************************************************--end>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--entry": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>", "<target-AugmentCore-****************************************************************--begin-compiling>"], "outputs": ["<target-AugmentCore-****************************************************************--entry>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--immediate": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************-Debug-macosx--arm64-x86_64-build-headers-stale-file-removal>", "<CreateBuildDirectory-/tmp/Augment.dst>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/Debug>", "<CreateBuildDirectory-/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug>"], "outputs": ["<target-AugmentCore-****************************************************************--immediate>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--linker-inputs-ready": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>", "<Linked Binary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList"], "outputs": ["<target-AugmentCore-****************************************************************--linker-inputs-ready>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--modules-ready": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h"], "outputs": ["<target-AugmentCore-****************************************************************--modules-ready>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--unsigned-product-ready": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--begin-compiling>", "<ExtractAppIntentsMetadata /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Metadata.appintents>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule", "<Linked Binary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "<target-AugmentCore-****************************************************************--Barrier-GenerateStubAPI>"], "outputs": ["<target-AugmentCore-****************************************************************--unsigned-product-ready>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:Gate target-AugmentCore-****************************************************************--will-sign": {"tool": "phony", "inputs": ["<target-AugmentCore-****************************************************************--unsigned-product-ready>"], "outputs": ["<target-AugmentCore-****************************************************************--will-sign>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:GenerateTAPI /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd": {"tool": "shell", "description": "GenerateTAPI /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "<target-AugmentCore-****************************************************************--ProductPostprocessingTaskProducer>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "<Eager Linking TBD Production /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/tapi", "stubify", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "-L/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "-o", "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "control-enabled": false, "signature": "dbf34070386a3517dc8947f32a1a7171"}, "P0:target-AugmentCore-****************************************************************-:Debug:MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A>", "<TRIGGER: MkDir /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources": {"tool": "mkdir", "description": "MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources", "<MkDir /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:ProcessInfoPlistFile /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist": {"tool": "info-plist-processor", "description": "ProcessInfoPlistFile /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist", "<target-AugmentCore-****************************************************************--ModuleVerifierTaskProducer>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Resources/Info.plist"]}, "P0:target-AugmentCore-****************************************************************-:Debug:RegisterExecutionPolicyException /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework": {"tool": "register-execution-policy-exception", "description": "RegisterExecutionPolicyException /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "<target-AugmentCore-****************************************************************--Barrier-CodeSign>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["<RegisterExecutionPolicyException /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>"]}, "P0:target-AugmentCore-****************************************************************-:Debug:ScanDependencies /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan"]}, "P0:target-AugmentCore-****************************************************************-:Debug:ScanDependencies /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "clang-scan-modules", "description": "ScanDependencies /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan"]}, "P0:target-AugmentCore-****************************************************************-:Debug:SwiftDriver Compilation AugmentCore normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation AugmentCore normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.swiftconstvalues"]}, "P0:target-AugmentCore-****************************************************************-:Debug:SwiftDriver Compilation AugmentCore normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation", "description": "SwiftDriver Compilation AugmentCore normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.stringsdata", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.swiftconstvalues", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.swiftconstvalues"]}, "P0:target-AugmentCore-****************************************************************-:Debug:SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore Versions/Current/AugmentCore": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore Versions/Current/AugmentCore", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/AugmentCore"], "contents": "Versions/Current/AugmentCore"}, "P0:target-AugmentCore-****************************************************************-:Debug:SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules Versions/Current/Modules": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules Versions/Current/Modules", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Modules"], "contents": "Versions/Current/Modules"}, "P0:target-AugmentCore-****************************************************************-:Debug:SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources Versions/Current/Resources": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources Versions/Current/Resources", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Resources"], "contents": "Versions/Current/Resources"}, "P0:target-AugmentCore-****************************************************************-:Debug:SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current A": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current A", "inputs": ["<target-AugmentCore-****************************************************************--start>", "<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/Current"], "contents": "A"}, "P0:target-AugmentCore-****************************************************************-:Debug:Touch /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework": {"tool": "shell", "description": "Touch /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework", "<target-AugmentCore-****************************************************************--Barrier-Validate>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["<Touch /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework>"], "args": ["/usr/bin/touch", "-c", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "signature": "d48964048f8e57a006df2a6283f431e5"}, "P1:target-AugmentCore-****************************************************************-:Debug:CompileC /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o.scan", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o"]}, "P1:target-AugmentCore-****************************************************************-:Debug:CompileC /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool": "ccompile", "description": "CompileC /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c normal x86_64 c com.apple.compilers.llvm.clang.1_0.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o.scan", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o"]}, "P2:::WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/Augment-704ba1199aef8dfbc6a6d14ce4d99537-VFS/all-product-headers.yaml"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.abi.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.private.swiftinterface"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftdoc"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftinterface"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/arm64-apple-macos.swiftmodule"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.abi.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.private.swiftinterface"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftdoc"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftinterface"]}, "P2:target-AugmentCore-****************************************************************-:Debug:Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule": {"tool": "file-copy", "description": "Copy /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule/", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/Modules/AugmentCore.swiftmodule/x86_64-apple-macos.swiftmodule"]}, "P2:target-AugmentCore-****************************************************************-:Debug:CreateUniversalBinary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore normal arm64 x86_64": {"tool": "shell", "description": "CreateUniversalBinary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore normal arm64 x86_64", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore", "<Linked Binary /Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore>", "<TRIGGER: CreateUniversalBinary /Users/<USER>/Downloads/AugmentApp\\ 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore normal arm64\\ x86_64>"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo", "-create", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "-output", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug/AugmentCore.framework/Versions/A/AugmentCore"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "signature": "e332e608aa1a5a5addcc3517e73c8258"}, "P2:target-AugmentCore-****************************************************************-:Debug:Ld /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore normal arm64": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore normal arm64", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "arm64-apple-macos15.5", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "-F/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "-filelist", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "-install_name", "@rpath/AugmentCore.framework/Versions/A/AugmentCore", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@loader_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-compatibility_version", "1", "-current_version", "1", "-o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/Binary/AugmentCore"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "deps": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_dependency_info.dat"], "deps-style": "dependency-info", "signature": "49c2bb72ee70ffd065e9eadb02f9c3d0"}, "P2:target-AugmentCore-****************************************************************-:Debug:Ld /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore normal x86_64": {"tool": "shell", "description": "Ld /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore normal x86_64", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_vers.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SearchEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileItem.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NotificationManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DependencyContainer.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentLogger.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Protocols.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentConfiguration.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorRecoveryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreferencesManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/StorageManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PerformanceMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/NetworkSync.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentSpace.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileType.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SnapshotManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/VersionControl.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ConflictResolution.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/augument_core-4cb94a79c56dfc68ca47c27465a00ab4.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/PreviewEngine.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackupManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/FileSystemMonitor.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/DataEncryption.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentError.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/ErrorHandling.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/SecurityAuditReporter.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/LargeFileHandler.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryManager.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/MemoryOptimizationService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/BackgroundProcessingService.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "<target-AugmentCore-****************************************************************--generated-headers>", "<target-AugmentCore-****************************************************************--swift-generated-headers>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-linking>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat"], "args": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang", "-<PERSON><PERSON><PERSON>", "-reproducible", "-target", "x86_64-apple-macos15.5", "-dynamiclib", "-is<PERSON><PERSON>", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-O0", "-L/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "-L/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "-F/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug", "-F/Users/<USER>/Downloads/AugmentApp 2/build/Debug", "-filelist", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "-install_name", "@rpath/AugmentCore.framework/Versions/A/AugmentCore", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@executable_path/../Frameworks", "-<PERSON><PERSON><PERSON>", "-rpath", "-<PERSON><PERSON><PERSON>", "@loader_path/Frameworks", "-dead_strip", "-<PERSON><PERSON><PERSON>", "-object_path_lto", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_lto.o", "-rdynamic", "-<PERSON><PERSON><PERSON>", "-no_deduplicate", "-<PERSON><PERSON><PERSON>", "-dependency_info", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat", "-fobjc-link-runtime", "-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/macosx", "-L/usr/lib/swift", "-<PERSON><PERSON><PERSON>", "-add_ast_path", "-<PERSON><PERSON><PERSON>", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "-<PERSON><PERSON><PERSON>", "-no_adhoc_codesign", "-compatibility_version", "1", "-current_version", "1", "-o", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/Binary/AugmentCore"], "env": {}, "working-directory": "/Users/<USER>/Downloads/AugmentApp 2", "deps": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_dependency_info.dat"], "deps-style": "dependency-info", "signature": "fe8d14cfad0a674f450d0bc3f18dabcc"}, "P2:target-AugmentCore-****************************************************************-:Debug:SwiftDriver Compilation Requirements AugmentCore normal arm64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements AugmentCore normal arm64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.swiftdoc"]}, "P2:target-AugmentCore-****************************************************************-:Debug:SwiftDriver Compilation Requirements AugmentCore normal x86_64 com.apple.xcode.tools.swift.compiler": {"tool": "swift-driver-compilation-requirement", "description": "SwiftDriver Compilation Requirements AugmentCore normal x86_64 com.apple.xcode.tools.swift.compiler", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SearchEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileItem.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NotificationManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DependencyContainer.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentLogger.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/Protocols.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentConfiguration.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorRecoveryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreferencesManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/StorageManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PerformanceMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/NetworkSync.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentSpace.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileType.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SnapshotManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/VersionControl.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ConflictResolution.swift", "/Users/<USER>/Downloads/AugmentApp 2/augument core/augument_core.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/PreviewEngine.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackupManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/FileSystemMonitor.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/DataEncryption.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/AugmentError.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/ErrorHandling.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/SecurityAuditReporter.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/LargeFileHandler.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryManager.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/MemoryOptimizationService.swift", "/Users/<USER>/Downloads/AugmentApp 2/AugmentCore/BackgroundProcessingService.swift", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "<ClangStatCache /var/folders/kc/yzwmhd9d17z38w5clfhddk5c0000gn/C/com.apple.DeveloperTools/16.4-16F6/Xcode/SDKStatCaches.noindex/macosx15.5-24F74-8254517eb8b97d462ccbc072ba9094c9.sdkstatcache>", "<target-AugmentCore-****************************************************************--copy-headers-completion>", "<target-AugmentCore-****************************************************************--fused-phase0-copy-headers>", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore Swift Compilation Requirements Finished", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftmodule", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftsourceinfo", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.abi.json", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.private.swiftinterface", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.swiftdoc"]}, "P2:target-AugmentCore-****************************************************************-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h": {"tool": "swift-header-tool", "description": "SwiftMergeGeneratedHeaders /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "inputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-Swift.h", "/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-Swift.h", "<target-AugmentCore-****************************************************************--begin-compiling>", "<WorkspaceHeaderMapVFSFilesWritten>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore-Swift.h"]}, "P2:target-AugmentCore-****************************************************************-:Debug:SymLink /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd": {"tool": "symlink", "description": "SymLink /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd /Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "inputs": ["<target-AugmentCore-****************************************************************--Barrier-Validate>", "<target-AugmentCore-****************************************************************--will-sign>", "<target-AugmentCore-****************************************************************--entry>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/AugmentCore.tbd"], "contents": "/Users/<USER>/Downloads/AugmentApp 2/build/EagerLinkingTBDs/Debug/AugmentCore.framework/Versions/A/AugmentCore.tbd", "repair-via-ownership-analysis": true}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-non-framework-target-headers.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyMetadataFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.DependencyStaticMetadataFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore.hmap"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/AugmentCore_vers.c"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore-OutputFileMap.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.LinkFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftConstValuesFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore.SwiftFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/arm64/AugmentCore_const_extract_protocols.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/7187679823f38a2a940e0043cdf9d637-common-args.resp"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore-OutputFileMap.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.LinkFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftConstValuesFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore.SwiftFileList"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/Objects-normal/x86_64/AugmentCore_const_extract_protocols.json"]}, "P2:target-AugmentCore-****************************************************************-:Debug:WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist": {"tool": "auxiliary-file", "description": "WriteAuxiliaryFile /Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist", "inputs": ["<target-AugmentCore-****************************************************************--immediate>"], "outputs": ["/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/empty-AugmentCore.plist"]}}}