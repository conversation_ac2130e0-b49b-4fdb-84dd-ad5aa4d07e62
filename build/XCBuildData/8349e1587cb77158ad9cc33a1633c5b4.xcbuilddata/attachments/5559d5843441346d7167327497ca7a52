-target x86_64-apple-macos15.5 '-std=gnu17' -fmodules -gmodules -fpascal-strings -O0 -fno-common '-DDEBUG=1' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk -fasm-blocks -g -iquote '/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-generated-files.hmap' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-own-target-headers.hmap' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-all-target-headers.hmap' -iquote '/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/AugmentCore-project-headers.hmap' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Debug/include' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources-normal/x86_64' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources/x86_64' '-I/Users/<USER>/Downloads/AugmentApp 2/build/Augment.build/Debug/AugmentCore.build/DerivedSources' '-F/Users/<USER>/Downloads/AugmentApp 2/build/Debug'