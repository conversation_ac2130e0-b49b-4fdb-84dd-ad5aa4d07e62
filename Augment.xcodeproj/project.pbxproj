// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		015528942E0C9F9D00B8636F /* AugmentCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 015528892E0C9F9C00B8636F /* AugmentCore.framework */; };
		0155289E2E0C9F9D00B8636F /* AugmentCore.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 015528892E0C9F9C00B8636F /* AugmentCore.framework */; };
		0155289F2E0C9F9D00B8636F /* AugmentCore.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 015528892E0C9F9C00B8636F /* AugmentCore.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		015528A72E0CA1B600B8636F /* VersionControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E12DFB077300D661A9 /* VersionControl.swift */; };
		015528A82E0CA1C400B8636F /* SearchEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DF2DFB077300D661A9 /* SearchEngine.swift */; };
		015528A92E0CA1CD00B8636F /* FileType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36F52DFB0ECB00D661A9 /* FileType.swift */; };
		015528AA2E0CA1D200B8636F /* AugmentSpace.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */; };
		015528AB2E0CA1D800B8636F /* FileItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36FA2DFB0ECB00D661A9 /* FileItem.swift */; };
		015528AC2E0CA1DE00B8636F /* BackupManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36D92DFB077300D661A9 /* BackupManager.swift */; };
		015528AD2E0CA1E200B8636F /* ConflictResolution.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DA2DFB077300D661A9 /* ConflictResolution.swift */; };
		015528AE2E0CA1E700B8636F /* FileSystemMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */; };
		015528AF2E0CA1EF00B8636F /* SnapshotManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E02DFB077300D661A9 /* SnapshotManager.swift */; };
		015528B02E0CA1F500B8636F /* NetworkSync.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DC2DFB077300D661A9 /* NetworkSync.swift */; };
		015528B12E0CA1FB00B8636F /* PreviewEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36DD2DFB077300D661A9 /* PreviewEngine.swift */; };
		015528BC2E0CA6DA00B8636F /* NotificationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B72E0CA6DA00B8636F /* NotificationManager.swift */; };
		015528BD2E0CA6DA00B8636F /* DependencyContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B52E0CA6DA00B8636F /* DependencyContainer.swift */; };
		015528BE2E0CA6DA00B8636F /* AugmentLogger.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B42E0CA6DA00B8636F /* AugmentLogger.swift */; };
		015528BF2E0CA6DA00B8636F /* Protocols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528BA2E0CA6DA00B8636F /* Protocols.swift */; };
		015528C02E0CA6DA00B8636F /* AugmentConfiguration.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B32E0CA6DA00B8636F /* AugmentConfiguration.swift */; };
		015528C12E0CA6DA00B8636F /* ErrorRecoveryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B62E0CA6DA00B8636F /* ErrorRecoveryManager.swift */; };
		015528C22E0CA6DA00B8636F /* PreferencesManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B92E0CA6DA00B8636F /* PreferencesManager.swift */; };
		015528C32E0CA6DA00B8636F /* StorageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528BB2E0CA6DA00B8636F /* StorageManager.swift */; };
		015528C42E0CA6DA00B8636F /* PerformanceMonitor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 015528B82E0CA6DA00B8636F /* PerformanceMonitor.swift */; };
		017C36EA2DFB077300D661A9 /* FileOperationInterceptor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */; };
		017C36EB2DFB077300D661A9 /* AugmentFileSystem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */; };
		017C36EE2DFB077300D661A9 /* AugmentFUSE.swift in Sources */ = {isa = PBXBuildFile; fileRef = 017C36E42DFB077300D661A9 /* AugmentFUSE.swift */; };
		8A1234561234567800000001 /* AugmentApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000002 /* AugmentApp.swift */; };
		8A1234561234567800000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000004 /* ContentView.swift */; };
		8A1234561234567800000005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000006 /* Assets.xcassets */; };
		8A1234561234567800000007 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000008 /* Preview Assets.xcassets */; };
		8A123456123456780000002E /* SpaceDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002A /* SpaceDetailView.swift */; };
		8A123456123456780000002F /* SearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002B /* SearchView.swift */; };
		8A1234561234567800000030 /* ConflictResolutionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002C /* ConflictResolutionView.swift */; };
		8A1234561234567800000031 /* VersionBrowser.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A123456123456780000002D /* VersionBrowser.swift */; };
		8A1234561234567800000036 /* SecurityManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000032 /* SecurityManager.swift */; };
		8A1234561234567800000037 /* DataEncryption.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000033 /* DataEncryption.swift */; };
		8A1234561234567800000038 /* AugmentError.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000034 /* AugmentError.swift */; };
		8A1234561234567800000039 /* ErrorHandling.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000035 /* ErrorHandling.swift */; };
		8A1234561234567800000041 /* SecurityAuditReporter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000040 /* SecurityAuditReporter.swift */; };
		8A1234561234567800000043 /* LargeFileHandler.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000042 /* LargeFileHandler.swift */; };
		8A1234561234567800000045 /* MemoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000044 /* MemoryManager.swift */; };
		8A1234561234567800000047 /* MemoryOptimizationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000046 /* MemoryOptimizationService.swift */; };
		8A1234561234567800000049 /* BackgroundProcessingService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8A1234561234567800000048 /* BackgroundProcessingService.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		015528952E0C9F9D00B8636F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8A1234561234567800000014 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 015528882E0C9F9C00B8636F;
			remoteInfo = "augument core";
		};
		015528972E0C9F9D00B8636F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8A1234561234567800000014 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8A1234561234567800000010;
			remoteInfo = Augment;
		};
		0155289C2E0C9F9D00B8636F /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8A1234561234567800000014 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 015528882E0C9F9C00B8636F;
			remoteInfo = "augument core";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		015528A02E0C9F9D00B8636F /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				0155289F2E0C9F9D00B8636F /* AugmentCore.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		015528892E0C9F9C00B8636F /* AugmentCore.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = AugmentCore.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		015528932E0C9F9D00B8636F /* augument coreTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "augument coreTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		015528B32E0CA6DA00B8636F /* AugmentConfiguration.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentConfiguration.swift; sourceTree = "<group>"; };
		015528B42E0CA6DA00B8636F /* AugmentLogger.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentLogger.swift; sourceTree = "<group>"; };
		015528B52E0CA6DA00B8636F /* DependencyContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DependencyContainer.swift; sourceTree = "<group>"; };
		015528B62E0CA6DA00B8636F /* ErrorRecoveryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorRecoveryManager.swift; sourceTree = "<group>"; };
		015528B72E0CA6DA00B8636F /* NotificationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationManager.swift; sourceTree = "<group>"; };
		015528B82E0CA6DA00B8636F /* PerformanceMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformanceMonitor.swift; sourceTree = "<group>"; };
		015528B92E0CA6DA00B8636F /* PreferencesManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreferencesManager.swift; sourceTree = "<group>"; };
		015528BA2E0CA6DA00B8636F /* Protocols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Protocols.swift; sourceTree = "<group>"; };
		015528BB2E0CA6DA00B8636F /* StorageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StorageManager.swift; sourceTree = "<group>"; };
		017C36D92DFB077300D661A9 /* BackupManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackupManager.swift; sourceTree = "<group>"; };
		017C36DA2DFB077300D661A9 /* ConflictResolution.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConflictResolution.swift; sourceTree = "<group>"; };
		017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileSystemMonitor.swift; sourceTree = "<group>"; };
		017C36DC2DFB077300D661A9 /* NetworkSync.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkSync.swift; sourceTree = "<group>"; };
		017C36DD2DFB077300D661A9 /* PreviewEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PreviewEngine.swift; sourceTree = "<group>"; };
		017C36DF2DFB077300D661A9 /* SearchEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchEngine.swift; sourceTree = "<group>"; };
		017C36E02DFB077300D661A9 /* SnapshotManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SnapshotManager.swift; sourceTree = "<group>"; };
		017C36E12DFB077300D661A9 /* VersionControl.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionControl.swift; sourceTree = "<group>"; };
		017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentFileSystem.swift; sourceTree = "<group>"; };
		017C36E42DFB077300D661A9 /* AugmentFUSE.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentFUSE.swift; sourceTree = "<group>"; };
		017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileOperationInterceptor.swift; sourceTree = "<group>"; };
		017C36F52DFB0ECB00D661A9 /* FileType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileType.swift; sourceTree = "<group>"; };
		017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentSpace.swift; sourceTree = "<group>"; };
		017C36FA2DFB0ECB00D661A9 /* FileItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileItem.swift; sourceTree = "<group>"; };
		8A1234561234567800000002 /* AugmentApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentApp.swift; sourceTree = "<group>"; };
		8A1234561234567800000004 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		8A1234561234567800000006 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8A1234561234567800000008 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		8A1234561234567800000009 /* Augment.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Augment.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8A123456123456780000000A /* Augment.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Augment.entitlements; sourceTree = "<group>"; };
		8A123456123456780000001F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8A123456123456780000002A /* SpaceDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SpaceDetailView.swift; sourceTree = "<group>"; };
		8A123456123456780000002B /* SearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchView.swift; sourceTree = "<group>"; };
		8A123456123456780000002C /* ConflictResolutionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConflictResolutionView.swift; sourceTree = "<group>"; };
		8A123456123456780000002D /* VersionBrowser.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VersionBrowser.swift; sourceTree = "<group>"; };
		8A1234561234567800000032 /* SecurityManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SecurityManager.swift; sourceTree = "<group>"; };
		8A1234561234567800000033 /* DataEncryption.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataEncryption.swift; sourceTree = "<group>"; };
		8A1234561234567800000034 /* AugmentError.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AugmentError.swift; sourceTree = "<group>"; };
		8A1234561234567800000035 /* ErrorHandling.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ErrorHandling.swift; sourceTree = "<group>"; };
		8A1234561234567800000040 /* SecurityAuditReporter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SecurityAuditReporter.swift; sourceTree = "<group>"; };
		8A1234561234567800000042 /* LargeFileHandler.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LargeFileHandler.swift; sourceTree = "<group>"; };
		8A1234561234567800000044 /* MemoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemoryManager.swift; sourceTree = "<group>"; };
		8A1234561234567800000046 /* MemoryOptimizationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemoryOptimizationService.swift; sourceTree = "<group>"; };
		8A1234561234567800000048 /* BackgroundProcessingService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BackgroundProcessingService.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		0155288A2E0C9F9C00B8636F /* augument core */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = "augument core"; sourceTree = "<group>"; };
		015528992E0C9F9D00B8636F /* augument coreTests */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = "augument coreTests"; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		015528862E0C9F9C00B8636F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		015528902E0C9F9D00B8636F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				015528942E0C9F9D00B8636F /* AugmentCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8A123456123456780000000B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0155289E2E0C9F9D00B8636F /* AugmentCore.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		017C36E22DFB077300D661A9 /* AugmentCore */ = {
			isa = PBXGroup;
			children = (
				015528B32E0CA6DA00B8636F /* AugmentConfiguration.swift */,
				015528B42E0CA6DA00B8636F /* AugmentLogger.swift */,
				015528B52E0CA6DA00B8636F /* DependencyContainer.swift */,
				015528B62E0CA6DA00B8636F /* ErrorRecoveryManager.swift */,
				015528B72E0CA6DA00B8636F /* NotificationManager.swift */,
				015528B82E0CA6DA00B8636F /* PerformanceMonitor.swift */,
				015528B92E0CA6DA00B8636F /* PreferencesManager.swift */,
				015528BA2E0CA6DA00B8636F /* Protocols.swift */,
				015528BB2E0CA6DA00B8636F /* StorageManager.swift */,
				017C36F52DFB0ECB00D661A9 /* FileType.swift */,
				017C36F82DFB0ECB00D661A9 /* AugmentSpace.swift */,
				017C36FA2DFB0ECB00D661A9 /* FileItem.swift */,
				017C36D92DFB077300D661A9 /* BackupManager.swift */,
				017C36DA2DFB077300D661A9 /* ConflictResolution.swift */,
				017C36DB2DFB077300D661A9 /* FileSystemMonitor.swift */,
				017C36E02DFB077300D661A9 /* SnapshotManager.swift */,
				017C36E12DFB077300D661A9 /* VersionControl.swift */,
				017C36DC2DFB077300D661A9 /* NetworkSync.swift */,
				017C36DD2DFB077300D661A9 /* PreviewEngine.swift */,
				017C36DF2DFB077300D661A9 /* SearchEngine.swift */,
				8A1234561234567800000032 /* SecurityManager.swift */,
				8A1234561234567800000033 /* DataEncryption.swift */,
				8A1234561234567800000034 /* AugmentError.swift */,
				8A1234561234567800000035 /* ErrorHandling.swift */,
				8A1234561234567800000040 /* SecurityAuditReporter.swift */,
				8A1234561234567800000042 /* LargeFileHandler.swift */,
				8A1234561234567800000044 /* MemoryManager.swift */,
				8A1234561234567800000046 /* MemoryOptimizationService.swift */,
				8A1234561234567800000048 /* BackgroundProcessingService.swift */,
			);
			path = AugmentCore;
			sourceTree = "<group>";
		};
		017C36E72DFB077300D661A9 /* AugmentFileSystem */ = {
			isa = PBXGroup;
			children = (
				017C36E32DFB077300D661A9 /* AugmentFileSystem.swift */,
				017C36E42DFB077300D661A9 /* AugmentFUSE.swift */,
				017C36E52DFB077300D661A9 /* FileOperationInterceptor.swift */,
			);
			path = AugmentFileSystem;
			sourceTree = "<group>";
		};
		8A123456123456780000000C /* Products */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000009 /* Augment.app */,
				015528892E0C9F9C00B8636F /* AugmentCore.framework */,
				015528932E0C9F9D00B8636F /* augument coreTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8A123456123456780000000D /* Augment */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000002 /* AugmentApp.swift */,
				8A1234561234567800000004 /* ContentView.swift */,
				8A123456123456780000002A /* SpaceDetailView.swift */,
				8A123456123456780000002B /* SearchView.swift */,
				8A123456123456780000002C /* ConflictResolutionView.swift */,
				8A123456123456780000002D /* VersionBrowser.swift */,
				8A1234561234567800000006 /* Assets.xcassets */,
				8A123456123456780000000A /* Augment.entitlements */,
				8A123456123456780000001F /* Info.plist */,
				8A123456123456780000000E /* Preview Content */,
			);
			path = Augment;
			sourceTree = "<group>";
		};
		8A123456123456780000000E /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				8A1234561234567800000008 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		8A123456123456780000000F = {
			isa = PBXGroup;
			children = (
				017C36E22DFB077300D661A9 /* AugmentCore */,
				017C36E72DFB077300D661A9 /* AugmentFileSystem */,
				8A123456123456780000000D /* Augment */,
				0155288A2E0C9F9C00B8636F /* augument core */,
				015528992E0C9F9D00B8636F /* augument coreTests */,
				8A123456123456780000000C /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		015528842E0C9F9C00B8636F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		015528882E0C9F9C00B8636F /* AugmentCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 015528A52E0C9F9D00B8636F /* Build configuration list for PBXNativeTarget "AugmentCore" */;
			buildPhases = (
				015528842E0C9F9C00B8636F /* Headers */,
				015528852E0C9F9C00B8636F /* Sources */,
				015528862E0C9F9C00B8636F /* Frameworks */,
				015528872E0C9F9C00B8636F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				0155288A2E0C9F9C00B8636F /* augument core */,
			);
			name = AugmentCore;
			packageProductDependencies = (
			);
			productName = "augument core";
			productReference = 015528892E0C9F9C00B8636F /* AugmentCore.framework */;
			productType = "com.apple.product-type.framework";
		};
		015528922E0C9F9D00B8636F /* augument coreTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 015528A62E0C9F9D00B8636F /* Build configuration list for PBXNativeTarget "augument coreTests" */;
			buildPhases = (
				0155288F2E0C9F9D00B8636F /* Sources */,
				015528902E0C9F9D00B8636F /* Frameworks */,
				015528912E0C9F9D00B8636F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				015528962E0C9F9D00B8636F /* PBXTargetDependency */,
				015528982E0C9F9D00B8636F /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				015528992E0C9F9D00B8636F /* augument coreTests */,
			);
			name = "augument coreTests";
			packageProductDependencies = (
			);
			productName = "augument coreTests";
			productReference = 015528932E0C9F9D00B8636F /* augument coreTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8A1234561234567800000010 /* Augment */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8A1234561234567800000011 /* Build configuration list for PBXNativeTarget "Augment" */;
			buildPhases = (
				8A1234561234567800000012 /* Sources */,
				8A123456123456780000000B /* Frameworks */,
				8A1234561234567800000013 /* Resources */,
				015528A02E0C9F9D00B8636F /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				0155289D2E0C9F9D00B8636F /* PBXTargetDependency */,
			);
			name = Augment;
			productName = Augment;
			productReference = 8A1234561234567800000009 /* Augment.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8A1234561234567800000014 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					015528882E0C9F9C00B8636F = {
						CreatedOnToolsVersion = 16.4;
					};
					015528922E0C9F9D00B8636F = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 8A1234561234567800000010;
					};
					8A1234561234567800000010 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 8A1234561234567800000015 /* Build configuration list for PBXProject "Augment" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8A123456123456780000000F;
			productRefGroup = 8A123456123456780000000C /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8A1234561234567800000010 /* Augment */,
				015528882E0C9F9C00B8636F /* AugmentCore */,
				015528922E0C9F9D00B8636F /* augument coreTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		015528872E0C9F9C00B8636F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		015528912E0C9F9D00B8636F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8A1234561234567800000013 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A1234561234567800000007 /* Preview Assets.xcassets in Resources */,
				8A1234561234567800000005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		015528852E0C9F9C00B8636F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				015528A82E0CA1C400B8636F /* SearchEngine.swift in Sources */,
				015528AB2E0CA1D800B8636F /* FileItem.swift in Sources */,
				015528BC2E0CA6DA00B8636F /* NotificationManager.swift in Sources */,
				015528BD2E0CA6DA00B8636F /* DependencyContainer.swift in Sources */,
				015528BE2E0CA6DA00B8636F /* AugmentLogger.swift in Sources */,
				015528BF2E0CA6DA00B8636F /* Protocols.swift in Sources */,
				015528C02E0CA6DA00B8636F /* AugmentConfiguration.swift in Sources */,
				015528C12E0CA6DA00B8636F /* ErrorRecoveryManager.swift in Sources */,
				015528C22E0CA6DA00B8636F /* PreferencesManager.swift in Sources */,
				015528C32E0CA6DA00B8636F /* StorageManager.swift in Sources */,
				015528C42E0CA6DA00B8636F /* PerformanceMonitor.swift in Sources */,
				015528B02E0CA1F500B8636F /* NetworkSync.swift in Sources */,
				015528AA2E0CA1D200B8636F /* AugmentSpace.swift in Sources */,
				015528A92E0CA1CD00B8636F /* FileType.swift in Sources */,
				015528AF2E0CA1EF00B8636F /* SnapshotManager.swift in Sources */,
				015528A72E0CA1B600B8636F /* VersionControl.swift in Sources */,
				015528AD2E0CA1E200B8636F /* ConflictResolution.swift in Sources */,
				015528B12E0CA1FB00B8636F /* PreviewEngine.swift in Sources */,
				015528AC2E0CA1DE00B8636F /* BackupManager.swift in Sources */,
				015528AE2E0CA1E700B8636F /* FileSystemMonitor.swift in Sources */,
				8A1234561234567800000036 /* SecurityManager.swift in Sources */,
				8A1234561234567800000037 /* DataEncryption.swift in Sources */,
				8A1234561234567800000038 /* AugmentError.swift in Sources */,
				8A1234561234567800000039 /* ErrorHandling.swift in Sources */,
				8A1234561234567800000041 /* SecurityAuditReporter.swift in Sources */,
				8A1234561234567800000043 /* LargeFileHandler.swift in Sources */,
				8A1234561234567800000045 /* MemoryManager.swift in Sources */,
				8A1234561234567800000047 /* MemoryOptimizationService.swift in Sources */,
				8A1234561234567800000049 /* BackgroundProcessingService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		0155288F2E0C9F9D00B8636F /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8A1234561234567800000012 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A1234561234567800000003 /* ContentView.swift in Sources */,
				8A1234561234567800000001 /* AugmentApp.swift in Sources */,
				8A123456123456780000002E /* SpaceDetailView.swift in Sources */,
				8A123456123456780000002F /* SearchView.swift in Sources */,
				8A1234561234567800000030 /* ConflictResolutionView.swift in Sources */,
				8A1234561234567800000031 /* VersionBrowser.swift in Sources */,
				017C36EA2DFB077300D661A9 /* FileOperationInterceptor.swift in Sources */,
				017C36EB2DFB077300D661A9 /* AugmentFileSystem.swift in Sources */,
				017C36EE2DFB077300D661A9 /* AugmentFUSE.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		015528962E0C9F9D00B8636F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 015528882E0C9F9C00B8636F /* AugmentCore */;
			targetProxy = 015528952E0C9F9D00B8636F /* PBXContainerItemProxy */;
		};
		015528982E0C9F9D00B8636F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8A1234561234567800000010 /* Augment */;
			targetProxy = 015528972E0C9F9D00B8636F /* PBXContainerItemProxy */;
		};
		0155289D2E0C9F9D00B8636F /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 015528882E0C9F9C00B8636F /* AugmentCore */;
			targetProxy = 0155289C2E0C9F9D00B8636F /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		015528A12E0C9F9D00B8636F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = "a.augument-core";
				PRODUCT_NAME = AugmentCore;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_MODULE = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		015528A22E0C9F9D00B8636F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				ENABLE_MODULE_VERIFIER = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				MODULE_VERIFIER_SUPPORTED_LANGUAGES = "objective-c objective-c++";
				MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS = "gnu17 gnu++20";
				PRODUCT_BUNDLE_IDENTIFIER = "a.augument-core";
				PRODUCT_NAME = AugmentCore;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_MODULE = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		015528A32E0C9F9D00B8636F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2TKF278BYV;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "a.augument-coreTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Augment.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Augment";
			};
			name = Debug;
		};
		015528A42E0C9F9D00B8636F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 2TKF278BYV;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "a.augument-coreTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Augment.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Augment";
			};
			name = Release;
		};
		8A1234561234567800000016 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 2TKF278BYV;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8A1234561234567800000017 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 2TKF278BYV;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 13.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8A1234561234567800000018 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Augment/Augment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Augment/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Augment/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.augment.Augment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8A1234561234567800000019 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Augment/Augment.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Augment/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				INFOPLIST_FILE = Augment/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.augment.Augment;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		015528A52E0C9F9D00B8636F /* Build configuration list for PBXNativeTarget "AugmentCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				015528A12E0C9F9D00B8636F /* Debug */,
				015528A22E0C9F9D00B8636F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		015528A62E0C9F9D00B8636F /* Build configuration list for PBXNativeTarget "augument coreTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				015528A32E0C9F9D00B8636F /* Debug */,
				015528A42E0C9F9D00B8636F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8A1234561234567800000011 /* Build configuration list for PBXNativeTarget "Augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1234561234567800000018 /* Debug */,
				8A1234561234567800000019 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8A1234561234567800000015 /* Build configuration list for PBXProject "Augment" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1234561234567800000016 /* Debug */,
				8A1234561234567800000017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8A1234561234567800000014 /* Project object */;
}
